# Tunami - AI Music Streaming Platform

A modern, AI-powered music streaming platform built with Next.js 14, TypeScript, and Supabase.

## 🚀 Features

- **AI-Powered Recommendations**: Personalized playlists and music discovery
- **Modern UI/UX**: Beautiful, responsive design with glass morphism effects
- **Real-time Audio**: High-quality streaming with spatial audio support
- **User Authentication**: Secure authentication with Supabase
- **Playlist Management**: Create, edit, and share playlists
- **Music Player**: Full-featured player with controls and visualizations
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom music-themed design system
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Icons**: Lucide React
- **Deployment**: Vercel (recommended)

## 🎨 Design System

The app features a custom color palette designed for music applications:

- **Primary**: Indigo/Blue tones (#6366f1)
- **Secondary**: Purple/Magenta tones (#d946ef)
- **Accent**: Green tones (#10b981)
- **Dark**: Slate tones for backgrounds

## 📦 Installation

1. **Clone the repository**:
   ```bash
   git clone <your-repo-url>
   cd tunami
   ```

2. **Install dependencies**:
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**:
   Copy `env.example` to `.env.local` and fill in your Supabase credentials:
   ```bash
   cp env.example .env.local
   ```

   Update the following variables:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

4. **Run the development server**:
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🗄️ Database Setup

The project includes TypeScript types for the following database tables:

- **profiles**: User profile information
- **songs**: Music track metadata
- **playlists**: User-created playlists

Create these tables in your Supabase dashboard or use the SQL schema provided in the `/database` folder (when available).

## 📁 Project Structure

```
src/
├── app/                    # Next.js 14 app directory
│   ├── globals.css        # Global styles and Tailwind directives
│   ├── layout.tsx         # Root layout component
│   └── page.tsx           # Homepage
├── components/            # React components
│   └── MusicPlayer.tsx    # Music player component
├── lib/                   # Utility functions and configurations
│   ├── supabase.ts       # Supabase client and types
│   └── utils.ts          # Common utility functions
```

## 🎵 Key Components

### MusicPlayer
A fully-featured music player component with:
- Play/pause controls
- Progress bar with seeking
- Volume control
- Song information display
- Like/unlike functionality

### Color System
Custom Tailwind color palette optimized for music apps:
- Primary colors for main UI elements
- Secondary colors for accents and highlights
- Gradient backgrounds for visual appeal
- Dark theme optimized for music listening

## 🔧 Configuration Files

- **tailwind.config.js**: Custom design system with music-themed colors
- **next.config.js**: Next.js configuration
- **tsconfig.json**: TypeScript configuration
- **postcss.config.js**: PostCSS configuration for Tailwind

## 🚀 Deployment

Deploy your app to Vercel:

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - Vercel will automatically build and deploy your app

Or deploy manually:

```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🎯 Next Steps

- [ ] Implement user authentication
- [ ] Add music upload functionality
- [ ] Create playlist management
- [ ] Implement AI recommendations
- [ ] Add social features
- [ ] Mobile app development
- [ ] Audio visualization enhancements

## 🐛 Troubleshooting

### Common Issues

1. **Supabase connection errors**: Check your environment variables
2. **Styling issues**: Ensure Tailwind CSS is properly configured
3. **TypeScript errors**: Check your type definitions

### Getting Help

- Check the [Next.js documentation](https://nextjs.org/docs)
- Review [Supabase documentation](https://supabase.com/docs)
- Visit [Tailwind CSS documentation](https://tailwindcss.com/docs)

---

Built with ❤️ by the Tunami team 