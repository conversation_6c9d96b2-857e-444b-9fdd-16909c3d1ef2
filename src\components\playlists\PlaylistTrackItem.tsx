'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { 
  Play, 
  Pause, 
  MoreVertical, 
  Trash2, 
  Heart,
  GripVertical,
  Music
} from 'lucide-react'
import { PlaylistTrack } from '@/types/playlist'
import { usePlaylist } from '@/contexts/PlaylistContext'
import { useAudio } from '@/contexts/AudioContext'
import { formatDuration } from '@/utils/format'

interface PlaylistTrackItemProps {
  playlistTrack: PlaylistTrack
  index: number
  isOwner: boolean
  isDragging?: boolean
  dragHandleProps?: any
  draggableProps?: any
  innerRef?: any
}

export default function PlaylistTrackItem({
  playlistTrack,
  index,
  isOwner,
  isDragging = false,
  dragHandleProps,
  draggableProps,
  innerRef
}: PlaylistTrackItemProps) {
  const { removeTrackFromPlaylist, loading } = usePlaylist()
  const { currentTrack, isPlaying: audioIsPlaying, playPlaylist, currentPlaylist } = useAudio()
  const [showMenu, setShowMenu] = useState(false)
  const [imageError, setImageError] = useState(false)

  const track = playlistTrack.track

  if (!track) {
    return null
  }

  // Check if this track is currently playing
  const isCurrentTrack = currentTrack?.id === track.id
  const isPlaying = isCurrentTrack && audioIsPlaying

  const handlePlay = () => {
    if (currentPlaylist && currentPlaylist.id === playlistTrack.playlist_id) {
      // If we're already playing this playlist, jump to this track
      const trackIndex = currentPlaylist.tracks.findIndex(pt => pt.track_id === track.id)
      if (trackIndex >= 0) {
        // This would need a jumpToTrack function in the audio context
        console.log('Jump to track at index:', trackIndex)
      }
    } else {
      // Load the entire playlist and start from this track
      // For now, we'll just play this single track
      console.log('Playing track:', track.title)
    }
  }

  const handleRemove = async () => {
    try {
      await removeTrackFromPlaylist(playlistTrack.playlist_id, playlistTrack.track_id)
      setShowMenu(false)
    } catch (error) {
      console.error('Failed to remove track:', error)
    }
  }

  const handleLike = () => {
    // TODO: Implement like functionality
    console.log('Liking track:', track.title)
  }

  return (
    <div
      ref={innerRef}
      {...draggableProps}
      className={`group px-6 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-b border-gray-100 dark:border-gray-700 last:border-b-0 ${
        isDragging ? 'bg-blue-50 dark:bg-blue-900/20 shadow-lg' : ''
      } ${
        isCurrentTrack ? 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800' : ''
      }`}
    >
      <div className="grid grid-cols-12 gap-4 items-center">
        {/* Position & Drag Handle */}
        <div className="col-span-1 flex items-center space-x-2">
          {isOwner && (
            <div
              {...dragHandleProps}
              className="opacity-0 group-hover:opacity-100 cursor-grab active:cursor-grabbing transition-opacity"
            >
              <GripVertical className="w-4 h-4 text-gray-400" />
            </div>
          )}
          <span className="text-sm text-gray-500 dark:text-gray-400 w-6 text-center">
            {index + 1}
          </span>
        </div>

        {/* Track Info */}
        <div className="col-span-6 flex items-center space-x-3">
          {/* Cover Image */}
          <div className="relative w-10 h-10 flex-shrink-0">
            <div className="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 rounded overflow-hidden">
              {track.cover_image_url && !imageError ? (
                <Image
                  src={track.cover_image_url}
                  alt={track.title}
                  width={40}
                  height={40}
                  className="object-cover"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Music className="w-4 h-4 text-gray-500" />
                </div>
              )}
            </div>

            {/* Play Button Overlay */}
            <button
              onClick={handlePlay}
              className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200"
            >
              {isPlaying ? (
                <Pause className="w-4 h-4 text-white" />
              ) : (
                <Play className="w-4 h-4 text-white ml-0.5" />
              )}
            </button>
          </div>

          {/* Track Details */}
          <div className="min-w-0 flex-1">
            <h4 className={`font-medium truncate ${
              isCurrentTrack 
                ? 'text-purple-600 dark:text-purple-400' 
                : 'text-gray-900 dark:text-white'
            }`}>
              {track.title}
              {isPlaying && (
                <span className="ml-2 text-xs">♪</span>
              )}
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {track.artist}
            </p>
          </div>
        </div>

        {/* Album */}
        <div className="col-span-3">
          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
            {track.album || 'Unknown Album'}
          </p>
        </div>

        {/* Duration & Actions */}
        <div className="col-span-2 flex items-center justify-end space-x-2">
          {/* Like Button */}
          <button
            onClick={handleLike}
            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-all"
          >
            <Heart className="w-4 h-4 text-gray-400 hover:text-red-500" />
          </button>

          {/* Duration */}
          <span className="text-sm text-gray-500 dark:text-gray-400 w-12 text-right">
            {formatDuration(track.duration || 0)}
          </span>

          {/* Menu */}
          {isOwner && (
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-all"
              >
                <MoreVertical className="w-4 h-4 text-gray-400" />
              </button>

              {showMenu && (
                <div className="absolute top-6 right-0 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 py-1 z-10 min-w-[140px]">
                  <button
                    onClick={handleRemove}
                    disabled={loading}
                    className="w-full px-3 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2 disabled:opacity-50"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Remove</span>
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  )
} 