// User Profile System Types
import { Profile } from './database'

export interface ExtendedProfile extends Profile {
  // Social links
  website_url?: string | null
  twitter_url?: string | null
  instagram_url?: string | null
  youtube_url?: string | null
  spotify_url?: string | null
  soundcloud_url?: string | null
  
  // Additional profile fields
  location?: string | null
  birth_date?: string | null
  phone?: string | null
  preferred_genres?: string[] | null
  preferred_moods?: string[] | null
  
  // Privacy settings
  profile_visibility: 'public' | 'private' | 'friends'
  show_email: boolean
  show_listening_history: boolean
  show_playlists: boolean
  
  // Notification preferences
  email_notifications: boolean
  push_notifications: boolean
  marketing_emails: boolean
}

export interface UserStats {
  tracks_uploaded: number
  playlists_created: number
  total_plays: number
  total_likes_received: number
  total_listening_time: number // in seconds
  followers_count: number
  following_count: number
  tracks_liked: number
  playlists_followed: number
}

export interface ProfileFormData {
  full_name: string
  username: string
  bio: string
  location: string
  website_url: string
  twitter_url: string
  instagram_url: string
  youtube_url: string
  spotify_url: string
  soundcloud_url: string
  preferred_genres: string[]
  preferred_moods: string[]
}

export interface PrivacySettings {
  profile_visibility: 'public' | 'private' | 'friends'
  show_email: boolean
  show_listening_history: boolean
  show_playlists: boolean
}

export interface NotificationSettings {
  email_notifications: boolean
  push_notifications: boolean
  marketing_emails: boolean
}

export interface AccountSettings {
  email: string
  password: string
  two_factor_enabled: boolean
  login_notifications: boolean
}

export interface SocialLink {
  platform: string
  url: string
  icon: string
  color: string
}

export interface ProfileUpload {
  file: File
  preview: string
} 