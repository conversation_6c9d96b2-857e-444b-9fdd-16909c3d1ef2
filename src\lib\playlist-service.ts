import { supabase } from './supabase'
import { 
  Playlist, 
  PlaylistWithTracks, 
  CreatePlaylistData, 
  UpdatePlaylistData, 
  AddTrackToPlaylistData,
  ReorderTrackData,
  PlaylistFilters,
  PlaylistStats
} from '@/types/playlist'

export class PlaylistService {
  // Create a new playlist
  static async createPlaylist(data: CreatePlaylistData): Promise<Playlist> {
    const { data: playlist, error } = await supabase
      .from('playlists')
      .insert([{
        name: data.name,
        description: data.description,
        is_public: data.is_public || false,
        cover_image_url: data.cover_image_url,
        user_id: (await supabase.auth.getUser()).data.user?.id
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create playlist: ${error.message}`)
    }

    return playlist
  }

  // Update an existing playlist
  static async updatePlaylist(id: string, data: UpdatePlaylistData): Promise<Playlist> {
    const { data: playlist, error } = await supabase
      .from('playlists')
      .update(data)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update playlist: ${error.message}`)
    }

    return playlist
  }

  // Delete a playlist
  static async deletePlaylist(id: string): Promise<void> {
    const { error } = await supabase
      .from('playlists')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete playlist: ${error.message}`)
    }
  }

  // Get a single playlist with tracks
  static async getPlaylist(id: string): Promise<PlaylistWithTracks> {
    const { data: playlist, error: playlistError } = await supabase
      .from('playlists')
      .select(`
        *,
        playlist_tracks (
          id,
          position,
          added_at,
          track:tracks (
            id,
            title,
            artist,
            album,
            duration,
            file_url,
            cover_image_url,
            genre,
            created_at
          )
        )
      `)
      .eq('id', id)
      .single()

    if (playlistError) {
      throw new Error(`Failed to fetch playlist: ${playlistError.message}`)
    }

    // Sort tracks by position
    const sortedTracks = playlist.playlist_tracks.sort((a: any, b: any) => a.position - b.position)

    return {
      ...playlist,
      tracks: sortedTracks
    }
  }

  // Get user's playlists
  static async getUserPlaylists(userId?: string): Promise<Playlist[]> {
    const currentUser = userId || (await supabase.auth.getUser()).data.user?.id

    if (!currentUser) {
      throw new Error('User not authenticated')
    }

    const { data: playlists, error } = await supabase
      .from('playlists')
      .select(`
        *,
        playlist_tracks (count)
      `)
      .eq('user_id', currentUser)
      .order('updated_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch user playlists: ${error.message}`)
    }

    return playlists.map(playlist => ({
      ...playlist,
      track_count: playlist.playlist_tracks?.[0]?.count || 0
    }))
  }

  // Get public playlists
  static async getPublicPlaylists(filters?: PlaylistFilters): Promise<Playlist[]> {
    let query = supabase
      .from('playlists')
      .select(`
        *,
        playlist_tracks (count)
      `)
      .eq('is_public', true)

    // Apply filters
    if (filters?.search) {
      query = query.ilike('name', `%${filters.search}%`)
    }

    if (filters?.user_id) {
      query = query.eq('user_id', filters.user_id)
    }

    // Apply sorting
    const sortBy = filters?.sort_by || 'updated_at'
    const sortOrder = filters?.sort_order === 'asc' ? { ascending: true } : { ascending: false }
    query = query.order(sortBy, sortOrder)

    const { data: playlists, error } = await query

    if (error) {
      throw new Error(`Failed to fetch public playlists: ${error.message}`)
    }

    return playlists.map(playlist => ({
      ...playlist,
      track_count: playlist.playlist_tracks?.[0]?.count || 0
    }))
  }

  // Add track to playlist
  static async addTrackToPlaylist(data: AddTrackToPlaylistData): Promise<void> {
    const { error } = await supabase
      .from('playlist_tracks')
      .insert([{
        playlist_id: data.playlist_id,
        track_id: data.track_id,
        position: data.position || 0 // Will be auto-set by trigger if 0
      }])

    if (error) {
      throw new Error(`Failed to add track to playlist: ${error.message}`)
    }
  }

  // Remove track from playlist
  static async removeTrackFromPlaylist(playlistId: string, trackId: string): Promise<void> {
    const { error } = await supabase
      .from('playlist_tracks')
      .delete()
      .eq('playlist_id', playlistId)
      .eq('track_id', trackId)

    if (error) {
      throw new Error(`Failed to remove track from playlist: ${error.message}`)
    }
  }

  // Reorder tracks in playlist
  static async reorderPlaylistTracks(playlistId: string, tracks: ReorderTrackData[]): Promise<void> {
    const updates = tracks.map(track => ({
      playlist_id: track.playlist_id,
      track_id: track.track_id,
      position: track.new_position
    }))

    const { error } = await supabase
      .from('playlist_tracks')
      .upsert(updates, { 
        onConflict: 'playlist_id,track_id',
        ignoreDuplicates: false 
      })

    if (error) {
      throw new Error(`Failed to reorder playlist tracks: ${error.message}`)
    }
  }

  // Get playlist statistics
  static async getPlaylistStats(playlistId: string): Promise<PlaylistStats> {
    const { data, error } = await supabase
      .from('playlist_tracks')
      .select(`
        track:tracks (duration),
        added_at
      `)
      .eq('playlist_id', playlistId)

    if (error) {
      throw new Error(`Failed to fetch playlist stats: ${error.message}`)
    }

    const trackCount = data.length
    const totalDuration = data.reduce((sum, item: any) => sum + (item.track?.duration || 0), 0)
    const lastUpdated = data.length > 0 
      ? Math.max(...data.map(item => new Date(item.added_at).getTime()))
      : Date.now()

    return {
      track_count: trackCount,
      total_duration: totalDuration,
      last_updated: new Date(lastUpdated).toISOString()
    }
  }

  // Check if track exists in playlist
  static async isTrackInPlaylist(playlistId: string, trackId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('playlist_tracks')
      .select('id')
      .eq('playlist_id', playlistId)
      .eq('track_id', trackId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      throw new Error(`Failed to check track in playlist: ${error.message}`)
    }

    return !!data
  }

  // Get playlists containing a specific track
  static async getPlaylistsWithTrack(trackId: string): Promise<Playlist[]> {
    const currentUser = (await supabase.auth.getUser()).data.user?.id

    if (!currentUser) {
      throw new Error('User not authenticated')
    }

    const { data: playlists, error } = await supabase
      .from('playlists')
      .select(`
        *,
        playlist_tracks!inner (track_id)
      `)
      .eq('user_id', currentUser)
      .eq('playlist_tracks.track_id', trackId)

    if (error) {
      throw new Error(`Failed to fetch playlists with track: ${error.message}`)
    }

    return playlists
  }

  // Duplicate a playlist
  static async duplicatePlaylist(playlistId: string, newName: string): Promise<Playlist> {
    const originalPlaylist = await this.getPlaylist(playlistId)
    
    // Create new playlist
    const newPlaylist = await this.createPlaylist({
      name: newName,
      description: originalPlaylist.description,
      is_public: false // Always create as private
    })

    // Add all tracks to new playlist
    if (originalPlaylist.tracks.length > 0) {
      const trackInserts = originalPlaylist.tracks.map((track, index) => ({
        playlist_id: newPlaylist.id,
        track_id: track.track_id,
        position: index + 1
      }))

      const { error } = await supabase
        .from('playlist_tracks')
        .insert(trackInserts)

      if (error) {
        // Clean up the created playlist if track insertion fails
        await this.deletePlaylist(newPlaylist.id)
        throw new Error(`Failed to duplicate playlist tracks: ${error.message}`)
      }
    }

    return newPlaylist
  }
} 