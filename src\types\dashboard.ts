// Dashboard Analytics Types
import { Track, Playlist } from './database'

export interface DashboardStats {
  total_plays: number
  total_uploads: number
  total_likes_received: number
  total_listening_time: number // in seconds
  tracks_uploaded_this_week: number
  plays_this_week: number
  likes_this_week: number
  listening_time_this_week: number
  favorite_genre: string | null
  favorite_ai_tool: string | null
  streak_days: number // consecutive days with activity
}

export interface RecentlyPlayedTrack extends Track {
  listened_at: string
  duration_listened: number
  completed: boolean
  source: 'playlist' | 'search' | 'recommendations' | 'browse' | 'direct'
  playlist_name?: string
}

export interface UploadHistoryItem extends Track {
  upload_status: 'pending' | 'processing' | 'completed' | 'failed'
  upload_progress?: number
  error_message?: string
  processing_time?: number
  credits_used?: number
}

export interface FavoriteTrack extends Track {
  liked_at: string
  play_count_since_liked: number
}

export interface PersonalizedRecommendation {
  track: Track
  reason: 'similar_genre' | 'similar_artist' | 'similar_mood' | 'trending' | 'new_release'
  confidence_score: number // 0-1
  explanation: string
}

export interface ListeningSession {
  id: string
  user_id: string
  started_at: string
  ended_at: string | null
  total_tracks: number
  total_duration: number
  source: string
  tracks: RecentlyPlayedTrack[]
}

export interface DashboardTimeFilter {
  value: 'today' | 'week' | 'month' | 'year' | 'all'
  label: string
  days: number
}

export interface QuickAction {
  id: string
  title: string
  description: string
  icon: string
  href: string
  color: string
  badge?: string | number
  primary?: boolean
}

export interface DashboardSection {
  id: string
  title: string
  description?: string
  icon: string
  data: any[]
  loading: boolean
  error: string | null
  hasMore?: boolean
  onLoadMore?: () => void
  onRefresh?: () => void
} 