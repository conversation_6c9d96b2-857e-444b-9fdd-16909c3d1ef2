#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import fs from "fs/promises";
import path from "path";

// Create MCP server for Tunami memory operations
const server = new McpServer({
  name: "tunami-memory",
  version: "1.0.0"
});

// Memory storage file
const MEMORY_FILE = path.resolve("../tunami-memory.json");

// Initialize memory storage
let memory = {
  userPreferences: {},
  playlists: {},
  sessions: {},
  analytics: {},
  recommendations: {}
};

// Load existing memory
async function loadMemory() {
  try {
    const data = await fs.readFile(MEMORY_FILE, 'utf-8');
    memory = JSON.parse(data);
  } catch (error) {
    // File doesn't exist or is invalid, use default memory
    console.error("Memory file not found, using default memory");
  }
}

// Save memory to file
async function saveMemory() {
  try {
    await fs.writeFile(MEMORY_FILE, JSON.stringify(memory, null, 2), 'utf-8');
  } catch (error) {
    console.error("Error saving memory:", error.message);
  }
}

// Store memory tool
server.tool(
  "store_memory",
  {
    key: z.string().describe("Key to store the memory under"),
    value: z.any().describe("Value to store"),
    category: z.enum(["userPreferences", "playlists", "sessions", "analytics", "recommendations"]).describe("Category to store under")
  },
  async ({ key, value, category }) => {
    try {
      if (!memory[category]) {
        memory[category] = {};
      }
      
      memory[category][key] = {
        value,
        timestamp: new Date().toISOString(),
        updated: new Date().toISOString()
      };
      
      await saveMemory();
      
      return {
        content: [{ 
          type: "text", 
          text: `Successfully stored memory: ${key} in category ${category}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error storing memory: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Retrieve memory tool
server.tool(
  "retrieve_memory",
  {
    key: z.string().describe("Key to retrieve"),
    category: z.enum(["userPreferences", "playlists", "sessions", "analytics", "recommendations"]).describe("Category to retrieve from")
  },
  async ({ key, category }) => {
    try {
      const categoryData = memory[category] || {};
      const item = categoryData[key];
      
      if (!item) {
        return {
          content: [{ 
            type: "text", 
            text: `No memory found for key: ${key} in category ${category}` 
          }]
        };
      }
      
      return {
        content: [{ 
          type: "text", 
          text: `Memory for ${key}:\n${JSON.stringify(item, null, 2)}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error retrieving memory: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// List memories tool
server.tool(
  "list_memories",
  {
    category: z.enum(["userPreferences", "playlists", "sessions", "analytics", "recommendations"]).optional().describe("Category to list (optional)")
  },
  async ({ category }) => {
    try {
      if (category) {
        const categoryData = memory[category] || {};
        const keys = Object.keys(categoryData);
        
        return {
          content: [{ 
            type: "text", 
            text: `Memories in ${category}:\n${keys.join('\n')}` 
          }]
        };
      } else {
        const allCategories = Object.keys(memory).map(cat => {
          const count = Object.keys(memory[cat] || {}).length;
          return `${cat}: ${count} items`;
        }).join('\n');
        
        return {
          content: [{ 
            type: "text", 
            text: `All memory categories:\n${allCategories}` 
          }]
        };
      }
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error listing memories: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Delete memory tool
server.tool(
  "delete_memory",
  {
    key: z.string().describe("Key to delete"),
    category: z.enum(["userPreferences", "playlists", "sessions", "analytics", "recommendations"]).describe("Category to delete from")
  },
  async ({ key, category }) => {
    try {
      const categoryData = memory[category] || {};
      
      if (!categoryData[key]) {
        return {
          content: [{ 
            type: "text", 
            text: `No memory found for key: ${key} in category ${category}` 
          }]
        };
      }
      
      delete categoryData[key];
      await saveMemory();
      
      return {
        content: [{ 
          type: "text", 
          text: `Successfully deleted memory: ${key} from category ${category}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error deleting memory: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Search memories tool
server.tool(
  "search_memories",
  {
    query: z.string().describe("Search query"),
    category: z.enum(["userPreferences", "playlists", "sessions", "analytics", "recommendations"]).optional().describe("Category to search in (optional)")
  },
  async ({ query, category }) => {
    try {
      const results = [];
      const categoriesToSearch = category ? [category] : Object.keys(memory);
      
      for (const cat of categoriesToSearch) {
        const categoryData = memory[cat] || {};
        
        for (const [key, item] of Object.entries(categoryData)) {
          const searchText = `${key} ${JSON.stringify(item.value)}`.toLowerCase();
          if (searchText.includes(query.toLowerCase())) {
            results.push({
              category: cat,
              key,
              value: item.value,
              timestamp: item.timestamp
            });
          }
        }
      }
      
      return {
        content: [{ 
          type: "text", 
          text: `Search results for "${query}":\n${JSON.stringify(results, null, 2)}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error searching memories: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Clear category tool
server.tool(
  "clear_category",
  {
    category: z.enum(["userPreferences", "playlists", "sessions", "analytics", "recommendations"]).describe("Category to clear")
  },
  async ({ category }) => {
    try {
      memory[category] = {};
      await saveMemory();
      
      return {
        content: [{ 
          type: "text", 
          text: `Successfully cleared category: ${category}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error clearing category: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Initialize memory on startup
await loadMemory();

// Start the server
const transport = new StdioServerTransport();
await server.connect(transport);

console.error("Tunami Memory MCP Server running on stdio"); 