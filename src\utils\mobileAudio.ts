// Mobile Audio Utilities for handling device-specific audio issues
import { isIOSDevice, isAndroidDevice, isSafariMobile } from '@/hooks/useMobileDetection'
import { NetworkStatus } from '@/hooks/useNetworkStatus'

export interface MobileAudioConfig {
  preload: 'none' | 'metadata' | 'auto'
  crossOrigin: 'anonymous' | 'use-credentials' | null
  bufferSize: number
  enableAutoplay: boolean
  useWebAudioAPI: boolean
}

// iOS Safari has strict autoplay policies and audio context restrictions
export const getIOSAudioConfig = (networkStatus: NetworkStatus): MobileAudioConfig => {
  return {
    preload: networkStatus.isFastConnection ? 'metadata' : 'none',
    crossOrigin: 'anonymous',
    bufferSize: networkStatus.isSlowConnection ? 5 : 15,
    enableAutoplay: false, // iOS requires user interaction
    useWebAudioAPI: false // Avoid iOS Web Audio API issues
  }
}

// Android Chrome configuration
export const getAndroidAudioConfig = (networkStatus: NetworkStatus): MobileAudioConfig => {
  return {
    preload: networkStatus.isFastConnection ? 'auto' : 'metadata',
    crossOrigin: 'anonymous',
    bufferSize: networkStatus.isSlowConnection ? 8 : 20,
    enableAutoplay: true, // Android Chrome allows autoplay with muted audio
    useWebAudioAPI: true
  }
}

// General mobile configuration
export const getMobileAudioConfig = (networkStatus: NetworkStatus): MobileAudioConfig => {
  if (isIOSDevice()) {
    return getIOSAudioConfig(networkStatus)
  } else if (isAndroidDevice()) {
    return getAndroidAudioConfig(networkStatus)
  }
  
  // Default mobile config
  return {
    preload: 'metadata',
    crossOrigin: 'anonymous',
    bufferSize: 10,
    enableAutoplay: false,
    useWebAudioAPI: true
  }
}

// Handle iOS audio context unlock (required for iOS Safari)
export const unlockIOSAudioContext = (): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!isIOSDevice()) {
      resolve(true)
      return
    }

    // Create a silent audio context to unlock iOS audio
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    
    if (audioContext.state === 'running') {
      resolve(true)
      return
    }

    const unlock = () => {
      // Create empty buffer
      const buffer = audioContext.createBuffer(1, 1, 22050)
      const source = audioContext.createBufferSource()
      source.buffer = buffer
      source.connect(audioContext.destination)
      
      // Play the silent sound
      source.start(0)
      
      // Check if context is unlocked
      setTimeout(() => {
        if (audioContext.state === 'running') {
          resolve(true)
        } else {
          resolve(false)
        }
      }, 100)
    }

    // Try to unlock on user interaction
    const events = ['touchstart', 'touchend', 'mousedown', 'keydown']
    const unlockHandler = () => {
      unlock()
      events.forEach(event => {
        document.removeEventListener(event, unlockHandler, true)
      })
    }

    events.forEach(event => {
      document.addEventListener(event, unlockHandler, true)
    })

    // Timeout after 5 seconds
    setTimeout(() => {
      events.forEach(event => {
        document.removeEventListener(event, unlockHandler, true)
      })
      resolve(false)
    }, 5000)
  })
}

// Create mobile-optimized audio element
export const createMobileAudioElement = (
  src: string, 
  networkStatus: NetworkStatus,
  onCanPlay?: () => void,
  onError?: (error: Event) => void
): HTMLAudioElement => {
  const audio = new Audio()
  const config = getMobileAudioConfig(networkStatus)
  
  // Apply mobile-specific configurations
  audio.preload = config.preload
  audio.crossOrigin = config.crossOrigin
  
  // iOS-specific settings
  if (isIOSDevice()) {
    // Set additional iOS-specific attributes
    audio.setAttribute('playsinline', 'true') // Prevent fullscreen on iOS
    audio.muted = false // Don't mute on iOS as it affects audio quality
  }
  
  // Android-specific settings
  if (isAndroidDevice()) {
    audio.muted = true // Start muted for autoplay compatibility
  }
  
  // Event listeners
  if (onCanPlay) {
    audio.addEventListener('canplay', onCanPlay)
  }
  
  if (onError) {
    audio.addEventListener('error', onError)
  }
  
  // Set source last to trigger loading
  audio.src = src
  
  return audio
}

// Handle mobile audio playback with proper error handling
export const playMobileAudio = async (
  audio: HTMLAudioElement,
  requiresUserInteraction = false
): Promise<boolean> => {
  try {
    // For iOS, ensure audio context is unlocked
    if (isIOSDevice() && requiresUserInteraction) {
      const unlocked = await unlockIOSAudioContext()
      if (!unlocked) {
        throw new Error('iOS audio context not unlocked')
      }
    }
    
    // Unmute if needed (for Android autoplay)
    if (audio.muted && isAndroidDevice()) {
      audio.muted = false
    }
    
    await audio.play()
    return true
  } catch (error) {
    console.warn('Mobile audio playback failed:', error)
    
    // Handle specific mobile audio errors
    if (error instanceof DOMException) {
      switch (error.name) {
        case 'NotAllowedError':
          // User interaction required
          throw new Error('User interaction required for audio playback')
        case 'NotSupportedError':
          // Audio format not supported
          throw new Error('Audio format not supported on this device')
        case 'AbortError':
          // Audio loading was aborted
          throw new Error('Audio loading was interrupted')
        default:
          throw new Error(`Audio playback failed: ${error.message}`)
      }
    }
    
    throw error
  }
}

// Optimize audio loading for mobile networks
export const optimizeAudioForMobile = (
  audio: HTMLAudioElement,
  networkStatus: NetworkStatus
): void => {
  const config = getMobileAudioConfig(networkStatus)
  
  // Adjust buffer size based on network quality
  if ('buffered' in audio) {
    // Set buffer ahead time (not directly supported, but we can influence it)
    audio.preload = networkStatus.isSlowConnection ? 'none' : 'metadata'
  }
  
  // Handle network quality changes
  const handleNetworkChange = () => {
    if (networkStatus.isSlowConnection) {
      // Reduce quality or pause loading on slow connections
      audio.preload = 'none'
    } else {
      audio.preload = 'metadata'
    }
  }
  
  // Listen for network changes
  window.addEventListener('online', handleNetworkChange)
  window.addEventListener('offline', handleNetworkChange)
}

// Check if audio format is supported on mobile
export const isMobileAudioFormatSupported = (mimeType: string): boolean => {
  const audio = document.createElement('audio')
  const support = audio.canPlayType(mimeType)
  
  // Mobile-specific format support
  if (isIOSDevice()) {
    // iOS prefers AAC and MP3
    return mimeType.includes('mp3') || mimeType.includes('aac') || mimeType.includes('mp4')
  }
  
  if (isAndroidDevice()) {
    // Android supports most formats but prefers MP3 and OGG
    return support === 'probably' || support === 'maybe'
  }
  
  return support === 'probably'
}

// Get optimal audio quality for mobile device
export const getMobileAudioQuality = (networkStatus: NetworkStatus): {
  bitrate: number
  sampleRate: number
  format: string
} => {
  const baseQuality = {
    high: { bitrate: 320, sampleRate: 44100, format: 'mp3' },
    medium: { bitrate: 192, sampleRate: 44100, format: 'mp3' },
    low: { bitrate: 128, sampleRate: 22050, format: 'mp3' }
  }
  
  if (networkStatus.quality === 'excellent' || networkStatus.quality === 'good') {
    return baseQuality.high
  } else if (networkStatus.quality === 'fair') {
    return baseQuality.medium
  } else {
    return baseQuality.low
  }
}

// Handle mobile audio interruptions (calls, notifications, etc.)
export const handleMobileAudioInterruptions = (
  audio: HTMLAudioElement,
  onInterruption?: () => void,
  onResume?: () => void
): () => void => {
  let wasPlaying = false
  
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // App went to background
      wasPlaying = !audio.paused
      if (wasPlaying) {
        audio.pause()
        onInterruption?.()
      }
    } else {
      // App came to foreground
      if (wasPlaying) {
        // Small delay to ensure app is fully active
        setTimeout(() => {
          audio.play().catch(() => {
            // Playback failed, might need user interaction
            onResume?.()
          })
        }, 100)
      }
    }
  }
  
  const handleAudioInterruption = () => {
    // Handle audio session interruptions (iOS)
    wasPlaying = !audio.paused
    if (wasPlaying) {
      audio.pause()
      onInterruption?.()
    }
  }
  
  // Listen for visibility changes
  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // Listen for audio interruptions (iOS)
  audio.addEventListener('pause', handleAudioInterruption)
  
  // Cleanup function
  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    audio.removeEventListener('pause', handleAudioInterruption)
  }
}

// Mobile audio debugging utilities
export const getMobileAudioDebugInfo = (audio: HTMLAudioElement) => {
  return {
    device: {
      isIOS: isIOSDevice(),
      isAndroid: isAndroidDevice(),
      isSafariMobile: isSafariMobile(),
      userAgent: navigator.userAgent
    },
    audio: {
      src: audio.src,
      currentTime: audio.currentTime,
      duration: audio.duration,
      paused: audio.paused,
      muted: audio.muted,
      volume: audio.volume,
      readyState: audio.readyState,
      networkState: audio.networkState,
      error: audio.error?.message || null
    },
    support: {
      mp3: isMobileAudioFormatSupported('audio/mpeg'),
      aac: isMobileAudioFormatSupported('audio/aac'),
      ogg: isMobileAudioFormatSupported('audio/ogg'),
      wav: isMobileAudioFormatSupported('audio/wav')
    }
  }
} 