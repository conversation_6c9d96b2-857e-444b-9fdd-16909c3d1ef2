# OAuth Setup Guide for Tunami

This guide will help you set up Google and GitHub OAuth authentication for your Tunami application.

## Prerequisites

- A Supabase project
- Google Cloud Console account (for Google OAuth)
- GitHub account (for GitHub OAuth)

## Supabase OAuth Configuration

### 1. Enable OAuth Providers in Supabase

1. Go to your Supabase Dashboard
2. Navigate to **Authentication** > **Providers**
3. Enable **Google** and **GitHub** providers

### 2. Configure OAuth Providers

#### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to **Credentials** > **Create Credentials** > **OAuth 2.0 Client IDs**
5. Set application type to **Web application**
6. Add authorized redirect URIs:
   ```
   https://your-project-ref.supabase.co/auth/v1/callback
   http://localhost:3000/auth/callback (for development)
   ```
7. Copy the **Client ID** and **Client Secret**
8. In Supabase, paste the Client ID and Client Secret in the Google provider settings

#### GitHub OAuth Setup

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click **New OAuth App**
3. Fill in the application details:
   - **Application name**: Tunami
   - **Homepage URL**: `https://your-domain.com` or `http://localhost:3000`
   - **Authorization callback URL**: `https://your-project-ref.supabase.co/auth/v1/callback`
4. Copy the **Client ID** and **Client Secret**
5. In Supabase, paste the Client ID and Client Secret in the GitHub provider settings

## Environment Variables

Add these to your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# OAuth Configuration (Optional - handled by Supabase)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_GITHUB_CLIENT_ID=your_github_client_id

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Testing OAuth Flow

1. Start your development server:

   ```bash
   npm run dev
   ```

2. Navigate to `/auth/login`
3. Click on "Continue with Google" or "Continue with GitHub"
4. Complete the OAuth flow
5. You should be redirected to `/auth/callback` and then to `/dashboard`

## Troubleshooting

### Common Issues

1. **Redirect URI Mismatch**

   - Ensure the redirect URI in your OAuth provider settings matches exactly
   - For development: `http://localhost:3000/auth/callback`
   - For production: `https://your-domain.com/auth/callback`

2. **OAuth Provider Not Enabled**

   - Check that the provider is enabled in Supabase Dashboard
   - Verify Client ID and Secret are correctly configured

3. **CORS Issues**
   - Ensure your domain is added to the allowed origins in Supabase
   - Check that the redirect URLs are properly configured

### Debug Mode

Enable debug mode by adding this to your environment:

```env
NEXT_PUBLIC_DEBUG_AUTH=true
```

This will log additional information to the console during the OAuth flow.

## Security Considerations

1. **Never expose Client Secrets** in your frontend code
2. Use **HTTPS in production** for OAuth callbacks
3. Regularly **rotate your OAuth credentials**
4. Implement **proper error handling** for failed OAuth attempts
5. Consider **rate limiting** for authentication endpoints

## Production Deployment

When deploying to production:

1. Update OAuth provider settings with production URLs
2. Set proper environment variables
3. Ensure HTTPS is enabled
4. Test the complete OAuth flow
5. Monitor authentication logs

## Additional Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [GitHub OAuth Documentation](https://docs.github.com/en/developers/apps/building-oauth-apps)
