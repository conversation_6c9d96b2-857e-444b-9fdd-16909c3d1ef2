'use client'

import React, { create<PERSON>ontext, useContext, useReducer, useCallback } from 'react'
import { PlaylistService } from '@/lib/playlist-service'
import { 
  Playlist, 
  PlaylistWithTracks, 
  CreatePlaylistData, 
  UpdatePlaylistData, 
  AddTrackToPlaylistData,
  ReorderTrackData,
  PlaylistFilters,
  PlaylistContextType
} from '@/types/playlist'

interface PlaylistState {
  playlists: Playlist[]
  currentPlaylist: PlaylistWithTracks | null
  loading: boolean
  error: string | null
}

type PlaylistAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PLAYLISTS'; payload: Playlist[] }
  | { type: 'SET_CURRENT_PLAYLIST'; payload: PlaylistWithTracks | null }
  | { type: 'ADD_PLAYLIST'; payload: Playlist }
  | { type: 'UPDATE_PLAYLIST'; payload: Playlist }
  | { type: 'DELETE_PLAYLIST'; payload: string }
  | { type: 'CLEAR_ERROR' }

const initialState: PlaylistState = {
  playlists: [],
  currentPlaylist: null,
  loading: false,
  error: null
}

function playlistReducer(state: PlaylistState, action: PlaylistAction): PlaylistState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false }
    
    case 'SET_PLAYLISTS':
      return { ...state, playlists: action.payload, loading: false }
    
    case 'SET_CURRENT_PLAYLIST':
      return { ...state, currentPlaylist: action.payload, loading: false }
    
    case 'ADD_PLAYLIST':
      return { 
        ...state, 
        playlists: [action.payload, ...state.playlists],
        loading: false 
      }
    
    case 'UPDATE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.map(playlist =>
          playlist.id === action.payload.id ? action.payload : playlist
        ),
        currentPlaylist: state.currentPlaylist?.id === action.payload.id
          ? { ...state.currentPlaylist, ...action.payload }
          : state.currentPlaylist,
        loading: false
      }
    
    case 'DELETE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.filter(playlist => playlist.id !== action.payload),
        currentPlaylist: state.currentPlaylist?.id === action.payload ? null : state.currentPlaylist,
        loading: false
      }
    
    case 'CLEAR_ERROR':
      return { ...state, error: null }
    
    default:
      return state
  }
}

const PlaylistContext = createContext<PlaylistContextType | undefined>(undefined)

export function PlaylistProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(playlistReducer, initialState)

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading })
  }, [])

  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error })
  }, [])

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' })
  }, [])

  // Create playlist
  const createPlaylist = useCallback(async (data: CreatePlaylistData): Promise<Playlist> => {
    try {
      setLoading(true)
      clearError()
      
      const playlist = await PlaylistService.createPlaylist(data)
      dispatch({ type: 'ADD_PLAYLIST', payload: playlist })
      
      return playlist
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Update playlist
  const updatePlaylist = useCallback(async (id: string, data: UpdatePlaylistData): Promise<Playlist> => {
    try {
      setLoading(true)
      clearError()
      
      const playlist = await PlaylistService.updatePlaylist(id, data)
      dispatch({ type: 'UPDATE_PLAYLIST', payload: playlist })
      
      return playlist
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Delete playlist
  const deletePlaylist = useCallback(async (id: string): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      
      await PlaylistService.deletePlaylist(id)
      dispatch({ type: 'DELETE_PLAYLIST', payload: id })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Get playlist
  const getPlaylist = useCallback(async (id: string): Promise<PlaylistWithTracks> => {
    try {
      setLoading(true)
      clearError()
      
      const playlist = await PlaylistService.getPlaylist(id)
      dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: playlist })
      
      return playlist
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Get user playlists
  const getUserPlaylists = useCallback(async (userId?: string): Promise<Playlist[]> => {
    try {
      setLoading(true)
      clearError()
      
      const playlists = await PlaylistService.getUserPlaylists(userId)
      dispatch({ type: 'SET_PLAYLISTS', payload: playlists })
      
      return playlists
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch playlists'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Get public playlists
  const getPublicPlaylists = useCallback(async (filters?: PlaylistFilters): Promise<Playlist[]> => {
    try {
      setLoading(true)
      clearError()
      
      const playlists = await PlaylistService.getPublicPlaylists(filters)
      dispatch({ type: 'SET_PLAYLISTS', payload: playlists })
      
      return playlists
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch public playlists'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Add track to playlist
  const addTrackToPlaylist = useCallback(async (data: AddTrackToPlaylistData): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      
      await PlaylistService.addTrackToPlaylist(data)
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === data.playlist_id) {
        await getPlaylist(data.playlist_id)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add track to playlist'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Remove track from playlist
  const removeTrackFromPlaylist = useCallback(async (playlistId: string, trackId: string): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      
      await PlaylistService.removeTrackFromPlaylist(playlistId, trackId)
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === playlistId) {
        await getPlaylist(playlistId)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove track from playlist'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Reorder playlist tracks
  const reorderPlaylistTracks = useCallback(async (playlistId: string, tracks: ReorderTrackData[]): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      
      await PlaylistService.reorderPlaylistTracks(playlistId, tracks)
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === playlistId) {
        await getPlaylist(playlistId)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reorder playlist tracks'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Refresh playlists
  const refreshPlaylists = useCallback(async (): Promise<void> => {
    try {
      await getUserPlaylists()
    } catch (error) {
      // Error is already handled in getUserPlaylists
    }
  }, [getUserPlaylists])

  const contextValue: PlaylistContextType = {
    playlists: state.playlists,
    currentPlaylist: state.currentPlaylist,
    loading: state.loading,
    error: state.error,
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    getPlaylist,
    getUserPlaylists,
    getPublicPlaylists,
    addTrackToPlaylist,
    removeTrackFromPlaylist,
    reorderPlaylistTracks,
    refreshPlaylists,
    clearError
  }

  return (
    <PlaylistContext.Provider value={contextValue}>
      {children}
    </PlaylistContext.Provider>
  )
}

export function usePlaylist() {
  const context = useContext(PlaylistContext)
  if (context === undefined) {
    throw new Error('usePlaylist must be used within a PlaylistProvider')
  }
  return context
} 