# Tunami MCP Servers

This directory contains Model Context Protocol (MCP) servers specifically designed for the Tunami music streaming platform. These servers provide AI agents with secure, controlled access to various tools and data sources.

## 🎵 Available Servers

### 1. **Filesystem Server** (`filesystem-server.js`)

Provides secure file operations for the Tunami project with configurable access controls.

**Features:**

- Read/write files within allowed directories
- List directory contents
- Create directories
- Get file information
- Secure path validation

**Allowed Directories:**

- `../src` - Source code
- `../public` - Public assets
- `../uploads` - User uploads
- `../logs` - Application logs

### 2. **Memory Server** (`memory-server.js`)

Knowledge graph-based persistent memory system for user preferences, playlists, and session data.

**Features:**

- Store/retrieve memories by category
- Search across memory categories
- List and manage memory items
- Categories: userPreferences, playlists, sessions, analytics, recommendations

### 3. **Fetch Server** (`fetch-server.js`)

Web content fetching and conversion optimized for music-related APIs and services.

**Features:**

- Fetch music metadata from MusicBrainz, Last.fm
- Retrieve lyrics from various APIs
- Get album art information
- URL status checking
- Restricted to music-related domains for security

### 4. **GitHub Server** (`github-server.js`)

Repository management and GitHub API integration for the Tunami project.

**Features:**

- Repository information and file listing
- Issue and pull request management
- Commit history and search
- File content retrieval
- Repository search

### 5. **PostgreSQL Server** (`postgres-server.js`)

Database access and schema inspection for the Supabase PostgreSQL database.

**Features:**

- List and describe tables
- Execute read-only queries
- Get table statistics
- Search tables by pattern
- Database information retrieval

### 6. **Context7 MCP Server** (External Package)

Up-to-date documentation and code examples for libraries directly in your AI coding assistant.

**Features:**

- Real-time documentation fetching
- Version-specific code examples
- Library identification and resolution
- No more outdated or hallucinated APIs
- Works with React, Next.js, Supabase, TypeScript, and more

**Setup:**

```bash
npm run setup:context7
```

**Usage:**
Add `use context7` to any prompt for up-to-date documentation.

**Examples:**

- `Create a Supabase auth component for Next.js 14. use context7`
- `Build a music player with React hooks. use context7`

See [CONTEXT7_SETUP.md](./CONTEXT7_SETUP.md) for detailed setup instructions.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- npm or yarn package manager

### Installation

```bash
# Navigate to the mcp-servers directory
cd mcp-servers

# Install dependencies
npm install

# Setup Context7 (recommended)
npm run setup:context7
```

### Running Individual Servers

```bash
# Filesystem Server
npm run start:filesystem

# Memory Server
npm run start:memory

# Fetch Server
npm run start:fetch

# GitHub Server (requires GITHUB_TOKEN)
GITHUB_TOKEN=your_token npm run start:github

# PostgreSQL Server (requires database config)
npm run start:postgres

# Context7 Setup
npm run setup:context7
```

### Environment Variables

Create a `.env` file in the mcp-servers directory:

```env
# GitHub Integration
GITHUB_TOKEN=your_github_personal_access_token

# Supabase Database (for PostgreSQL server)
SUPABASE_DB_HOST=your_supabase_host
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=your_username
SUPABASE_DB_PASSWORD=your_password
SUPABASE_DB_SSL=true
```

## 🔧 Configuration for Claude Desktop

Add these servers to your Claude Desktop configuration:

### Windows

Edit `%APPDATA%\Claude\claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "tunami-filesystem": {
      "command": "node",
      "args": ["D:\\path\\to\\Tunami\\mcp-servers\\filesystem-server.js"],
      "cwd": "D:\\path\\to\\Tunami\\mcp-servers"
    },
    "tunami-memory": {
      "command": "node",
      "args": ["D:\\path\\to\\Tunami\\mcp-servers\\memory-server.js"],
      "cwd": "D:\\path\\to\\Tunami\\mcp-servers"
    },
    "tunami-fetch": {
      "command": "node",
      "args": ["D:\\path\\to\\Tunami\\mcp-servers\\fetch-server.js"],
      "cwd": "D:\\path\\to\\Tunami\\mcp-servers"
    },
    "tunami-github": {
      "command": "node",
      "args": ["D:\\path\\to\\Tunami\\mcp-servers\\github-server.js"],
      "cwd": "D:\\path\\to\\Tunami\\mcp-servers",
      "env": {
        "GITHUB_TOKEN": "your_github_token_here"
      }
    },
    "tunami-postgres": {
      "command": "node",
      "args": ["D:\\path\\to\\Tunami\\mcp-servers\\postgres-server.js"],
      "cwd": "D:\\path\\to\\Tunami\\mcp-servers",
      "env": {
        "SUPABASE_DB_HOST": "your_supabase_host",
        "SUPABASE_DB_USER": "your_username",
        "SUPABASE_DB_PASSWORD": "your_password"
      }
    }
  }
}
```

### macOS/Linux

Edit `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "tunami-filesystem": {
      "command": "node",
      "args": ["/path/to/Tunami/mcp-servers/filesystem-server.js"],
      "cwd": "/path/to/Tunami/mcp-servers"
    },
    "tunami-memory": {
      "command": "node",
      "args": ["/path/to/Tunami/mcp-servers/memory-server.js"],
      "cwd": "/path/to/Tunami/mcp-servers"
    },
    "tunami-fetch": {
      "command": "node",
      "args": ["/path/to/Tunami/mcp-servers/fetch-server.js"],
      "cwd": "/path/to/Tunami/mcp-servers"
    },
    "tunami-github": {
      "command": "node",
      "args": ["/path/to/Tunami/mcp-servers/github-server.js"],
      "cwd": "/path/to/Tunami/mcp-servers",
      "env": {
        "GITHUB_TOKEN": "your_github_token_here"
      }
    },
    "tunami-postgres": {
      "command": "node",
      "args": ["/path/to/Tunami/mcp-servers/postgres-server.js"],
      "cwd": "/path/to/Tunami/mcp-servers",
      "env": {
        "SUPABASE_DB_HOST": "your_supabase_host",
        "SUPABASE_DB_USER": "your_username",
        "SUPABASE_DB_PASSWORD": "your_password"
      }
    }
  }
}
```

## 🛠️ Development

### Testing Servers

You can test individual servers using the MCP Inspector:

```bash
# Install MCP Inspector globally
npm install -g @modelcontextprotocol/inspector

# Test a server
mcp-inspector node filesystem-server.js
```

### Adding New Tools

Each server follows the MCP SDK pattern. To add new tools:

1. Define the tool using `server.tool()`
2. Specify input schema with Zod
3. Implement the async handler function
4. Return properly formatted responses

Example:

```javascript
server.tool(
  "tool_name",
  {
    param1: z.string().describe("Parameter description"),
    param2: z.number().optional().describe("Optional parameter"),
  },
  async ({ param1, param2 }) => {
    try {
      // Tool implementation
      return {
        content: [
          {
            type: "text",
            text: "Tool result",
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }
);
```

## 🔒 Security Considerations

- **Filesystem Server**: Only allows access to predefined directories
- **Fetch Server**: Restricted to music-related domains
- **PostgreSQL Server**: Read-only queries only, with query validation
- **GitHub Server**: Requires authentication token for write operations
- **Memory Server**: Local file storage with structured categories

## 📚 Resources

- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [MCP TypeScript SDK](https://github.com/modelcontextprotocol/typescript-sdk)
- [Claude Desktop MCP Guide](https://docs.anthropic.com/claude/docs/mcp)

## 🤝 Contributing

1. Follow the existing code patterns
2. Add proper error handling
3. Include descriptive tool descriptions
4. Test with MCP Inspector before committing
5. Update this README when adding new servers

## 📄 License

MIT License - see the main project LICENSE file for details.
