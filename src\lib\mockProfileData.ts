// Mock Profile Data for Testing
import { ExtendedProfile, UserStats } from '@/types/profile'
import { Track, Playlist } from '@/types/database'

// Generate mock user profile
export const generateMockProfile = (userId: string): ExtendedProfile => {
  return {
    id: userId,
    email: '<EMAIL>',
    full_name: '<PERSON>',
    username: 'alexj_music',
    avatar_url: null,
    bio: 'AI music enthusiast and creator. Love experimenting with different genres and moods. Always looking for new sounds and collaborations!',
    is_premium: true,
    subscription_tier: 'pro',
    ai_generation_credits: 150,
    total_tracks_generated: 42,
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-12-20T15:45:00Z',
    
    // Extended profile fields
    website_url: 'https://alexjmusic.com',
    twitter_url: 'https://twitter.com/alexj_music',
    instagram_url: 'https://instagram.com/alexj_music',
    youtube_url: 'https://youtube.com/c/alexjmusic',
    spotify_url: 'https://open.spotify.com/artist/alexj',
    soundcloud_url: 'https://soundcloud.com/alexj-music',
    
    location: 'San Francisco, CA',
    birth_date: null,
    phone: null,
    preferred_genres: ['electronic', 'ambient', 'jazz', 'experimental'],
    preferred_moods: ['chill', 'creative', 'uplifting', 'dreamy'],
    
    // Privacy settings
    profile_visibility: 'public',
    show_email: false,
    show_listening_history: true,
    show_playlists: true,
    
    // Notification preferences
    email_notifications: true,
    push_notifications: true,
    marketing_emails: false
  }
}

// Generate mock user stats
export const generateMockStats = (): UserStats => {
  return {
    tracks_uploaded: 42,
    playlists_created: 8,
    total_plays: 15420,
    total_likes_received: 892,
    total_listening_time: 86400, // 24 hours in seconds
    followers_count: 234,
    following_count: 156,
    tracks_liked: 1205,
    playlists_followed: 67
  }
}

// Generate mock tracks
export const generateMockTracks = (userId: string, count: number = 10): Track[] => {
  const trackTitles = [
    'Neon Dreams',
    'Digital Sunset',
    'Cosmic Wanderer',
    'Urban Pulse',
    'Ethereal Waves',
    'Midnight Circuit',
    'Quantum Jazz',
    'Synthetic Soul',
    'Electric Meditation',
    'Future Nostalgia',
    'Binary Blues',
    'Algorithmic Love',
    'Data Stream',
    'Virtual Reality',
    'Cyber Chill'
  ]

  const artists = [
    'Alex Johnson',
    'AI Collective',
    'Digital Dreams',
    'Synthetic Sounds',
    'Future Music'
  ]

  const genres = ['electronic', 'ambient', 'jazz', 'experimental', 'classical']
  const moods = ['chill', 'energetic', 'creative', 'uplifting', 'dreamy']
  const aiTools = ['suno', 'udio', 'mubert', 'aiva', 'custom']

  return Array.from({ length: count }, (_, i) => ({
    id: `track_${userId}_${i + 1}`,
    title: trackTitles[i % trackTitles.length],
    artist_name: artists[i % artists.length],
    file_url: null,
    file_path: null,
    file_size: Math.floor(Math.random() * 10000000) + 1000000, // 1-10MB
    ai_tool: aiTools[i % aiTools.length] as any,
    duration: Math.floor(Math.random() * 300) + 120, // 2-7 minutes
    genre: genres[i % genres.length] as any,
    mood: moods[i % moods.length] as any,
    tempo: Math.floor(Math.random() * 60) + 80, // 80-140 BPM
    key_signature: ['C', 'D', 'E', 'F', 'G', 'A', 'B'][i % 7],
    ai_prompt: `Create a ${moods[i % moods.length]} ${genres[i % genres.length]} track`,
    ai_parameters: {
      style: genres[i % genres.length],
      mood: moods[i % moods.length],
      tempo: Math.floor(Math.random() * 60) + 80
    },
    is_public: true,
    is_featured: i < 3,
    play_count: Math.floor(Math.random() * 1000) + 50,
    like_count: Math.floor(Math.random() * 100) + 5,
    upload_status: 'completed',
    uploaded_by: userId,
    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }))
}

// Generate mock playlists
export const generateMockPlaylists = (userId: string, count: number = 5): Playlist[] => {
  const playlistNames = [
    'Chill Vibes',
    'Work Focus',
    'Late Night Sessions',
    'Creative Flow',
    'Morning Energy',
    'Ambient Dreams',
    'Electronic Favorites',
    'Jazz Fusion'
  ]

  const descriptions = [
    'Perfect for relaxing and unwinding',
    'Background music for productive work sessions',
    'Late night coding and creative work',
    'Music to spark creativity and inspiration',
    'Energizing tracks to start the day',
    'Atmospheric sounds for deep focus',
    'The best electronic tracks I\'ve discovered',
    'Modern jazz with electronic elements'
  ]

  return Array.from({ length: count }, (_, i) => ({
    id: `playlist_${userId}_${i + 1}`,
    name: playlistNames[i % playlistNames.length],
    description: descriptions[i % descriptions.length],
    cover_image_url: null,
    user_id: userId,
    is_public: i < 3, // First 3 are public
    is_collaborative: i === 0, // First one is collaborative
    track_count: Math.floor(Math.random() * 25) + 5, // 5-30 tracks
    total_duration: Math.floor(Math.random() * 7200) + 1800, // 30min - 2.5hrs
    created_at: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }))
}

// Mock API functions for development
export const mockProfileAPI = {
  getUserProfile: async (userId: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    return { profile: generateMockProfile(userId), error: null }
  },

  getUserStats: async (userId: string) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return { stats: generateMockStats(), error: null }
  },

  getUserTracks: async (userId: string, limit: number = 10, offset: number = 0) => {
    await new Promise(resolve => setTimeout(resolve, 400))
    const allTracks = generateMockTracks(userId, 25)
    const tracks = allTracks.slice(offset, offset + limit)
    return { tracks, error: null }
  },

  getUserPlaylists: async (userId: string, limit: number = 10, offset: number = 0) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    const allPlaylists = generateMockPlaylists(userId, 8)
    const playlists = allPlaylists.slice(offset, offset + limit)
    return { playlists, error: null }
  },

  updateProfile: async (userId: string, updates: Partial<ExtendedProfile>) => {
    await new Promise(resolve => setTimeout(resolve, 600))
    const currentProfile = generateMockProfile(userId)
    const updatedProfile = { ...currentProfile, ...updates }
    return { profile: updatedProfile, error: null }
  },

  uploadProfileImage: async (file: File, userId: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000))
    // Simulate successful upload
    const mockUrl = `https://example.com/avatars/${userId}-${Date.now()}.jpg`
    return { url: mockUrl, error: null }
  }
}

// Development mode check
export const isDevelopmentMode = process.env.NODE_ENV === 'development' 