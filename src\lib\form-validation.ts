import React from 'react'

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean
  message?: string
}

export interface FormField {
  value: any
  rules?: ValidationRule[]
  error?: string
  touched?: boolean
}

export interface FormState {
  [key: string]: FormField
}

export class FormValidator {
  static validateField(value: any, rules: ValidationRule[] = []): string | undefined {
    for (const rule of rules) {
      // Required validation
      if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
        return rule.message || 'This field is required'
      }

      // Skip other validations if value is empty and not required
      if (!value || (typeof value === 'string' && !value.trim())) {
        continue
      }

      // Min length validation
      if (rule.minLength && value.length < rule.minLength) {
        return rule.message || `Must be at least ${rule.minLength} characters`
      }

      // Max length validation
      if (rule.maxLength && value.length > rule.maxLength) {
        return rule.message || `Must be no more than ${rule.maxLength} characters`
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(value)) {
        return rule.message || 'Invalid format'
      }

      // Custom validation
      if (rule.custom && !rule.custom(value)) {
        return rule.message || 'Invalid value'
      }
    }

    return undefined
  }

  static validateForm(formState: FormState): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {}
    let isValid = true

    for (const [fieldName, field] of Object.entries(formState)) {
      const error = this.validateField(field.value, field.rules)
      if (error) {
        errors[fieldName] = error
        isValid = false
      }
    }

    return { isValid, errors }
  }

  static updateField(
    formState: FormState,
    fieldName: string,
    value: any,
    touch: boolean = true
  ): FormState {
    const field = formState[fieldName]
    if (!field) return formState

    const error = this.validateField(value, field.rules)

    return {
      ...formState,
      [fieldName]: {
        ...field,
        value,
        error: touch ? error : field.error,
        touched: touch ? true : field.touched
      }
    }
  }

  static touchAllFields(formState: FormState): FormState {
    const updatedState: FormState = {}

    for (const [fieldName, field] of Object.entries(formState)) {
      const error = this.validateField(field.value, field.rules)
      updatedState[fieldName] = {
        ...field,
        error,
        touched: true
      }
    }

    return updatedState
  }
}

// Common validation rules
export const CommonRules = {
  required: (message?: string): ValidationRule => ({
    required: true,
    message: message || 'This field is required'
  }),

  email: (message?: string): ValidationRule => ({
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: message || 'Please enter a valid email address'
  }),

  minLength: (length: number, message?: string): ValidationRule => ({
    minLength: length,
    message: message || `Must be at least ${length} characters`
  }),

  maxLength: (length: number, message?: string): ValidationRule => ({
    maxLength: length,
    message: message || `Must be no more than ${length} characters`
  }),

  password: (message?: string): ValidationRule => ({
    minLength: 6,
    message: message || 'Password must be at least 6 characters'
  }),

  strongPassword: (message?: string): ValidationRule => ({
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    message: message || 'Password must contain at least 8 characters, one uppercase, one lowercase, and one number'
  }),

  url: (message?: string): ValidationRule => ({
    pattern: /^https?:\/\/.+/,
    message: message || 'Please enter a valid URL'
  }),

  phoneNumber: (message?: string): ValidationRule => ({
    pattern: /^\+?[\d\s\-\(\)]+$/,
    message: message || 'Please enter a valid phone number'
  }),

  alphanumeric: (message?: string): ValidationRule => ({
    pattern: /^[a-zA-Z0-9]+$/,
    message: message || 'Only letters and numbers are allowed'
  }),

  noSpecialChars: (message?: string): ValidationRule => ({
    pattern: /^[a-zA-Z0-9\s]+$/,
    message: message || 'Special characters are not allowed'
  }),

  custom: (validator: (value: any) => boolean, message: string): ValidationRule => ({
    custom: validator,
    message
  })
}

// React hook for form validation
export function useFormValidation<T extends Record<string, any>>(
  initialState: T,
  validationSchema: Record<keyof T, ValidationRule[]>
) {
  const [formState, setFormState] = React.useState<FormState>(() => {
    const state: FormState = {}
    for (const [key, value] of Object.entries(initialState)) {
      state[key] = {
        value,
        rules: validationSchema[key] || [],
        error: undefined,
        touched: false
      }
    }
    return state
  })

  const [isSubmitted, setIsSubmitted] = React.useState(false)

  const setValue = React.useCallback((fieldName: keyof T, value: any, touch: boolean = true) => {
    setFormState(prev => FormValidator.updateField(prev, fieldName as string, value, touch))
  }, [])

  const setError = React.useCallback((fieldName: keyof T, error: string) => {
    setFormState(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName as string],
        error
      }
    }))
  }, [])

  const touchField = React.useCallback((fieldName: keyof T) => {
    setFormState(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName as string],
        touched: true,
        error: FormValidator.validateField(
          prev[fieldName as string].value,
          prev[fieldName as string].rules
        )
      }
    }))
  }, [])

  const validate = React.useCallback(() => {
    const touchedState = FormValidator.touchAllFields(formState)
    setFormState(touchedState)
    setIsSubmitted(true)
    
    return FormValidator.validateForm(touchedState)
  }, [formState])

  const reset = React.useCallback(() => {
    const resetState: FormState = {}
    for (const [key, value] of Object.entries(initialState)) {
      resetState[key] = {
        value,
        rules: validationSchema[key] || [],
        error: undefined,
        touched: false
      }
    }
    setFormState(resetState)
    setIsSubmitted(false)
  }, [initialState, validationSchema])

  const getValues = React.useCallback(() => {
    const values: Partial<T> = {}
    for (const [key, field] of Object.entries(formState)) {
      values[key as keyof T] = field.value
    }
    return values as T
  }, [formState])

  const { isValid } = React.useMemo(() => 
    FormValidator.validateForm(formState), 
    [formState]
  )

  return {
    formState,
    isValid,
    isSubmitted,
    setValue,
    setError,
    touchField,
    validate,
    reset,
    getValues
  }
} 