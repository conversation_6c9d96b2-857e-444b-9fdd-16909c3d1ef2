// Profile Management Utilities
import { supabase } from './supabase'
import { ExtendedProfile, UserStats, ProfileFormData, SocialLink } from '@/types/profile'
import { Profile } from '@/types/database'
import { mockProfileAPI, isDevelopmentMode } from './mockProfileData'

// Profile image upload to Supabase Storage
export const uploadProfileImage = async (file: File, userId: string): Promise<{ url: string | null; error: any }> => {
  // Use mock data in development
  if (isDevelopmentMode) {
    return mockProfileAPI.uploadProfileImage(file, userId)
  }

  try {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image')
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      throw new Error('File size must be less than 5MB')
    }

    // Generate unique filename
    const fileExt = file.name.split('.').pop()
    const fileName = `${userId}-${Date.now()}.${fileExt}`
    const filePath = `avatars/${fileName}`

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('profile-images')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) throw error

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('profile-images')
      .getPublicUrl(filePath)

    return { url: publicUrl, error: null }
  } catch (error) {
    console.error('Profile image upload error:', error)
    return { url: null, error }
  }
}

// Delete profile image from Supabase Storage
export const deleteProfileImage = async (imageUrl: string): Promise<{ error: any }> => {
  // Skip in development mode
  if (isDevelopmentMode) {
    return { error: null }
  }

  try {
    // Extract file path from URL
    const urlParts = imageUrl.split('/')
    const fileName = urlParts[urlParts.length - 1]
    const filePath = `avatars/${fileName}`

    const { error } = await supabase.storage
      .from('profile-images')
      .remove([filePath])

    if (error) throw error
    return { error: null }
  } catch (error) {
    console.error('Profile image deletion error:', error)
    return { error }
  }
}

// Get user profile with extended fields
export const getUserProfile = async (userId: string): Promise<{ profile: ExtendedProfile | null; error: any }> => {
  // Use mock data in development
  if (isDevelopmentMode) {
    return mockProfileAPI.getUserProfile(userId)
  }

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) throw error

    // Add default values for extended fields if they don't exist
    const extendedProfile: ExtendedProfile = {
      ...data,
      website_url: data.website_url || null,
      twitter_url: data.twitter_url || null,
      instagram_url: data.instagram_url || null,
      youtube_url: data.youtube_url || null,
      spotify_url: data.spotify_url || null,
      soundcloud_url: data.soundcloud_url || null,
      location: data.location || null,
      birth_date: data.birth_date || null,
      phone: data.phone || null,
      preferred_genres: data.preferred_genres || [],
      preferred_moods: data.preferred_moods || [],
      profile_visibility: data.profile_visibility || 'public',
      show_email: data.show_email ?? false,
      show_listening_history: data.show_listening_history ?? true,
      show_playlists: data.show_playlists ?? true,
      email_notifications: data.email_notifications ?? true,
      push_notifications: data.push_notifications ?? true,
      marketing_emails: data.marketing_emails ?? false,
    }

    return { profile: extendedProfile, error: null }
  } catch (error) {
    console.error('Get user profile error:', error)
    return { profile: null, error }
  }
}

// Update user profile
export const updateUserProfile = async (userId: string, updates: Partial<ExtendedProfile>): Promise<{ profile: ExtendedProfile | null; error: any }> => {
  // Use mock data in development
  if (isDevelopmentMode) {
    return mockProfileAPI.updateProfile(userId, updates)
  }

  try {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error
    return { profile: data as ExtendedProfile, error: null }
  } catch (error) {
    console.error('Update user profile error:', error)
    return { profile: null, error }
  }
}

// Get user statistics
export const getUserStats = async (userId: string): Promise<{ stats: UserStats | null; error: any }> => {
  // Use mock data in development
  if (isDevelopmentMode) {
    return mockProfileAPI.getUserStats(userId)
  }

  try {
    // Get tracks uploaded count
    const { count: tracksUploaded } = await supabase
      .from('tracks')
      .select('*', { count: 'exact', head: true })
      .eq('uploaded_by', userId)

    // Get playlists created count
    const { count: playlistsCreated } = await supabase
      .from('playlists')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    // Get total plays for user's tracks
    const { data: userTracks } = await supabase
      .from('tracks')
      .select('play_count')
      .eq('uploaded_by', userId)

    const totalPlays = userTracks?.reduce((sum, track) => sum + (track.play_count || 0), 0) || 0

    // Get total likes received for user's tracks
    const { data: userTracksWithLikes } = await supabase
      .from('tracks')
      .select('like_count')
      .eq('uploaded_by', userId)

    const totalLikesReceived = userTracksWithLikes?.reduce((sum, track) => sum + (track.like_count || 0), 0) || 0

    // Get total listening time from listening history
    const { data: listeningHistory } = await supabase
      .from('listening_history')
      .select('duration_listened')
      .eq('user_id', userId)

    const totalListeningTime = listeningHistory?.reduce((sum, entry) => sum + (entry.duration_listened || 0), 0) || 0

    // Get tracks liked by user
    const { count: tracksLiked } = await supabase
      .from('track_likes')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)

    const stats: UserStats = {
      tracks_uploaded: tracksUploaded || 0,
      playlists_created: playlistsCreated || 0,
      total_plays: totalPlays,
      total_likes_received: totalLikesReceived,
      total_listening_time: totalListeningTime,
      followers_count: 0, // TODO: Implement followers system
      following_count: 0, // TODO: Implement following system
      tracks_liked: tracksLiked || 0,
      playlists_followed: 0, // TODO: Implement playlist following
    }

    return { stats, error: null }
  } catch (error) {
    console.error('Get user stats error:', error)
    return { stats: null, error }
  }
}

// Get user's uploaded tracks
export const getUserTracks = async (userId: string, limit = 10, offset = 0) => {
  // Use mock data in development
  if (isDevelopmentMode) {
    return mockProfileAPI.getUserTracks(userId, limit, offset)
  }

  try {
    const { data, error } = await supabase
      .from('tracks')
      .select('*')
      .eq('uploaded_by', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error
    return { tracks: data, error: null }
  } catch (error) {
    console.error('Get user tracks error:', error)
    return { tracks: null, error }
  }
}

// Get user's playlists
export const getUserPlaylists = async (userId: string, limit = 10, offset = 0) => {
  // Use mock data in development
  if (isDevelopmentMode) {
    return mockProfileAPI.getUserPlaylists(userId, limit, offset)
  }

  try {
    const { data, error } = await supabase
      .from('playlists')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error
    return { playlists: data, error: null }
  } catch (error) {
    console.error('Get user playlists error:', error)
    return { playlists: null, error }
  }
}

// Delete user account
export const deleteUserAccount = async (userId: string): Promise<{ error: any }> => {
  // Skip in development mode
  if (isDevelopmentMode) {
    console.log('Mock: Account deletion simulated for user:', userId)
    return { error: null }
  }

  try {
    // This should be handled by database triggers/policies
    // For now, we'll just delete the profile
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('id', userId)

    if (error) throw error
    return { error: null }
  } catch (error) {
    console.error('Delete user account error:', error)
    return { error }
  }
}

// Social links configuration
export const socialPlatforms: SocialLink[] = [
  {
    platform: 'website',
    url: '',
    icon: '🌐',
    color: 'text-blue-500'
  },
  {
    platform: 'twitter',
    url: '',
    icon: '🐦',
    color: 'text-blue-400'
  },
  {
    platform: 'instagram',
    url: '',
    icon: '📷',
    color: 'text-pink-500'
  },
  {
    platform: 'youtube',
    url: '',
    icon: '📺',
    color: 'text-red-500'
  },
  {
    platform: 'spotify',
    url: '',
    icon: '🎵',
    color: 'text-green-500'
  },
  {
    platform: 'soundcloud',
    url: '',
    icon: '☁️',
    color: 'text-orange-500'
  }
]

// Format listening time
export const formatListeningTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`
  }
  return `${minutes}m`
}

// Validate social media URL
export const validateSocialUrl = (platform: string, url: string): boolean => {
  if (!url) return true // Empty URLs are valid
  
  const patterns: Record<string, RegExp> = {
    twitter: /^https?:\/\/(www\.)?(twitter\.com|x\.com)\/[a-zA-Z0-9_]+\/?$/,
    instagram: /^https?:\/\/(www\.)?instagram\.com\/[a-zA-Z0-9_.]+\/?$/,
    youtube: /^https?:\/\/(www\.)?youtube\.com\/(channel\/|user\/|c\/)?[a-zA-Z0-9_-]+\/?$/,
    spotify: /^https?:\/\/(open\.)?spotify\.com\/(user\/)?[a-zA-Z0-9_-]+\/?$/,
    soundcloud: /^https?:\/\/(www\.)?soundcloud\.com\/[a-zA-Z0-9_-]+\/?$/,
    website: /^https?:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\/?.*$/
  }
  
  const pattern = patterns[platform]
  return pattern ? pattern.test(url) : true
} 