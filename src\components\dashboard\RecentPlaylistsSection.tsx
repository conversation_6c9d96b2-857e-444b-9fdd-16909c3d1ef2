// Recent Playlists Section Component
'use client'

import { useState } from 'react'
import { 
  List, 
  Play, 
  MoreHorizontal, 
  RefreshCw,
  Music,
  Plus,
  Clock,
  Users,
  Lock,
  Globe
} from 'lucide-react'
import { Playlist } from '@/types/database'

interface RecentPlaylistsSectionProps {
  playlists: Playlist[]
  loading: boolean
  error: string | null
  onRefresh: () => void
}

export default function RecentPlaylistsSection({
  playlists,
  loading,
  error,
  onRefresh
}: RecentPlaylistsSectionProps) {
  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const handleCreatePlaylist = () => {
    // Navigate to playlist creation or open modal
    window.location.href = '/playlists/create'
  }

  const handleViewPlaylist = (playlistId: string) => {
    window.location.href = `/playlists/${playlistId}`
  }

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <List className="w-5 h-5 text-blue-400" />
            Recent Playlists
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="bg-gray-700 rounded-lg p-4 animate-pulse">
              <div className="w-full aspect-square bg-gray-600 rounded-lg mb-3" />
              <div className="h-4 bg-gray-600 rounded w-3/4 mb-2" />
              <div className="h-3 bg-gray-600 rounded w-1/2" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <List className="w-5 h-5 text-blue-400" />
            Recent Playlists
          </h2>
          <button
            onClick={onRefresh}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
        
        <div className="text-center py-8">
          <div className="text-red-400 mb-2">Failed to load recent playlists</div>
          <div className="text-gray-500 text-sm">{error}</div>
          <button
            onClick={onRefresh}
            className="mt-4 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (playlists.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <List className="w-5 h-5 text-blue-400" />
            Recent Playlists
          </h2>
        </div>
        
        <div className="text-center py-12">
          <List className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">No Playlists Yet</h3>
          <p className="text-gray-500 mb-6">Create your first playlist to organize your music!</p>
          <button
            onClick={handleCreatePlaylist}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto"
          >
            <Plus className="w-5 h-5" />
            Create Your First Playlist
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white flex items-center gap-2">
          <List className="w-5 h-5 text-blue-400" />
          Recent Playlists
          <span className="text-sm text-gray-400 font-normal">({playlists.length})</span>
        </h2>
        
        <div className="flex items-center gap-3">
          {/* Create Playlist Button */}
          <button
            onClick={handleCreatePlaylist}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Playlist
          </button>

          {/* Refresh Button */}
          <button
            onClick={onRefresh}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Playlists Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {playlists.map((playlist) => (
          <div
            key={playlist.id}
            className="bg-gray-700 hover:bg-gray-600 rounded-lg p-4 transition-colors group cursor-pointer"
            onClick={() => handleViewPlaylist(playlist.id)}
          >
            {/* Playlist Cover */}
            <div className="relative w-full aspect-square mb-3">
              {playlist.cover_image_url ? (
                <img
                  src={playlist.cover_image_url}
                  alt={playlist.name}
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <List className="w-12 h-12 text-white" />
                </div>
              )}
              
              {/* Privacy Badge */}
              <div className="absolute top-2 right-2">
                {playlist.is_public ? (
                  <div className="bg-green-600 text-white p-1 rounded-full">
                    <Globe className="w-3 h-3" />
                  </div>
                ) : (
                  <div className="bg-gray-600 text-white p-1 rounded-full">
                    <Lock className="w-3 h-3" />
                  </div>
                )}
              </div>

              {/* Collaborative Badge */}
              {playlist.is_collaborative && (
                <div className="absolute top-2 left-2 bg-purple-600 text-white p-1 rounded-full">
                  <Users className="w-3 h-3" />
                </div>
              )}

              {/* Play Button */}
              <button className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <Play className="w-8 h-8 text-white" />
              </button>
            </div>

            {/* Playlist Info */}
            <div className="mb-3">
              <h4 className="font-medium text-white truncate mb-1">{playlist.name}</h4>
              {playlist.description && (
                <p className="text-sm text-gray-400 line-clamp-2 mb-2">{playlist.description}</p>
              )}
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <span>{playlist.track_count || 0} tracks</span>
                <span>•</span>
                <span>{formatDuration(playlist.total_duration || 0)}</span>
                <span>•</span>
                <span>{formatTimeAgo(playlist.created_at)}</span>
              </div>
            </div>

            {/* Playlist Stats */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 text-xs text-gray-400">
                <div className="flex items-center gap-1">
                  <Music className="w-3 h-3" />
                  <span>{playlist.track_count || 0}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatDuration(playlist.total_duration || 0)}</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Visibility Indicator */}
                <span className={`text-xs px-2 py-1 rounded ${
                  playlist.is_public 
                    ? 'bg-green-600 text-white' 
                    : 'bg-gray-600 text-gray-300'
                }`}>
                  {playlist.is_public ? 'Public' : 'Private'}
                </span>

                {/* More Options */}
                <button 
                  className="text-gray-400 hover:text-white transition-colors opacity-0 group-hover:opacity-100"
                  onClick={(e) => {
                    e.stopPropagation()
                    // Handle more options
                  }}
                >
                  <MoreHorizontal className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Playlists Summary */}
      <div className="mt-6 pt-6 border-t border-gray-700">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-400">
              {playlists.length}
            </div>
            <div className="text-sm text-gray-400">Total Playlists</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">
              {playlists.filter(p => p.is_public).length}
            </div>
            <div className="text-sm text-gray-400">Public</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">
              {playlists.filter(p => p.is_collaborative).length}
            </div>
            <div className="text-sm text-gray-400">Collaborative</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-orange-400">
              {playlists.reduce((sum, p) => sum + (p.track_count || 0), 0)}
            </div>
            <div className="text-sm text-gray-400">Total Tracks</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 pt-6 border-t border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4">Quick Actions</h3>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={handleCreatePlaylist}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            New Playlist
          </button>
          <button
            onClick={() => window.location.href = '/playlists'}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
          >
            <List className="w-4 h-4" />
            View All Playlists
          </button>
          <button
            onClick={() => window.location.href = '/playlists/discover'}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
          >
            <Globe className="w-4 h-4" />
            Discover Public Playlists
          </button>
        </div>
      </div>
    </div>
  )
} 