import Link from 'next/link'
import { Music } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-950 to-blue-950 flex items-center justify-center p-4">
      <div className="relative z-10 w-full max-w-4xl text-center">
        {/* Header */}
        <div className="mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="p-4 bg-gradient-to-r from-purple-500 to-blue-500 rounded-3xl">
              <Music className="w-12 h-12 text-white" />
            </div>
          </div>
          <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent mb-6">
            Welcome to Tunami
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Discover, share, and enjoy amazing music with our intelligent platform
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <Link
            href="/auth/login"
            className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            Sign In
          </Link>
          <Link
            href="/auth/signup"
            className="w-full sm:w-auto bg-transparent border-2 border-purple-500 hover:bg-purple-500 text-purple-400 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105"
          >
            Get Started
          </Link>
        </div>

        {/* Features Preview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-3">🎵 Smart Music Discovery</h3>
            <p className="text-gray-300">AI-powered recommendations tailored to your taste</p>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-3">🎧 High-Quality Streaming</h3>
            <p className="text-gray-300">Crystal clear audio with adaptive quality</p>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-3">📱 Mobile-First Design</h3>
            <p className="text-gray-300">Seamless experience across all devices</p>
          </div>
        </div>
      </div>
    </div>
  )
} 