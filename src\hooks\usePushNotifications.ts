'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'

interface PushNotificationPermission {
  permission: NotificationPermission
  isSupported: boolean
  canRequestPermission: boolean
}

interface PushSubscription {
  isSubscribed: boolean
  subscription: globalThis.PushSubscription | null
  endpoint?: string
}

interface NotificationPayload {
  title: string
  body: string
  icon?: string
  image?: string
  badge?: string
  tag?: string
  data?: any
  actions?: NotificationAction[]
  vibrate?: number[]
  silent?: boolean
  requireInteraction?: boolean
}

interface NotificationPreferences {
  newTracks: boolean
  aiUpdates: boolean
  playlistUpdates: boolean
  socialActivity: boolean
  systemUpdates: boolean
  quiet_hours: {
    enabled: boolean
    start: string // HH:MM format
    end: string   // HH:MM format
  }
}

const DEFAULT_PREFERENCES: NotificationPreferences = {
  newTracks: true,
  aiUpdates: true,
  playlistUpdates: true,
  socialActivity: false,
  systemUpdates: true,
  quiet_hours: {
    enabled: false,
    start: '22:00',
    end: '08:00'
  }
}

export function usePushNotifications() {
  const [permission, setPermission] = useState<PushNotificationPermission>({
    permission: 'default',
    isSupported: false,
    canRequestPermission: false
  })
  
  const [subscription, setSubscription] = useState<PushSubscription>({
    isSubscribed: false,
    subscription: null
  })
  
  const [preferences, setPreferences] = useState<NotificationPreferences>(DEFAULT_PREFERENCES)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { user } = useAuth()

  // Check if push notifications are supported
  const checkSupport = useCallback(() => {
    const isSupported = 'serviceWorker' in navigator && 
                       'PushManager' in window && 
                       'Notification' in window

    const canRequestPermission = isSupported && 
                                 Notification.permission !== 'denied'

    return {
      permission: Notification.permission,
      isSupported,
      canRequestPermission
    }
  }, [])

  // Initialize push notification status
  useEffect(() => {
    const initializePushNotifications = async () => {
      setIsLoading(true)
      
      try {
        // Check support and permission
        const supportInfo = checkSupport()
        setPermission(supportInfo)

        // Load user preferences
        if (user) {
          await loadPreferences()
        }

        // Check existing subscription
        if (supportInfo.isSupported && supportInfo.permission === 'granted') {
          await checkExistingSubscription()
        }
      } catch (error) {
        console.error('Failed to initialize push notifications:', error)
        setError('Failed to initialize notifications')
      } finally {
        setIsLoading(false)
      }
    }

    initializePushNotifications()
  }, [user, checkSupport])

  // Load user notification preferences
  const loadPreferences = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('notifications')
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') { // Not found error
        throw error
      }

      if (data?.notifications) {
        setPreferences({ ...DEFAULT_PREFERENCES, ...data.notifications })
      }
    } catch (error) {
      console.error('Failed to load notification preferences:', error)
    }
  }

  // Save user notification preferences
  const savePreferences = async (newPreferences: Partial<NotificationPreferences>) => {
    if (!user) return

    const updatedPreferences = { ...preferences, ...newPreferences }
    setPreferences(updatedPreferences)

    try {
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          notifications: updatedPreferences
        }, {
          onConflict: 'user_id'
        })

      if (error) throw error

      console.log('Notification preferences saved')
    } catch (error) {
      console.error('Failed to save notification preferences:', error)
      setError('Failed to save preferences')
    }
  }

  // Check for existing push subscription
  const checkExistingSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready
      const existingSubscription = await registration.pushManager.getSubscription()

      if (existingSubscription) {
        setSubscription({
          isSubscribed: true,
          subscription: existingSubscription,
          endpoint: existingSubscription.endpoint
        })

        // Verify subscription is still valid on server
        await verifySubscription(existingSubscription)
      }
    } catch (error) {
      console.error('Failed to check existing subscription:', error)
    }
  }

  // Verify subscription with server
  const verifySubscription = async (pushSubscription: globalThis.PushSubscription) => {
    if (!user) return

    try {
      const { error } = await supabase
        .from('push_subscriptions')
        .upsert({
          user_id: user.id,
          endpoint: pushSubscription.endpoint,
          keys: {
            p256dh: arrayBufferToBase64(pushSubscription.getKey('p256dh')),
            auth: arrayBufferToBase64(pushSubscription.getKey('auth'))
          },
          active: true,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,endpoint'
        })

      if (error) throw error
    } catch (error) {
      console.error('Failed to verify subscription:', error)
    }
  }

  // Request notification permission
  const requestPermission = async (): Promise<boolean> => {
    if (!permission.isSupported) {
      setError('Push notifications are not supported on this device')
      return false
    }

    if (permission.permission === 'denied') {
      setError('Notifications are blocked. Please enable them in your browser settings.')
      return false
    }

    try {
      const result = await Notification.requestPermission()
      
      setPermission(prev => ({
        ...prev,
        permission: result,
        canRequestPermission: result !== 'denied'
      }))

      if (result === 'granted') {
        setError(null)
        return true
      } else {
        setError('Notification permission denied')
        return false
      }
    } catch (error) {
      console.error('Failed to request permission:', error)
      setError('Failed to request notification permission')
      return false
    }
  }

  // Subscribe to push notifications
  const subscribe = async (): Promise<boolean> => {
    if (!user) {
      setError('Please log in to enable notifications')
      return false
    }

    if (permission.permission !== 'granted') {
      const granted = await requestPermission()
      if (!granted) return false
    }

    try {
      const registration = await navigator.serviceWorker.ready
      
      // Get VAPID public key from your server
      const vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || 
                            'BEl62iUYgUivxIkv69yViEuiBIa40HI8BUH5tJMDRg-RezqjoBpBGHhJqlHhGXZhA0t_wZ8w6XcKZZHfbXKFnrU'

      const pushSubscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
      })

      // Save subscription to database
      await verifySubscription(pushSubscription)

      setSubscription({
        isSubscribed: true,
        subscription: pushSubscription,
        endpoint: pushSubscription.endpoint
      })

      setError(null)
      console.log('Push notification subscription successful')
      return true

    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error)
      setError('Failed to enable notifications')
      return false
    }
  }

  // Unsubscribe from push notifications
  const unsubscribe = async (): Promise<boolean> => {
    if (!subscription.subscription) return true

    try {
      // Unsubscribe from browser
      await subscription.subscription.unsubscribe()

      // Remove from database
      if (user) {
        await supabase
          .from('push_subscriptions')
          .update({ active: false })
          .eq('user_id', user.id)
          .eq('endpoint', subscription.subscription.endpoint)
      }

      setSubscription({
        isSubscribed: false,
        subscription: null
      })

      setError(null)
      console.log('Push notification unsubscription successful')
      return true

    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error)
      setError('Failed to disable notifications')
      return false
    }
  }

  // Show local notification
  const showNotification = async (payload: NotificationPayload): Promise<boolean> => {
    if (permission.permission !== 'granted') {
      console.warn('Cannot show notification: permission not granted')
      return false
    }

    // Check quiet hours
    if (preferences.quiet_hours.enabled && isInQuietHours()) {
      console.log('Notification suppressed due to quiet hours')
      return false
    }

    try {
      const registration = await navigator.serviceWorker.ready
      
      const notificationOptions: NotificationOptions = {
        body: payload.body,
        icon: payload.icon || '/icons/icon-192x192.png',
        badge: payload.badge || '/icons/icon-72x72.png',
        image: payload.image,
        tag: payload.tag || 'tunami-notification',
        data: payload.data,
        actions: payload.actions,
        vibrate: payload.vibrate || [200, 100, 200],
        silent: payload.silent || false,
        requireInteraction: payload.requireInteraction || false,
        renotify: true
      }

      await registration.showNotification(payload.title, notificationOptions)
      return true

    } catch (error) {
      console.error('Failed to show notification:', error)
      return false
    }
  }

  // Check if current time is in quiet hours
  const isInQuietHours = (): boolean => {
    if (!preferences.quiet_hours.enabled) return false

    const now = new Date()
    const currentTime = now.getHours() * 60 + now.getMinutes()
    
    const [startHour, startMin] = preferences.quiet_hours.start.split(':').map(Number)
    const [endHour, endMin] = preferences.quiet_hours.end.split(':').map(Number)
    
    const startTime = startHour * 60 + startMin
    const endTime = endHour * 60 + endMin

    // Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime
    } else {
      return currentTime >= startTime && currentTime <= endTime
    }
  }

  // Test notification
  const testNotification = async (): Promise<boolean> => {
    return await showNotification({
      title: 'Tunami Test Notification',
      body: 'Push notifications are working! 🎵',
      tag: 'test-notification',
      data: { type: 'test' }
    })
  }

  // Utility functions
  const arrayBufferToBase64 = (buffer: ArrayBuffer | null): string => {
    if (!buffer) return ''
    const bytes = new Uint8Array(buffer)
    let binary = ''
    bytes.forEach(byte => binary += String.fromCharCode(byte))
    return btoa(binary)
  }

  const urlBase64ToUint8Array = (base64String: string): Uint8Array => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    
    return outputArray
  }

  // Send test push notification from server
  const sendTestPush = async (): Promise<boolean> => {
    if (!user || !subscription.isSubscribed) return false

    try {
      const { error } = await supabase.functions.invoke('send-push-notification', {
        body: {
          user_id: user.id,
          notification: {
            title: 'Tunami Test Push',
            body: 'This is a test push notification from the server! 🎵',
            data: { type: 'test', timestamp: Date.now() }
          }
        }
      })

      if (error) throw error
      return true

    } catch (error) {
      console.error('Failed to send test push notification:', error)
      return false
    }
  }

  return {
    // State
    permission,
    subscription,
    preferences,
    isLoading,
    error,
    
    // Actions
    requestPermission,
    subscribe,
    unsubscribe,
    showNotification,
    testNotification,
    sendTestPush,
    savePreferences,
    
    // Utilities
    isSupported: permission.isSupported,
    isGranted: permission.permission === 'granted',
    isSubscribed: subscription.isSubscribed,
    canSubscribe: permission.canRequestPermission && !subscription.isSubscribed
  }
}

// Utility hook for handling notification clicks
export function useNotificationActions() {
  useEffect(() => {
    const handleNotificationClick = (event: Event) => {
      const notificationEvent = event as any
      const notification = notificationEvent.notification
      const action = notificationEvent.action
      const data = notification.data

      console.log('Notification clicked:', { action, data })

      // Handle different notification actions
      switch (action) {
        case 'play':
          if (data?.trackId) {
            window.location.href = `/play?track=${data.trackId}`
          }
          break
        case 'like':
          // Handle like action
          break
        case 'dismiss':
          // Just close the notification
          break
        default:
          // Default action - open app
          if (data?.url) {
            window.location.href = data.url
          } else {
            window.location.href = '/'
          }
      }

      notification.close()
    }

    // Listen for notification clicks from service worker
    navigator.serviceWorker?.addEventListener('message', (event) => {
      if (event.data?.type === 'NOTIFICATION_CLICK') {
        handleNotificationClick(event.data.event)
      }
    })

    return () => {
      navigator.serviceWorker?.removeEventListener('message', handleNotificationClick)
    }
  }, [])
} 