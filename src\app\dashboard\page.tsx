'use client'

import { useState } from 'react'
import { 
  Bar<PERSON>hart3, 
  RefreshCw, 
  Settings,
  TrendingUp,
  Music,
  Plus
} from 'lucide-react'
import { useDashboardAnalytics } from '@/hooks/useDashboardAnalytics'
import DashboardStatsOverview from '@/components/dashboard/DashboardStatsOverview'
import RecentlyPlayedSection from '@/components/dashboard/RecentlyPlayedSection'
import UploadHistorySection from '@/components/dashboard/UploadHistorySection'
import FavoritesSection from '@/components/dashboard/FavoritesSection'
import RecommendationsSection from '@/components/dashboard/RecommendationsSection'
import RecentPlaylistsSection from '@/components/dashboard/RecentPlaylistsSection'

export default function DashboardPage() {
  const {
    // Data
    stats,
    recentlyPlayed,
    uploadHistory,
    favorites,
    recommendations,
    recentPlaylists,
    
    // Loading states
    loading,
    statsLoading,
    recentlyPlayedLoading,
    uploadHistoryLoading,
    favoritesLoading,
    recommendationsLoading,
    playlistsLoading,
    
    // Error states
    error,
    statsError,
    recentlyPlayedError,
    uploadHistoryError,
    favoritesError,
    recommendationsError,
    playlistsError,
    
    // Pagination
    hasMoreRecentlyPlayed,
    hasMoreUploadHistory,
    hasMoreFavorites,
    
    // Time filter
    timeFilter,
    timeFilters,
    
    // Actions
    changeTimeFilter,
    refreshAll,
    loadMoreRecentlyPlayed,
    loadMoreUploadHistory,
    loadMoreFavorites,
    handleToggleTrackLike,
    recordListening,
    
    // Individual refresh functions
    refreshStats,
    refreshRecentlyPlayed,
    refreshUploadHistory,
    refreshFavorites,
    refreshRecommendations,
    refreshRecentPlaylists
  } = useDashboardAnalytics()

  const [activeSection, setActiveSection] = useState<string>('overview')

  const handleQuickUpload = () => {
    window.location.href = '/upload'
  }

  const handleViewProfile = () => {
    window.location.href = '/profile'
  }

  if (loading && !stats) {
    return (
      <div className="min-h-screen bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header Skeleton */}
          <div className="mb-8 animate-pulse">
            <div className="h-8 bg-gray-700 rounded w-64 mb-4" />
            <div className="h-4 bg-gray-700 rounded w-96" />
          </div>

          {/* Stats Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-gray-700 rounded w-20 mb-4" />
                <div className="h-8 bg-gray-700 rounded w-16 mb-2" />
                <div className="h-4 bg-gray-700 rounded w-24" />
              </div>
            ))}
          </div>

          {/* Content Skeleton */}
          <div className="space-y-8">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-gray-700 rounded w-48 mb-6" />
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, j) => (
                    <div key={j} className="h-16 bg-gray-700 rounded" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-900/20 border border-red-800 rounded-lg p-6 text-center">
            <div className="text-red-400 text-lg font-medium mb-2">
              Failed to load dashboard
            </div>
            <div className="text-gray-400 mb-4">{error}</div>
            <button
              onClick={refreshAll}
              className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center gap-3">
              <BarChart3 className="w-8 h-8 text-purple-400" />
              Dashboard Analytics
            </h1>
            <p className="text-gray-400 mt-2">
              Track your music journey with detailed insights and personalized recommendations
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Quick Upload Button */}
            <button
              onClick={handleQuickUpload}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <Plus className="w-5 h-5" />
              Quick Upload
            </button>

            {/* View Profile Button */}
            <button
              onClick={handleViewProfile}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              View Profile
            </button>

            {/* Refresh All Button */}
            <button
              onClick={refreshAll}
              disabled={loading}
              className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-3 rounded-lg font-medium transition-colors disabled:opacity-50"
              title="Refresh all data"
            >
              <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex flex-wrap gap-2 mb-8 border-b border-gray-700 pb-4">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'listening', label: 'Listening History', icon: Music },
            { id: 'recommendations', label: 'Recommendations', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSection(tab.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 ${
                activeSection === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Dashboard Content */}
        <div className="space-y-8">
          {/* Stats Overview - Always visible */}
          <DashboardStatsOverview
            stats={stats}
            timeFilter={timeFilter}
            timeFilters={timeFilters}
            onTimeFilterChange={changeTimeFilter}
            loading={statsLoading}
            error={statsError}
          />

          {/* Conditional Content Based on Active Section */}
          {activeSection === 'overview' && (
            <>
              {/* Recently Played Tracks */}
              <RecentlyPlayedSection
                tracks={recentlyPlayed}
                loading={recentlyPlayedLoading}
                error={recentlyPlayedError}
                hasMore={hasMoreRecentlyPlayed}
                onLoadMore={loadMoreRecentlyPlayed}
                onRefresh={refreshRecentlyPlayed}
              />

              {/* Upload History */}
              <UploadHistorySection
                uploads={uploadHistory}
                loading={uploadHistoryLoading}
                error={uploadHistoryError}
                hasMore={hasMoreUploadHistory}
                onLoadMore={loadMoreUploadHistory}
                onRefresh={refreshUploadHistory}
              />

              {/* Recent Playlists */}
              <RecentPlaylistsSection
                playlists={recentPlaylists}
                loading={playlistsLoading}
                error={playlistsError}
                onRefresh={refreshRecentPlaylists}
              />
            </>
          )}

          {activeSection === 'listening' && (
            <>
              {/* Recently Played Tracks - Expanded View */}
              <RecentlyPlayedSection
                tracks={recentlyPlayed}
                loading={recentlyPlayedLoading}
                error={recentlyPlayedError}
                hasMore={hasMoreRecentlyPlayed}
                onLoadMore={loadMoreRecentlyPlayed}
                onRefresh={refreshRecentlyPlayed}
              />

              {/* Favorite Tracks */}
              <FavoritesSection
                favorites={favorites}
                loading={favoritesLoading}
                error={favoritesError}
                hasMore={hasMoreFavorites}
                onLoadMore={loadMoreFavorites}
                onRefresh={refreshFavorites}
                onToggleLike={handleToggleTrackLike}
              />
            </>
          )}

          {activeSection === 'recommendations' && (
            <>
              {/* Personalized Recommendations */}
              <RecommendationsSection
                recommendations={recommendations}
                loading={recommendationsLoading}
                error={recommendationsError}
                onRefresh={refreshRecommendations}
                onToggleLike={handleToggleTrackLike}
              />

              {/* Favorite Tracks - For context */}
              <FavoritesSection
                favorites={favorites.slice(0, 6)} // Show only first 6 for context
                loading={favoritesLoading}
                error={favoritesError}
                hasMore={false} // Disable load more in this view
                onLoadMore={() => {}}
                onRefresh={refreshFavorites}
                onToggleLike={handleToggleTrackLike}
              />
            </>
          )}
        </div>

        {/* Footer Actions */}
        <div className="mt-12 pt-8 border-t border-gray-700">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="text-gray-400">
              <p>Last updated: {new Date().toLocaleString()}</p>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={() => window.location.href = '/settings'}
                className="text-gray-400 hover:text-white transition-colors flex items-center gap-2"
              >
                <Settings className="w-4 h-4" />
                Dashboard Settings
              </button>
              
              <button
                onClick={refreshAll}
                className="text-gray-400 hover:text-white transition-colors flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Refresh Data
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 