'use client'

import { useState } from 'react'
import { User } from 'lucide-react'

interface AvatarProps {
  src?: string | null
  alt?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  fallbackText?: string
}

const sizeClasses = {
  xs: 'w-6 h-6',
  sm: 'w-8 h-8',
  md: 'w-10 h-10',
  lg: 'w-12 h-12',
  xl: 'w-16 h-16'
}

const iconSizes = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8'
}

export function Avatar({ 
  src, 
  alt = '', 
  size = 'md', 
  className = '',
  fallbackText 
}: AvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const handleImageLoad = () => {
    setIsLoading(false)
  }

  const handleImageError = () => {
    setImageError(true)
    setIsLoading(false)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const showFallback = !src || imageError

  return (
    <div 
      className={`
        relative inline-flex items-center justify-center rounded-full bg-gray-600 overflow-hidden
        ${sizeClasses[size]} 
        ${className}
      `}
    >
      {!showFallback && (
        <>
          {isLoading && (
            <div className="absolute inset-0 bg-gray-600 animate-pulse rounded-full" />
          )}
          <img
            src={src}
            alt={alt}
            className="w-full h-full object-cover"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </>
      )}
      
      {showFallback && (
        <div className="flex items-center justify-center w-full h-full bg-gradient-to-br from-purple-500 to-blue-500">
          {fallbackText || alt ? (
            <span className="text-white font-medium text-sm">
              {getInitials(fallbackText || alt)}
            </span>
          ) : (
            <User className={`text-white ${iconSizes[size]}`} />
          )}
        </div>
      )}
    </div>
  )
} 