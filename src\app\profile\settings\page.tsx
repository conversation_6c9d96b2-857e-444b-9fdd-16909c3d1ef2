// Profile Settings Page
'use client'

import { useState } from 'react'
import { useProfile } from '@/hooks/useProfile'
import { useAuth } from '@/contexts/AuthContext'
import PrivacySettings from '@/components/profile/PrivacySettings'
import NotificationSettings from '@/components/profile/NotificationSettings'
import AccountSettings from '@/components/profile/AccountSettings'
import { LoadingSpinner, ErrorMessage } from '@/components/LoadingStates'

type SettingsTab = 'privacy' | 'notifications' | 'account'

export default function ProfileSettingsPage() {
  const { user } = useAuth()
  const {
    profile,
    loading,
    updating,
    error,
    updateProfile,
    refresh
  } = useProfile()

  const [activeTab, setActiveTab] = useState<SettingsTab>('privacy')

  const tabs = [
    {
      id: 'privacy' as const,
      label: 'Privacy',
      icon: '🔒',
      description: 'Control who can see your profile and content'
    },
    {
      id: 'notifications' as const,
      label: 'Notifications',
      icon: '🔔',
      description: 'Manage your notification preferences'
    },
    {
      id: 'account' as const,
      label: 'Account',
      icon: '⚙️',
      description: 'Account security and deletion'
    }
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <ErrorMessage 
          message={error} 
          onRetry={refresh}
        />
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">⚙️</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Settings Not Available
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Please log in to access your settings.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Profile Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your privacy, notifications, and account preferences
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Settings Navigation */}
          <div className="lg:w-1/4">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-xl">{tab.icon}</span>
                      <div>
                        <div className="font-medium">{tab.label}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {tab.description}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Settings Content */}
          <div className="lg:w-3/4">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              {activeTab === 'privacy' && (
                <PrivacySettings
                  profile={profile}
                  onUpdate={updateProfile}
                  loading={updating}
                />
              )}

              {activeTab === 'notifications' && (
                <NotificationSettings
                  profile={profile}
                  onUpdate={updateProfile}
                  loading={updating}
                />
              )}

              {activeTab === 'account' && (
                <AccountSettings
                  profile={profile}
                  user={user}
                  onUpdate={updateProfile}
                  loading={updating}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 