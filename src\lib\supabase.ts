import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
}

// Create Supabase client with TypeScript support and optimized settings
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storageKey: 'tunami-auth-token',
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce', // More secure flow
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'X-Client-Info': 'tunami@1.0.0',
    },
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
})

// Browser client factory function for social features
export const createBrowserClient = () => {
  return createClient<Database>(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      storageKey: 'tunami-auth-token',
      storage: typeof window !== 'undefined' ? window.localStorage : undefined,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      flowType: 'pkce',
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'X-Client-Info': 'tunami@1.0.0',
      },
    },
  })
}

// Authentication helper functions with OAuth support
export const auth = {
  // Sign up with email and password
  signUp: async (email: string, password: string, options?: { 
    data?: { 
      full_name?: string 
      username?: string 
    } 
  }) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: options?.data || {},
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })
      
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Sign up error:', error)
      return { data: null, error }
    }
  },

  // Sign in with email and password
  signIn: async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      return { data: null, error }
    }
  },

  // Sign in with Google OAuth
  signInWithGoogle: async (options?: { redirectTo?: string }) => {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: options?.redirectTo || `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      })
      
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Google sign in error:', error)
      return { data: null, error }
    }
  },

  // Sign in with GitHub OAuth (optional)
  signInWithGitHub: async (options?: { redirectTo?: string }) => {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'github',
        options: {
          redirectTo: options?.redirectTo || `${window.location.origin}/auth/callback`,
        },
      })
      
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('GitHub sign in error:', error)
      return { data: null, error }
    }
  },

  // Sign out
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      return { error: null }
    } catch (error) {
      console.error('Sign out error:', error)
      return { error }
    }
  },

  // Get current user
  getCurrentUser: async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      if (error) throw error
      return { user, error: null }
    } catch (error) {
      console.error('Get current user error:', error)
      return { user: null, error }
    }
  },

  // Get current session
  getCurrentSession: async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      if (error) throw error
      return { session, error: null }
    } catch (error) {
      console.error('Get current session error:', error)
      return { session: null, error }
    }
  },

  // Reset password
  resetPassword: async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })
      if (error) throw error
      return { error: null }
    } catch (error) {
      console.error('Reset password error:', error)
      return { error }
    }
  },

  // Update password
  updatePassword: async (password: string) => {
    try {
      const { data, error } = await supabase.auth.updateUser({
        password,
      })
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Update password error:', error)
      return { data: null, error }
    }
  },

  // Listen to auth changes
  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback)
  },
}

// Database helper functions
export const db = {
  // Profile operations
  profiles: {
    get: async (userId: string) => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
      
      return { data, error }
    },

    create: async (profile: Database['public']['Tables']['profiles']['Insert']) => {
      const { data, error } = await supabase
        .from('profiles')
        .insert(profile)
        .select()
        .single()
      
      return { data, error }
    },

    update: async (userId: string, updates: Database['public']['Tables']['profiles']['Update']) => {
      const { data, error } = await supabase
        .from('profiles')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single()
      
      return { data, error }
    },
  },

  // Track operations
  tracks: {
    getAll: async (limit = 50) => {
      const { data, error } = await supabase
        .from('tracks')
        .select('*')
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .limit(limit)
      
      return { data, error }
    },

    getById: async (trackId: string) => {
      const { data, error } = await supabase
        .from('tracks')
        .select('*')
        .eq('id', trackId)
        .single()
      
      return { data, error }
    },

    search: async (query: string, limit = 20) => {
      const { data, error } = await supabase
        .from('tracks')
        .select('*')
        .or(`title.ilike.%${query}%,artist_name.ilike.%${query}%`)
        .eq('is_public', true)
        .limit(limit)
      
      return { data, error }
    },

    incrementPlayCount: async (trackId: string) => {
      const { error } = await supabase.rpc('increment_track_play_count', {
        track_id: trackId
      })
      
      return { error }
    },
  },

  // Playlist operations
  playlists: {
    getUserPlaylists: async (userId: string) => {
      const { data, error } = await supabase
        .from('playlists')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
      
      return { data, error }
    },

    create: async (playlist: Database['public']['Tables']['playlists']['Insert']) => {
      const { data, error } = await supabase
        .from('playlists')
        .insert(playlist)
        .select()
        .single()
      
      return { data, error }
    },

    addSong: async (playlistId: string, songId: string, position: number) => {
      const { data, error } = await supabase
        .from('playlist_songs')
        .insert({
          playlist_id: playlistId,
          song_id: songId,
          position,
        })
        .select()
        .single()
      
      return { data, error }
    },

    removeSong: async (playlistId: string, songId: string) => {
      const { error } = await supabase
        .from('playlist_songs')
        .delete()
        .eq('playlist_id', playlistId)
        .eq('song_id', songId)
      
      return { error }
    },
  },

  // User likes
  likes: {
    toggle: async (userId: string, songId: string) => {
      // First check if like exists
      const { data: existingLike } = await supabase
        .from('user_likes')
        .select('id')
        .eq('user_id', userId)
        .eq('song_id', songId)
        .single()

      if (existingLike) {
        // Remove like
        const { error } = await supabase
          .from('user_likes')
          .delete()
          .eq('user_id', userId)
          .eq('song_id', songId)
        
        return { liked: false, error }
      } else {
        // Add like
        const { error } = await supabase
          .from('user_likes')
          .insert({
            user_id: userId,
            song_id: songId,
          })
        
        return { liked: true, error }
      }
    },

    getUserLikes: async (userId: string) => {
      const { data, error } = await supabase
        .from('user_likes')
        .select(`
          *,
          songs (
            *,
            artists (name, image_url),
            albums (title, cover_url)
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
      
      return { data, error }
    },
  },
}

// Error handling helper
export const handleSupabaseError = (error: any) => {
  if (error?.message) {
    console.error('Supabase error:', error.message)
    return error.message
  }
  
  console.error('Unknown Supabase error:', error)
  return 'An unexpected error occurred'
}

// Re-export types for convenience
export type {
  Profile,
  Track,
  Playlist,
  PlaylistTrack,
  TrackLike,
  ListeningHistory,
  MoodType,
  GenreType,
  AIToolType,
  AgreementVersionType,
} from '@/types/database' 