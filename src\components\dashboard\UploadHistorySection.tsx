// Upload History Section Component
'use client'

import { useState } from 'react'
import { 
  Upload, 
  Play, 
  MoreHorizontal, 
  RefreshCw,
  CheckCircle,
  Clock,
  AlertCircle,
  Music,
  Plus,
  Eye,
  Heart,
  BarChart3
} from 'lucide-react'
import { UploadHistoryItem } from '@/types/dashboard'
import { useAudio } from '@/contexts/AudioContext'
import { convertTracksToAudioTracks } from '@/lib/audio-utils'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { EmptyState } from '@/components/ui/EmptyState'

interface UploadHistorySectionProps {
  uploads: UploadHistoryItem[]
  loading: boolean
  error: string | null
  hasMore: boolean
  onLoadMore: () => void
  onRefresh: () => void
}

export default function UploadHistorySection({
  uploads,
  loading,
  error,
  hasMore,
  onLoadMore,
  onRefresh
}: UploadHistorySectionProps) {
  const { setQueue } = useAudio()
  const [actionErrors, setActionErrors] = useState<Record<string, string>>({})

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-400 animate-spin" />
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed'
      case 'processing':
        return 'Processing'
      case 'failed':
        return 'Failed'
      default:
        return 'Unknown'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-400/10 border-green-400/20'
      case 'processing':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20'
      case 'failed':
        return 'text-red-400 bg-red-400/10 border-red-400/20'
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20'
    }
  }

  const getAIToolIcon = (tool: string) => {
    const icons: Record<string, string> = {
      suno: '🎵',
      udio: '🎶',
      mubert: '🎼',
      aiva: '🎹',
      amper: '🎸',
      custom: '⚡',
      other: '🎧'
    }
    return icons[tool] || '🎧'
  }

  const handlePlayTrack = (track: UploadHistoryItem) => {
    if (track.upload_status !== 'completed') {
      setActionErrors(prev => ({ 
        ...prev, 
        [`play-${track.id}`]: 'Track is not ready for playback yet' 
      }))
      return
    }

    try {
      const audioTracks = convertTracksToAudioTracks([track])
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        setActionErrors(prev => ({ ...prev, [`play-${track.id}`]: '' }))
      } else {
        setActionErrors(prev => ({ 
          ...prev, 
          [`play-${track.id}`]: 'Unable to play this track' 
        }))
      }
    } catch (error) {
      setActionErrors(prev => ({ 
        ...prev, 
        [`play-${track.id}`]: 'Failed to play track' 
      }))
    }
  }

  const handleQuickUpload = () => {
    // Navigate to upload page or open upload modal
    window.location.href = '/upload'
  }

  const handleRetryUpload = (trackId: string) => {
    // Implement retry logic here
    console.log('Retrying upload for track:', trackId)
  }

  // Loading state
  if (loading && uploads.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Upload className="w-5 h-5 text-green-400" />
            Upload History
          </h2>
          <LoadingSpinner size="sm" />
        </div>
        
        <div className="space-y-4" role="status" aria-label="Loading upload history">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="flex items-center gap-4 p-4 bg-gray-700 rounded-lg animate-pulse">
              <div className="w-12 h-12 bg-gray-600 rounded-lg" />
              <div className="flex-1">
                <div className="h-4 bg-gray-600 rounded w-3/4 mb-2" />
                <div className="h-3 bg-gray-600 rounded w-1/2" />
              </div>
              <div className="w-20 h-6 bg-gray-600 rounded" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Upload className="w-5 h-5 text-green-400" />
            Upload History
          </h2>
        </div>
        
        <ErrorMessage
          message={error}
          onRetry={onRefresh}
          retryLabel="Reload History"
          severity="error"
          className="max-w-2xl"
        />
      </div>
    )
  }

  // Empty state
  if (uploads.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Upload className="w-5 h-5 text-green-400" />
            Upload History
          </h2>
        </div>
        
        <EmptyState
          icon={Music}
          title="No Uploads Yet"
          description="Start creating and uploading your music!"
          action={{
            label: "Upload Your First Track",
            onClick: handleQuickUpload,
            icon: Plus
          }}
        />
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white flex items-center gap-2">
          <Upload className="w-5 h-5 text-green-400" />
          Upload History
          <span className="text-sm text-gray-400 font-normal">({uploads.length})</span>
        </h2>
        
        <div className="flex items-center gap-3">
          {/* Quick Upload Button */}
          <button
            onClick={handleQuickUpload}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
            aria-label="Upload a new track"
          >
            <Plus className="w-4 h-4" />
            Upload
          </button>

          {/* Refresh Button */}
          <button
            onClick={onRefresh}
            disabled={loading}
            className="text-gray-400 hover:text-white transition-colors disabled:opacity-50"
            aria-label="Refresh upload history"
            title="Refresh upload history"
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Action Errors */}
      {Object.entries(actionErrors).map(([key, error]) => 
        error && (
          <ErrorMessage
            key={key}
            message={error}
            severity="warning"
            onDismiss={() => setActionErrors(prev => ({ ...prev, [key]: '' }))}
            className="mb-4"
          />
        )
      )}

      {/* Uploads List */}
      <div className="space-y-3" role="list" aria-label="Upload history">
        {uploads.map((upload) => (
          <div
            key={upload.id}
            className="flex items-center gap-4 p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors group"
            role="listitem"
          >
            {/* Track Cover & Play Button */}
            <div className="relative w-12 h-12 flex-shrink-0">
              <div 
                className="w-full h-full bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center"
                aria-label={`Album art for ${upload.title}`}
              >
                <span className="text-white text-lg" role="img" aria-label={`Generated with ${upload.ai_tool}`}>
                  {getAIToolIcon(upload.ai_tool)}
                </span>
              </div>
              {upload.upload_status === 'completed' && (
                <button
                  onClick={() => handlePlayTrack(upload)}
                  className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-green-500"
                  aria-label={`Play ${upload.title} by ${upload.artist_name}`}
                  title={`Play ${upload.title}`}
                >
                  <Play className="w-5 h-5 text-white" />
                </button>
              )}
              {upload.upload_status === 'processing' && (
                <div className="absolute inset-0 bg-black bg-opacity-30 rounded-lg flex items-center justify-center">
                  <LoadingSpinner size="sm" className="text-yellow-400" />
                </div>
              )}
            </div>

            {/* Track Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-white truncate" title={upload.title}>{upload.title}</h4>
                <div className="flex items-center gap-1">
                  {getStatusIcon(upload.upload_status)}
                  <span 
                    className={`text-xs px-2 py-0.5 rounded-full border ${getStatusColor(upload.upload_status)}`}
                    aria-label={`Upload status: ${getStatusText(upload.upload_status)}`}
                  >
                    {getStatusText(upload.upload_status)}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <span className="capitalize" title={`Genre: ${upload.genre}`}>{upload.genre}</span>
                <span aria-hidden="true">•</span>
                <span title={`Duration: ${formatDuration(upload.duration || 0)}`}>{formatDuration(upload.duration || 0)}</span>
                <span aria-hidden="true">•</span>
                <span title={`File size: ${formatFileSize(upload.file_size || 0)}`}>{formatFileSize(upload.file_size || 0)}</span>
                <span aria-hidden="true">•</span>
                <span title={`Uploaded ${formatTimeAgo(upload.created_at)}`}>{formatTimeAgo(upload.created_at)}</span>
              </div>
              
              {/* Progress Bar for Processing */}
              {upload.upload_status === 'processing' && upload.upload_progress !== undefined && (
                <div className="mt-2">
                  <div className="w-full bg-gray-600 rounded-full h-1.5">
                    <div 
                      className="bg-yellow-400 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${upload.upload_progress}%` }}
                      role="progressbar"
                      aria-valuenow={upload.upload_progress}
                      aria-valuemin={0}
                      aria-valuemax={100}
                      aria-label={`Upload progress: ${upload.upload_progress}%`}
                    />
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    Processing... {upload.upload_progress}%
                  </div>
                </div>
              )}

              {/* Error Message for Failed Uploads */}
              {upload.upload_status === 'failed' && upload.error_message && (
                <div className="mt-2 text-xs text-red-400 bg-red-400/10 border border-red-400/20 rounded px-2 py-1">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="w-3 h-3 flex-shrink-0" />
                    <span>{upload.error_message}</span>
                    <button
                      onClick={() => handleRetryUpload(upload.id)}
                      className="ml-auto text-red-300 hover:text-red-200 underline"
                      aria-label={`Retry upload for ${upload.title}`}
                    >
                      Retry
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Stats */}
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-1" title={`${upload.play_count || 0} plays`}>
                <Eye className="w-4 h-4" aria-hidden="true" />
                <span aria-label={`${upload.play_count || 0} plays`}>{upload.play_count || 0}</span>
              </div>
              <div className="flex items-center gap-1" title={`${upload.like_count || 0} likes`}>
                <Heart className="w-4 h-4" aria-hidden="true" />
                <span aria-label={`${upload.like_count || 0} likes`}>{upload.like_count || 0}</span>
              </div>
            </div>

            {/* Visibility Badge */}
            <div className="flex items-center gap-2">
              {upload.is_public ? (
                <span 
                  className="bg-green-600 text-white px-2 py-1 rounded text-xs font-medium"
                  aria-label="Public track"
                  title="This track is public and can be discovered by others"
                >
                  Public
                </span>
              ) : (
                <span 
                  className="bg-gray-600 text-gray-300 px-2 py-1 rounded text-xs font-medium"
                  aria-label="Private track"
                  title="This track is private and only visible to you"
                >
                  Private
                </span>
              )}
            </div>

            {/* More Options */}
            <button 
              className="text-gray-400 hover:text-white transition-colors opacity-0 group-hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded"
              aria-label={`More options for ${upload.title}`}
              title="More options"
            >
              <MoreHorizontal className="w-5 h-5" />
            </button>
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center mt-6">
          <button
            onClick={onLoadMore}
            disabled={loading}
            className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
            aria-label={`Load more upload history. Currently showing ${uploads.length} uploads.`}
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="inline mr-2" />
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </button>
        </div>
      )}

      {/* Upload Summary */}
      <div className="mt-6 pt-6 border-t border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4">Upload Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className="p-3 bg-gray-700/50 rounded-lg">
            <div className="text-2xl font-bold text-green-400">
              {uploads.filter(u => u.upload_status === 'completed').length}
            </div>
            <div className="text-sm text-gray-400">Completed</div>
          </div>
          <div className="p-3 bg-gray-700/50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-400">
              {uploads.filter(u => u.upload_status === 'processing').length}
            </div>
            <div className="text-sm text-gray-400">Processing</div>
          </div>
          <div className="p-3 bg-gray-700/50 rounded-lg">
            <div className="text-2xl font-bold text-blue-400">
              {uploads.filter(u => u.is_public).length}
            </div>
            <div className="text-sm text-gray-400">Public</div>
          </div>
          <div className="p-3 bg-gray-700/50 rounded-lg">
            <div className="text-2xl font-bold text-purple-400">
              {uploads.reduce((sum, u) => sum + (u.play_count || 0), 0)}
            </div>
            <div className="text-sm text-gray-400">Total Plays</div>
          </div>
        </div>
      </div>
    </div>
  )
} 