{"name": "tunami-mcp-servers", "version": "1.0.0", "description": "MCP servers setup for Tunami music streaming platform", "type": "module", "main": "index.js", "scripts": {"setup": "node setup-cursor.js", "setup:context7": "node context7-setup.js", "setup:mcp-panel": "node create-mcp-config.js", "verify": "node verify-config.js", "start:filesystem": "node filesystem-server.js", "start:memory": "node memory-server.js", "start:fetch": "node fetch-server.js", "start:github": "node github-server.js", "start:postgres": "node postgres-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "model-context-protocol", "tunami", "music", "streaming", "context7", "documentation"], "author": "Tunami Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "mcp-remote": "^0.1.9", "tunami-mcp-servers": "file:", "zod": "^3.25.28"}}