// Privacy Settings Component
'use client'

import { useState } from 'react'
import { ExtendedProfile, PrivacySettings as PrivacySettingsType } from '@/types/profile'

interface PrivacySettingsProps {
  profile: ExtendedProfile
  onUpdate: (updates: Partial<ExtendedProfile>) => Promise<{ success: boolean; error: string | null }>
  loading: boolean
}

export default function PrivacySettings({ profile, onUpdate, loading }: PrivacySettingsProps) {
  const [settings, setSettings] = useState<PrivacySettingsType>({
    profile_visibility: profile.profile_visibility || 'public',
    show_email: profile.show_email || false,
    show_listening_history: profile.show_listening_history ?? true,
    show_playlists: profile.show_playlists ?? true
  })

  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const handleSettingChange = (key: keyof PrivacySettingsType, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    setMessage(null)
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      setMessage(null)

      const result = await onUpdate(settings)
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Privacy settings updated successfully!' })
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to update settings' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred' })
    } finally {
      setSaving(false)
    }
  }

  const visibilityOptions = [
    {
      value: 'public' as const,
      label: 'Public',
      description: 'Anyone can view your profile and content',
      icon: '🌍'
    },
    {
      value: 'private' as const,
      label: 'Private',
      description: 'Only you can view your profile',
      icon: '🔒'
    },
    {
      value: 'friends' as const,
      label: 'Friends Only',
      description: 'Only your friends can view your profile',
      icon: '👥'
    }
  ]

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Privacy Settings
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Control who can see your profile and what information is visible to others.
        </p>
      </div>

      <div className="space-y-8">
        {/* Profile Visibility */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Profile Visibility
          </h3>
          <div className="space-y-3">
            {visibilityOptions.map((option) => (
              <label
                key={option.value}
                className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                  settings.profile_visibility === option.value
                    ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <input
                  type="radio"
                  name="profile_visibility"
                  value={option.value}
                  checked={settings.profile_visibility === option.value}
                  onChange={(e) => handleSettingChange('profile_visibility', e.target.value)}
                  className="mt-1 text-purple-600 focus:ring-purple-500"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-lg">{option.icon}</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {option.label}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {option.description}
                  </p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Content Visibility */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Content Visibility
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Show Email Address
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Display your email address on your public profile
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.show_email}
                  onChange={(e) => handleSettingChange('show_email', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Show Listening History
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Allow others to see what you've been listening to
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.show_listening_history}
                  onChange={(e) => handleSettingChange('show_listening_history', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Show Playlists
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Display your public playlists on your profile
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.show_playlists}
                  onChange={(e) => handleSettingChange('show_playlists', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Privacy Tips */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <span className="text-blue-500 text-xl">💡</span>
            <div>
              <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-2">
                Privacy Tips
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-400 space-y-1">
                <li>• Private profiles are only visible to you</li>
                <li>• Friends-only profiles require a follow system (coming soon)</li>
                <li>• Your uploaded tracks respect your profile visibility settings</li>
                <li>• Email addresses are never shown to other users by default</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Message */}
        {message && (
          <div className={`p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
              : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
          }`}>
            {message.text}
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleSave}
            disabled={saving || loading}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            {saving ? 'Saving...' : 'Save Privacy Settings'}
          </button>
        </div>
      </div>
    </div>
  )
} 