import React from 'react'

export function PWAMetadata() {
  return (
    <>
      {/* PWA Meta Tags */}
      <meta name="application-name" content="Tunami" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Tunami" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="msapplication-config" content="/browserconfig.xml" />
      <meta name="msapplication-TileColor" content="#7c3aed" />
      <meta name="msapplication-tap-highlight" content="no" />
      
      {/* Manifest */}
      <link rel="manifest" href="/manifest.json" />
      
      {/* Theme Colors */}
      <meta name="theme-color" content="#0f172a" />
      <meta name="msapplication-navbutton-color" content="#0f172a" />
      
      {/* Disable automatic phone number detection */}
      <meta name="format-detection" content="telephone=no" />
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="Referrer-Policy" content="origin-when-cross-origin" />
    </>
  )
} 