'use client'

import { Music, Loader2, Play, Upload, Headphones, AlertCircle, RefreshCw } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
  text?: string
}

export function LoadingSpinner({ size = 'md', className = '', text }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8'
  }

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <Loader2 className={`${sizeClasses[size]} animate-spin text-purple-400`} />
      {text && <span className="ml-2 text-gray-300">{text}</span>}
    </div>
  )
}

interface PageLoadingProps {
  title?: string
  subtitle?: string
  icon?: 'music' | 'play' | 'upload' | 'headphones'
  className?: string
}

export function PageLoading({ 
  title = 'Loading...', 
  subtitle,
  icon = 'music',
  className = '' 
}: PageLoadingProps) {
  const icons = {
    music: Music,
    play: Play,
    upload: Upload,
    headphones: Headphones
  }
  
  const IconComponent = icons[icon]

  return (
    <div className={`min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center p-4 ${className}`}>
      <div className="text-center">
        <div className="relative mb-6">
          <IconComponent className="w-16 h-16 text-purple-400 animate-pulse mx-auto" />
          <Loader2 className="w-6 h-6 text-white animate-spin absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
        </div>
        <h2 className="text-xl font-bold text-white mb-2">{title}</h2>
        {subtitle && <p className="text-gray-400">{subtitle}</p>}
      </div>
    </div>
  )
}

interface ContentLoadingProps {
  rows?: number
  showAvatar?: boolean
  className?: string
}

export function ContentLoading({ rows = 3, showAvatar = false, className = '' }: ContentLoadingProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: rows }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 bg-gray-800 rounded-lg animate-pulse">
          {showAvatar && (
            <div className="w-12 h-12 bg-gray-700 rounded-lg flex-shrink-0" />
          )}
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-700 rounded w-3/4" />
            <div className="h-3 bg-gray-700 rounded w-1/2" />
          </div>
          <div className="w-8 h-8 bg-gray-700 rounded-full flex-shrink-0" />
        </div>
      ))}
    </div>
  )
}

interface TrackLoadingProps {
  count?: number
  layout?: 'horizontal' | 'vertical'
  className?: string
}

export function TrackLoading({ count = 6, layout = 'vertical', className = '' }: TrackLoadingProps) {
  if (layout === 'horizontal') {
    return (
      <div className={`space-y-4 ${className}`}>
        {Array.from({ length: count }).map((_, index) => (
          <div key={index} className="flex items-center space-x-4 p-4 bg-gray-800 rounded-lg animate-pulse">
            <div className="w-16 h-16 bg-gray-700 rounded-lg flex-shrink-0" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-700 rounded w-3/4" />
              <div className="h-3 bg-gray-700 rounded w-1/2" />
            </div>
            <div className="flex space-x-2">
              <div className="w-8 h-8 bg-gray-700 rounded-full" />
              <div className="w-8 h-8 bg-gray-700 rounded-full" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-gray-800 rounded-lg p-4 animate-pulse">
          <div className="aspect-square bg-gray-700 rounded-lg mb-4" />
          <div className="space-y-2">
            <div className="h-4 bg-gray-700 rounded w-3/4" />
            <div className="h-3 bg-gray-700 rounded w-1/2" />
          </div>
        </div>
      ))}
    </div>
  )
}

interface ButtonLoadingProps {
  text?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'secondary'
  disabled?: boolean
  className?: string
}

export function ButtonLoading({ 
  text = 'Loading...', 
  size = 'md',
  variant = 'primary',
  disabled = true,
  className = '' 
}: ButtonLoadingProps) {
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }

  const variantClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary'
  }

  return (
    <button 
      disabled={disabled}
      className={`${sizeClasses[size]} ${variantClasses[variant]} flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      <Loader2 className="w-4 h-4 animate-spin" />
      {text}
    </button>
  )
}

interface AudioLoadingProps {
  className?: string
}

export function AudioLoading({ className = '' }: AudioLoadingProps) {
  return (
    <div className={`bg-gray-800 rounded-lg p-4 ${className}`}>
      <div className="flex items-center space-x-4 mb-4 animate-pulse">
        <div className="w-12 h-12 bg-gray-700 rounded-lg flex-shrink-0" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-700 rounded w-3/4" />
          <div className="h-3 bg-gray-700 rounded w-1/2" />
        </div>
      </div>
      
      <div className="space-y-3 animate-pulse">
        <div className="h-1 bg-gray-700 rounded-full w-full" />
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <div className="w-8 h-8 bg-gray-700 rounded-full" />
            <div className="w-8 h-8 bg-gray-700 rounded-full" />
            <div className="w-8 h-8 bg-gray-700 rounded-full" />
          </div>
          <div className="flex space-x-2">
            <div className="h-3 bg-gray-700 rounded w-12" />
            <div className="w-20 h-1 bg-gray-700 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  )
}

interface ErrorMessageProps {
  message: string
  onRetry?: () => void
  className?: string
}

export function ErrorMessage({ message, onRetry, className = '' }: ErrorMessageProps) {
  return (
    <div className={`text-center p-6 ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        <AlertCircle className="w-12 h-12 text-red-400" />
        <div>
          <h3 className="text-lg font-semibold text-white mb-2">Something went wrong</h3>
          <p className="text-gray-400 max-w-md">{message}</p>
        </div>
        {onRetry && (
          <button
            onClick={onRetry}
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Try Again</span>
          </button>
        )}
      </div>
    </div>
  )
}

// Combined loading state for dashboard
export function DashboardLoading() {
  return (
    <div className="space-y-8">
      {/* Header loading */}
      <div className="animate-pulse">
        <div className="h-8 bg-gray-700 rounded w-1/3 mb-2" />
        <div className="h-4 bg-gray-700 rounded w-1/2" />
      </div>

      {/* Stats grid loading */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="bg-gray-800 rounded-lg p-6 animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="w-8 h-8 bg-gray-700 rounded" />
              <div className="h-4 bg-gray-700 rounded w-16" />
            </div>
            <div className="h-8 bg-gray-700 rounded w-20 mb-2" />
            <div className="h-3 bg-gray-700 rounded w-24" />
          </div>
        ))}
      </div>

      {/* Recent tracks loading */}
      <div>
        <div className="h-6 bg-gray-700 rounded w-32 mb-4 animate-pulse" />
        <TrackLoading count={4} layout="horizontal" />
      </div>
    </div>
  )
} 