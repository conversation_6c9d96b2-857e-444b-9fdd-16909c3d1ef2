import { supabase } from './supabase'
import type { PostgrestFilterBuilder } from '@supabase/postgrest-js'

interface QueryCache {
  data: any
  timestamp: number
  ttl: number
}

interface QueryOptions {
  useCache?: boolean
  cacheTTL?: number
  pageSize?: number
  page?: number
  select?: string
  orderBy?: string
  ascending?: boolean
  filters?: Record<string, any>
  enableCount?: boolean
}

interface OptimizedQueryResult<T> {
  data: T[]
  count?: number
  hasMore: boolean
  page: number
  pageSize: number
  totalPages?: number
  error?: string
  fromCache: boolean
  queryTime: number
}

class DatabaseOptimizer {
  private cache = new Map<string, QueryCache>()
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5 minutes
  private readonly DEFAULT_PAGE_SIZE = 20

  constructor() {
    // Clean up expired cache entries every 5 minutes
    setInterval(() => {
      this.cleanExpiredCache()
    }, 5 * 60 * 1000)
  }

  private generateCacheKey(
    table: string,
    options: QueryOptions,
    userId?: string
  ): string {
    const keyData = {
      table,
      ...options,
      userId: userId || 'anonymous'
    }
    return JSON.stringify(keyData)
  }

  private cleanExpiredCache(): void {
    const now = Date.now()
    for (const [key, cache] of this.cache.entries()) {
      if (now > cache.timestamp + cache.ttl) {
        this.cache.delete(key)
      }
    }
  }

  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    const now = Date.now()
    if (now > cached.timestamp + cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  // Optimized paginated query with caching
  async queryPaginated<T>(
    table: string,
    options: QueryOptions = {},
    userId?: string
  ): Promise<OptimizedQueryResult<T>> {
    const startTime = performance.now()
    
    const {
      useCache = true,
      cacheTTL = this.DEFAULT_TTL,
      pageSize = this.DEFAULT_PAGE_SIZE,
      page = 0,
      select = '*',
      orderBy = 'created_at',
      ascending = false,
      filters = {},
      enableCount = false
    } = options

    const cacheKey = this.generateCacheKey(table, options, userId)

    // Try cache first
    if (useCache) {
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return {
          ...cached,
          fromCache: true,
          queryTime: performance.now() - startTime
        }
      }
    }

    try {
      // Build base query
      let query = supabase
        .from(table)
        .select(select, { count: enableCount ? 'exact' : undefined })

      // Apply filters
      Object.entries(filters).forEach(([column, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            query = query.in(column, value)
          } else if (typeof value === 'object' && value.operator) {
            // Support for complex filters like { operator: 'ilike', value: '%search%' }
            switch (value.operator) {
              case 'ilike':
                query = query.ilike(column, value.value)
                break
              case 'gte':
                query = query.gte(column, value.value)
                break
              case 'lte':
                query = query.lte(column, value.value)
                break
              case 'gt':
                query = query.gt(column, value.value)
                break
              case 'lt':
                query = query.lt(column, value.value)
                break
              case 'neq':
                query = query.neq(column, value.value)
                break
              default:
                query = query.eq(column, value.value)
            }
          } else {
            query = query.eq(column, value)
          }
        }
      })

      // Apply ordering
      query = query.order(orderBy, { ascending })

      // Apply pagination
      const from = page * pageSize
      const to = from + pageSize - 1
      query = query.range(from, to)

      const { data, error, count } = await query

      if (error) {
        throw error
      }

      const totalPages = enableCount && count ? Math.ceil(count / pageSize) : undefined
      const hasMore = data ? data.length === pageSize : false

      const result: OptimizedQueryResult<T> = {
        data: data || [],
        count,
        hasMore,
        page,
        pageSize,
        totalPages,
        fromCache: false,
        queryTime: performance.now() - startTime
      }

      // Cache the result
      if (useCache && data) {
        this.setCache(cacheKey, result, cacheTTL)
      }

      return result
    } catch (error: any) {
      return {
        data: [],
        hasMore: false,
        page,
        pageSize,
        error: error.message,
        fromCache: false,
        queryTime: performance.now() - startTime
      }
    }
  }

  // Optimized single record query
  async queryById<T>(
    table: string,
    id: string,
    options: Omit<QueryOptions, 'page' | 'pageSize'> = {}
  ): Promise<{ data: T | null; error?: string; fromCache: boolean; queryTime: number }> {
    const startTime = performance.now()
    
    const {
      useCache = true,
      cacheTTL = this.DEFAULT_TTL,
      select = '*'
    } = options

    const cacheKey = this.generateCacheKey(table, { ...options, id })

    // Try cache first
    if (useCache) {
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return {
          data: cached,
          fromCache: true,
          queryTime: performance.now() - startTime
        }
      }
    }

    try {
      const { data, error } = await supabase
        .from(table)
        .select(select)
        .eq('id', id)
        .single()

      if (error) {
        throw error
      }

      // Cache the result
      if (useCache && data) {
        this.setCache(cacheKey, data, cacheTTL)
      }

      return {
        data,
        fromCache: false,
        queryTime: performance.now() - startTime
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message,
        fromCache: false,
        queryTime: performance.now() - startTime
      }
    }
  }

  // Optimized search query with fuzzy matching
  async searchRecords<T>(
    table: string,
    searchTerm: string,
    searchColumns: string[],
    options: QueryOptions = {}
  ): Promise<OptimizedQueryResult<T>> {
    const searchFilters = searchColumns.reduce((acc, column) => {
      acc[column] = {
        operator: 'ilike',
        value: `%${searchTerm}%`
      }
      return acc
    }, {} as Record<string, any>)

    // Use OR logic for search - we'll need to implement this differently
    // since Supabase doesn't directly support OR in our filter system
    const startTime = performance.now()
    
    try {
      let query = supabase
        .from(table)
        .select(options.select || '*', { count: options.enableCount ? 'exact' : undefined })

      // Build OR conditions for search
      const orConditions = searchColumns.map(column => `${column}.ilike.%${searchTerm}%`).join(',')
      query = query.or(orConditions)

      // Apply additional filters
      Object.entries(options.filters || {}).forEach(([column, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(column, value)
        }
      })

      // Apply ordering and pagination
      query = query
        .order(options.orderBy || 'created_at', { ascending: options.ascending || false })
        .range(
          (options.page || 0) * (options.pageSize || this.DEFAULT_PAGE_SIZE),
          ((options.page || 0) + 1) * (options.pageSize || this.DEFAULT_PAGE_SIZE) - 1
        )

      const { data, error, count } = await query

      if (error) {
        throw error
      }

      return {
        data: data || [],
        count,
        hasMore: data ? data.length === (options.pageSize || this.DEFAULT_PAGE_SIZE) : false,
        page: options.page || 0,
        pageSize: options.pageSize || this.DEFAULT_PAGE_SIZE,
        totalPages: options.enableCount && count ? Math.ceil(count / (options.pageSize || this.DEFAULT_PAGE_SIZE)) : undefined,
        fromCache: false,
        queryTime: performance.now() - startTime
      }
    } catch (error: any) {
      return {
        data: [],
        hasMore: false,
        page: options.page || 0,
        pageSize: options.pageSize || this.DEFAULT_PAGE_SIZE,
        error: error.message,
        fromCache: false,
        queryTime: performance.now() - startTime
      }
    }
  }

  // Batch operations for better performance
  async batchInsert<T>(
    table: string,
    records: Partial<T>[],
    chunkSize: number = 100
  ): Promise<{ success: boolean; error?: string; insertedCount: number }> {
    try {
      let insertedCount = 0

      // Process in chunks to avoid overwhelming the database
      for (let i = 0; i < records.length; i += chunkSize) {
        const chunk = records.slice(i, i + chunkSize)
        
        const { data, error } = await supabase
          .from(table)
          .insert(chunk)

        if (error) {
          throw error
        }

        insertedCount += chunk.length
      }

      // Invalidate related cache entries
      this.invalidateTableCache(table)

      return {
        success: true,
        insertedCount
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        insertedCount: 0
      }
    }
  }

  // Cache invalidation
  invalidateTableCache(table: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.includes(`"table":"${table}"`)
    )
    
    keysToDelete.forEach(key => {
      this.cache.delete(key)
    })
  }

  invalidateUserCache(userId: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.includes(`"userId":"${userId}"`)
    )
    
    keysToDelete.forEach(key => {
      this.cache.delete(key)
    })
  }

  clearCache(): void {
    this.cache.clear()
  }

  // Performance monitoring
  getCacheStats(): {
    size: number
    hitRate: number
    totalQueries: number
    cachedQueries: number
  } {
    // This is a simplified version - in production you'd want more detailed metrics
    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits vs misses
      totalQueries: 0,
      cachedQueries: this.cache.size
    }
  }
}

// Export singleton instance
export const dbOptimizer = new DatabaseOptimizer()

// Convenience functions for common operations
export const optimizedQuery = {
  tracks: (options?: QueryOptions, userId?: string) => 
    dbOptimizer.queryPaginated('tracks', options, userId),
  
  playlists: (options?: QueryOptions, userId?: string) => 
    dbOptimizer.queryPaginated('playlists', options, userId),
  
  users: (options?: QueryOptions) => 
    dbOptimizer.queryPaginated('users', options),
  
  searchTracks: (searchTerm: string, options?: QueryOptions) =>
    dbOptimizer.searchRecords('tracks', searchTerm, ['title', 'artist', 'album'], options),
  
  searchPlaylists: (searchTerm: string, options?: QueryOptions) =>
    dbOptimizer.searchRecords('playlists', searchTerm, ['name', 'description'], options),
  
  trackById: (id: string, options?: Omit<QueryOptions, 'page' | 'pageSize'>) =>
    dbOptimizer.queryById('tracks', id, options),
  
  playlistById: (id: string, options?: Omit<QueryOptions, 'page' | 'pageSize'>) =>
    dbOptimizer.queryById('playlists', id, options)
} 