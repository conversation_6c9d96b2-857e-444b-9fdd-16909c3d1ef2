'use client'

import { useState, useEffect, useCallback } from 'react'
import { X, Download, Smartphone, Monitor, Share } from 'lucide-react'
import { useMobileDetection } from '@/hooks/useMobileDetection'

interface PWAInstallPromptProps {
  onInstall?: () => void
  onDismiss?: () => void
  className?: string
  autoShow?: boolean
  showAfterDelay?: number
  showAfterPageViews?: number
}

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: BeforeInstallPromptEvent
  }
}

export default function PWAInstallPrompt({
  onInstall,
  onDismiss,
  className = '',
  autoShow = true,
  showAfterDelay = 30000, // 30 seconds
  showAfterPageViews = 3
}: PWAInstallPromptProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [isInstalling, setIsInstalling] = useState(false)
  const [installStep, setInstallStep] = useState<'prompt' | 'instructions' | 'success'>('prompt')
  const [pageViews, setPageViews] = useState(0)
  
  const { isMobile, isTablet, isDesktop } = useMobileDetection()

  // Check if app is already installed
  const isAppInstalled = useCallback(() => {
    // PWA installed check
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return true
    }
    
    // iOS Safari standalone mode
    if ((window.navigator as any).standalone === true) {
      return true
    }
    
    // Android app
    if (document.referrer.startsWith('android-app://')) {
      return true
    }
    
    return false
  }, [])

  // Track page views for install prompt timing
  useEffect(() => {
    if (isAppInstalled()) return

    const storedViews = localStorage.getItem('tunami-page-views')
    const views = storedViews ? parseInt(storedViews) : 0
    const newViews = views + 1
    
    setPageViews(newViews)
    localStorage.setItem('tunami-page-views', newViews.toString())
  }, [isAppInstalled])

  // Check if user has previously dismissed
  const hasUserDismissed = useCallback(() => {
    const dismissed = localStorage.getItem('tunami-install-dismissed')
    if (!dismissed) return false
    
    const dismissedTime = parseInt(dismissed)
    const weekInMs = 7 * 24 * 60 * 60 * 1000
    
    // Show again after a week
    return Date.now() - dismissedTime < weekInMs
  }, [])

  // Setup install prompt event listener
  useEffect(() => {
    if (isAppInstalled() || hasUserDismissed()) return

    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      console.log('🎵 PWA: Before install prompt event fired')
      
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault()
      
      // Stash the event so it can be triggered later
      setDeferredPrompt(e)
      
      // Show prompt based on conditions
      if (autoShow && pageViews >= showAfterPageViews) {
        setTimeout(() => {
          setIsVisible(true)
        }, showAfterDelay)
      }
    }

    const handleAppInstalled = () => {
      console.log('🎵 PWA: App was installed')
      setInstallStep('success')
      setIsVisible(false)
      
      // Clean up stored preferences
      localStorage.removeItem('tunami-install-dismissed')
      localStorage.removeItem('tunami-page-views')
      
      onInstall?.()
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [autoShow, showAfterDelay, showAfterPageViews, pageViews, isAppInstalled, hasUserDismissed, onInstall])

  const handleInstall = async () => {
    if (!deferredPrompt) {
      // Show manual installation instructions
      setInstallStep('instructions')
      return
    }

    try {
      setIsInstalling(true)
      
      // Show the install prompt
      await deferredPrompt.prompt()
      
      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice
      
      console.log('🎵 PWA: User choice:', outcome)
      
      if (outcome === 'accepted') {
        setInstallStep('success')
        onInstall?.()
      } else {
        handleDismiss()
      }
      
    } catch (error) {
      console.error('🎵 PWA: Install prompt error:', error)
    } finally {
      setIsInstalling(false)
      setDeferredPrompt(null)
    }
  }

  const handleDismiss = () => {
    setIsVisible(false)
    
    // Remember user dismissed
    localStorage.setItem('tunami-install-dismissed', Date.now().toString())
    
    onDismiss?.()
  }

  const getDeviceIcon = () => {
    if (isMobile) return <Smartphone className="w-6 h-6" />
    if (isTablet) return <Smartphone className="w-6 h-6" />
    return <Monitor className="w-6 h-6" />
  }

  const getDeviceInstructions = () => {
    if (isMobile) {
      if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
        return (
          <div className="space-y-3">
            <p className="text-sm text-gray-600">On iOS Safari:</p>
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
              <li>Tap the Share button <Share className="w-4 h-4 inline" /></li>
              <li>Scroll down and tap "Add to Home Screen"</li>
              <li>Tap "Add" to confirm</li>
            </ol>
          </div>
        )
      } else {
        return (
          <div className="space-y-3">
            <p className="text-sm text-gray-600">On Android Chrome:</p>
            <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
              <li>Tap the menu (⋮) in your browser</li>
              <li>Tap "Add to Home screen"</li>
              <li>Tap "Add" to confirm</li>
            </ol>
          </div>
        )
      }
    }
    
    return (
      <div className="space-y-3">
        <p className="text-sm text-gray-600">On Desktop:</p>
        <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
          <li>Look for the install icon in your address bar</li>
          <li>Or use your browser's menu → "Install Tunami"</li>
          <li>Click "Install" to add to your desktop</li>
        </ol>
      </div>
    )
  }

  const getInstallBenefits = () => [
    'Faster loading and better performance',
    'Works offline with cached music',
    'Native-like experience',
    'Quick access from home screen',
    'Push notifications for new music',
    'Less battery usage'
  ]

  if (!isVisible || isAppInstalled()) {
    return null
  }

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm ${className}`}>
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6 text-white relative">
          <button
            onClick={handleDismiss}
            className="absolute top-4 right-4 p-1 rounded-full hover:bg-white/20 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <span className="text-2xl">🎵</span>
            </div>
            <div>
              <h2 className="text-xl font-bold">Install Tunami</h2>
              <p className="text-white/80 text-sm">Get the best music experience</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {installStep === 'prompt' && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  {getDeviceIcon()}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Add Tunami to your {isMobile ? 'home screen' : 'desktop'}
                </h3>
                <p className="text-gray-600 text-sm">
                  Install our app for a better music experience with offline access and faster loading.
                </p>
              </div>

              {/* Benefits */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Why install?</h4>
                <ul className="space-y-2">
                  {getInstallBenefits().slice(0, 3).map((benefit, index) => (
                    <li key={index} className="flex items-center space-x-2 text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-purple-500 rounded-full flex-shrink-0" />
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={handleInstall}
                  disabled={isInstalling}
                  className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>{isInstalling ? 'Installing...' : 'Install App'}</span>
                </button>
                <button
                  onClick={handleDismiss}
                  className="px-4 py-3 text-gray-600 font-medium rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Not now
                </button>
              </div>
            </div>
          )}

          {installStep === 'instructions' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Manual Installation
                </h3>
                <p className="text-gray-600 text-sm">
                  Follow these steps to add Tunami to your device:
                </p>
              </div>

              {getDeviceInstructions()}

              <button
                onClick={() => setInstallStep('prompt')}
                className="w-full px-4 py-3 text-purple-600 font-medium rounded-lg hover:bg-purple-50 transition-colors"
              >
                Back
              </button>
            </div>
          )}

          {installStep === 'success' && (
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <span className="text-2xl">✅</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Successfully Installed!
                </h3>
                <p className="text-gray-600 text-sm">
                  Tunami has been added to your device. You can now access it from your home screen.
                </p>
              </div>
              <button
                onClick={() => setIsVisible(false)}
                className="w-full px-4 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors"
              >
                Great!
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Hook for manual control of install prompt
export function usePWAInstall() {
  const [canInstall, setCanInstall] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)

  useEffect(() => {
    const checkInstallStatus = () => {
      const installed = window.matchMedia('(display-mode: standalone)').matches ||
                       (window.navigator as any).standalone ||
                       document.referrer.includes('android-app://')
      setIsInstalled(installed)
    }

    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setCanInstall(true)
    }

    const handleAppInstalled = () => {
      setIsInstalled(true)
      setCanInstall(false)
      setDeferredPrompt(null)
    }

    checkInstallStatus()
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const install = async () => {
    if (!deferredPrompt) return false

    try {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        setCanInstall(false)
        setDeferredPrompt(null)
        return true
      }
      
      return false
    } catch (error) {
      console.error('Install failed:', error)
      return false
    }
  }

  return {
    canInstall,
    isInstalled,
    install
  }
} 