// Tunami Social Features Service
import { createBrowserClient } from '@/lib/supabase'
import type { 
  UserFollow, 
  ActivityFeed, 
  ActivityType, 
  TrackMention, 
  ShareLink, 
  PublicProfile,
  ActivityWithDetails
} from '@/types/database'

const supabase = createBrowserClient()

// =============================================
// FOLLOW/UNFOLLOW FUNCTIONALITY
// =============================================

export async function followUser(targetUserId: string) {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    // Insert follow relationship
    const { error: followError } = await supabase
      .from('user_follows')
      .insert({
        follower_id: user.id,
        following_id: targetUserId
      })

    if (followError) throw followError

    // Update follower count for target user
    const { error: countError } = await supabase.rpc('increment_follower_count', {
      user_id: targetUserId
    })

    if (countError) console.warn('Failed to update follower count:', countError)

    // Update following count for current user
    const { error: followingCountError } = await supabase.rpc('increment_following_count', {
      user_id: user.id
    })

    if (followingCountError) console.warn('Failed to update following count:', followingCountError)

    // Create activity
    await createActivity({
      user_id: user.id,
      activity_type: 'user_follow',
      target_user_id: targetUserId,
      is_public: true
    })

    return { success: true }
  } catch (error) {
    console.error('Error following user:', error)
    return { success: false, error }
  }
}

export async function unfollowUser(targetUserId: string) {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    // Remove follow relationship
    const { error: unfollowError } = await supabase
      .from('user_follows')
      .delete()
      .match({ 
        follower_id: user.id, 
        following_id: targetUserId 
      })

    if (unfollowError) throw unfollowError

    // Update follower count for target user
    const { error: countError } = await supabase.rpc('decrement_follower_count', {
      user_id: targetUserId
    })

    if (countError) console.warn('Failed to update follower count:', countError)

    // Update following count for current user
    const { error: followingCountError } = await supabase.rpc('decrement_following_count', {
      user_id: user.id
    })

    if (followingCountError) console.warn('Failed to update following count:', followingCountError)

    return { success: true }
  } catch (error) {
    console.error('Error unfollowing user:', error)
    return { success: false, error }
  }
}

export async function getFollowStatus(targetUserId: string) {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return { isFollowing: false, isFollowedBy: false }

    // Check if current user follows target
    const { data: following } = await supabase
      .from('user_follows')
      .select('id')
      .match({ 
        follower_id: user.id, 
        following_id: targetUserId 
      })
      .single()

    // Check if target follows current user
    const { data: followedBy } = await supabase
      .from('user_follows')
      .select('id')
      .match({ 
        follower_id: targetUserId, 
        following_id: user.id 
      })
      .single()

    return {
      isFollowing: !!following,
      isFollowedBy: !!followedBy
    }
  } catch (error) {
    console.error('Error getting follow status:', error)
    return { isFollowing: false, isFollowedBy: false }
  }
}

export async function getUserFollowers(userId: string, page = 0, limit = 20) {
  try {
    const { data, error } = await supabase
      .from('user_follows')
      .select(`
        follower_id,
        created_at,
        profiles!user_follows_follower_id_fkey (
          id,
          username,
          full_name,
          avatar_url,
          is_public,
          followers_count
        )
      `)
      .eq('following_id', userId)
      .order('created_at', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error('Error getting followers:', error)
    return { data: null, error }
  }
}

export async function getUserFollowing(userId: string, page = 0, limit = 20) {
  try {
    const { data, error } = await supabase
      .from('user_follows')
      .select(`
        following_id,
        created_at,
        profiles!user_follows_following_id_fkey (
          id,
          username,
          full_name,
          avatar_url,
          is_public,
          followers_count
        )
      `)
      .eq('follower_id', userId)
      .order('created_at', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error('Error getting following:', error)
    return { data: null, error }
  }
}

// =============================================
// PUBLIC PROFILES & USER DIRECTORY
// =============================================

export async function getPublicProfile(userId: string): Promise<PublicProfile | null> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .eq('is_public', true)
      .single()

    if (error || !data) return null

    // Get follow status
    const followStatus = await getFollowStatus(userId)

    return {
      ...data,
      is_following: followStatus.isFollowing,
      is_followed_by: followStatus.isFollowedBy
    }
  } catch (error) {
    console.error('Error getting public profile:', error)
    return null
  }
}

export async function searchPublicUsers(query: string, page = 0, limit = 20) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, username, full_name, avatar_url, bio, followers_count, is_public')
      .eq('is_public', true)
      .or(`username.ilike.%${query}%, full_name.ilike.%${query}%`)
      .order('followers_count', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error('Error searching users:', error)
    return { data: null, error }
  }
}

export async function getPopularUsers(page = 0, limit = 20) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, username, full_name, avatar_url, bio, followers_count')
      .eq('is_public', true)
      .gt('followers_count', 0)
      .order('followers_count', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error('Error getting popular users:', error)
    return { data: null, error }
  }
}

export async function getPublicPlaylists(userId: string, page = 0, limit = 20) {
  try {
    const { data, error } = await supabase
      .from('playlists')
      .select(`
        *,
        profiles!playlists_user_id_fkey (
          username,
          full_name,
          avatar_url
        )
      `)
      .eq('user_id', userId)
      .eq('is_public', true)
      .order('created_at', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (error) throw error
    return { data, error: null }
  } catch (error) {
    console.error('Error getting public playlists:', error)
    return { data: null, error }
  }
}

// =============================================
// ACTIVITY FEED
// =============================================

export async function createActivity(activity: Omit<ActivityFeed, 'id' | 'created_at'>) {
  try {
    const { error } = await supabase
      .from('activity_feed')
      .insert(activity)

    if (error) throw error
    return { success: true }
  } catch (error) {
    console.error('Error creating activity:', error)
    return { success: false, error }
  }
}

export async function getFollowingActivityFeed(page = 0, limit = 20): Promise<ActivityWithDetails[]> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return []

    // Get activities from users the current user follows
    const { data, error } = await supabase
      .from('activity_feed')
      .select(`
        *,
        profiles!activity_feed_user_id_fkey (
          username,
          full_name,
          avatar_url
        ),
        target_profiles:profiles!activity_feed_target_user_id_fkey (
          username,
          full_name
        ),
        tracks (
          title
        ),
        playlists (
          name
        )
      `)
      .in('user_id', 
        supabase
          .from('user_follows')
          .select('following_id')
          .eq('follower_id', user.id)
      )
      .eq('is_public', true)
      .order('created_at', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (error) throw error

    return data?.map((activity: any) => ({
      ...activity,
      user_name: activity.profiles?.full_name,
      user_username: activity.profiles?.username,
      user_avatar_url: activity.profiles?.avatar_url,
      target_user_name: activity.target_profiles?.full_name,
      target_user_username: activity.target_profiles?.username,
      track_title: activity.tracks?.title,
      playlist_name: activity.playlists?.name
    })) || []
  } catch (error) {
    console.error('Error getting activity feed:', error)
    return []
  }
}

export async function getUserActivity(userId: string, page = 0, limit = 20): Promise<ActivityWithDetails[]> {
  try {
    const { data, error } = await supabase
      .from('activity_feed')
      .select(`
        *,
        profiles!activity_feed_user_id_fkey (
          username,
          full_name,
          avatar_url
        ),
        target_profiles:profiles!activity_feed_target_user_id_fkey (
          username,
          full_name
        ),
        tracks (
          title
        ),
        playlists (
          name
        )
      `)
      .eq('user_id', userId)
      .eq('is_public', true)
      .order('created_at', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (error) throw error

    return data?.map((activity: any) => ({
      ...activity,
      user_name: activity.profiles?.full_name,
      user_username: activity.profiles?.username,
      user_avatar_url: activity.profiles?.avatar_url,
      target_user_name: activity.target_profiles?.full_name,
      target_user_username: activity.target_profiles?.username,
      track_title: activity.tracks?.title,
      playlist_name: activity.playlists?.name
    })) || []
  } catch (error) {
    console.error('Error getting user activity:', error)
    return []
  }
}

// =============================================
// TRACK/PLAYLIST SHARING
// =============================================

export async function createShareLink(
  type: 'track' | 'playlist',
  targetId: string,
  expiresInDays?: number
): Promise<string | null> {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    // Generate unique share code
    const shareCode = Math.random().toString(36).substring(2, 14)
    
    const expiresAt = expiresInDays 
      ? new Date(Date.now() + (expiresInDays * 24 * 60 * 60 * 1000)).toISOString()
      : null

    const insertData = {
      share_code: shareCode,
      user_id: user.id,
      expires_at: expiresAt,
      ...(type === 'track' ? { track_id: targetId } : { playlist_id: targetId })
    }

    const { error } = await supabase
      .from('share_links')
      .insert(insertData)

    if (error) throw error

    // Create activity
    await createActivity({
      user_id: user.id,
      activity_type: type === 'track' ? 'track_like' : 'playlist_share',
      track_id: type === 'track' ? targetId : null,
      playlist_id: type === 'playlist' ? targetId : null,
      is_public: true
    })

    return shareCode
  } catch (error) {
    console.error('Error creating share link:', error)
    return null
  }
}

export async function getSharedContent(shareCode: string) {
  try {
    const { data: shareLink, error } = await supabase
      .from('share_links')
      .select(`
        *,
        tracks (
          *,
          profiles!tracks_uploaded_by_fkey (
            username,
            full_name,
            avatar_url
          )
        ),
        playlists (
          *,
          profiles!playlists_user_id_fkey (
            username,
            full_name,
            avatar_url
          )
        )
      `)
      .eq('share_code', shareCode)
      .eq('is_active', true)
      .single()

    if (error || !shareLink) return null

    // Check if expired
    if (shareLink.expires_at && new Date(shareLink.expires_at) < new Date()) {
      return null
    }

    // Increment access count
    await supabase
      .from('share_links')
      .update({ access_count: shareLink.access_count + 1 })
      .eq('id', shareLink.id)

    return shareLink
  } catch (error) {
    console.error('Error getting shared content:', error)
    return null
  }
}

// =============================================
// USER MENTIONS
// =============================================

export async function extractMentions(text: string): Promise<string[]> {
  const mentionRegex = /@(\w+)/g
  const mentions = []
  let match

  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1])
  }

  return mentions
}

export async function saveMentions(trackId: string, description: string) {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    const mentions = await extractMentions(description)
    if (mentions.length === 0) return

    // Get user IDs for mentioned usernames
    const { data: users } = await supabase
      .from('profiles')
      .select('id, username')
      .in('username', mentions)

    if (!users || users.length === 0) return

    // Insert mentions
    const mentionData = users.map((mentionedUser, index) => ({
      track_id: trackId,
      mentioned_user_id: mentionedUser.id,
      mentioned_by: user.id,
      position: description.indexOf(`@${mentionedUser.username}`)
    }))

    const { error } = await supabase
      .from('track_mentions')
      .insert(mentionData)

    if (error) console.warn('Error saving mentions:', error)

    // Update mentions count
    await supabase
      .from('tracks')
      .update({ mentions_count: mentions.length })
      .eq('id', trackId)

    // Create activities for mentions
    for (const mentionedUser of users) {
      await createActivity({
        user_id: user.id,
        activity_type: 'track_mention',
        target_user_id: mentionedUser.id,
        track_id: trackId,
        is_public: true
      })
    }
  } catch (error) {
    console.error('Error saving mentions:', error)
  }
}

export async function getTrackMentions(trackId: string) {
  try {
    const { data, error } = await supabase
      .from('track_mentions')
      .select(`
        *,
        profiles!track_mentions_mentioned_user_id_fkey (
          username,
          full_name,
          avatar_url
        )
      `)
      .eq('track_id', trackId)

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error getting track mentions:', error)
    return []
  }
} 