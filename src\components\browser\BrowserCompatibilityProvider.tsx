'use client'

import React, { createContext, useContext, ReactNode } from 'react'

interface BrowserCompatibilityContextType {
  isLoading: boolean
  needsUserInteraction: boolean
  enableUserInteraction: () => void
}

const BrowserCompatibilityContext = createContext<BrowserCompatibilityContextType | null>(null)

export function useBrowserCompatibility() {
  const context = useContext(BrowserCompatibilityContext)
  if (!context) {
    throw new Error('useBrowserCompatibility must be used within BrowserCompatibilityProvider')
  }
  return context
}

interface BrowserCompatibilityProviderProps {
  children: ReactNode
}

export function BrowserCompatibilityProvider({ children }: BrowserCompatibilityProviderProps) {
  const enableUserInteraction = () => {
    // Simple audio context unlock for modern browsers
    if (window.AudioContext || (window as any).webkitAudioContext) {
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext
      const context = new AudioContext()
      
      if (context.state === 'suspended') {
        context.resume().catch(() => {
          console.warn('Failed to resume audio context')
        })
      }
    }
  }

  const contextValue: BrowserCompatibilityContextType = {
    isLoading: false,
    needsUserInteraction: false,
    enableUserInteraction
  }

  return (
    <BrowserCompatibilityContext.Provider value={contextValue}>
      {children}
    </BrowserCompatibilityContext.Provider>
  )
}

export default BrowserCompatibilityProvider 