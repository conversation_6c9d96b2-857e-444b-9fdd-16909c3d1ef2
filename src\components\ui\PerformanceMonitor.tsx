'use client'

import React, { useState, useEffect } from 'react'
import { Activity, Database, Zap, Wifi, Image, Disc } from 'lucide-react'
import { dbOptimizer } from '@/lib/database-optimizer'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'

interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number | null // Largest Contentful Paint
  fid: number | null // First Input Delay
  cls: number | null // Cumulative Layout Shift
  fcp: number | null // First Contentful Paint
  ttfb: number | null // Time to First Byte
  
  // App-specific metrics
  memoryUsage: number
  dbCacheHitRate: number
  dbCacheSize: number
  imageLoadTime: number
  audioLoadTime: number
  
  // Network metrics
  connectionType: string
  downlink: number
  isOnline: boolean
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    lcp: null,
    fid: null,
    cls: null,
    fcp: null,
    ttfb: null,
    memoryUsage: 0,
    dbCacheHitRate: 0,
    dbCacheSize: 0,
    imageLoadTime: 0,
    audioLoadTime: 0,
    connectionType: 'unknown',
    downlink: 0,
    isOnline: true
  })

  const [isVisible, setIsVisible] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const { isOnline, effectiveType, downlink } = useNetworkStatus()

  // Collect Core Web Vitals
  useEffect(() => {
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
      return
    }

    // Performance Observer for Core Web Vitals
    if ('PerformanceObserver' in window) {
      // LCP (Largest Contentful Paint)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }))
      })

      // FCP (First Contentful Paint)
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint') as any
        if (fcpEntry) {
          setMetrics(prev => ({ ...prev, fcp: fcpEntry.startTime }))
        }
      })

      // CLS (Cumulative Layout Shift)
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value
          }
        }
        setMetrics(prev => ({ ...prev, cls: clsValue }))
      })

      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        fcpObserver.observe({ entryTypes: ['paint'] })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (error) {
        console.warn('Some performance observers not supported:', error)
      }

      return () => {
        lcpObserver.disconnect()
        fcpObserver.disconnect()
        clsObserver.disconnect()
      }
    }
  }, [])

  // Collect app-specific metrics
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') {
      setIsVisible(false)
      return
    }

    setIsVisible(true)

    const interval = setInterval(() => {
      // Memory usage
      const memoryInfo = (performance as any).memory
      const memoryUsage = memoryInfo ? (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100 : 0

      // Database cache stats
      const cacheStats = dbOptimizer.getCacheStats()

      // Network metrics
      const connection = (navigator as any).connection

      setMetrics(prev => ({
        ...prev,
        memoryUsage,
        dbCacheHitRate: cacheStats.hitRate,
        dbCacheSize: cacheStats.size,
        connectionType: effectiveType || 'unknown',
        downlink: downlink || 0,
        isOnline
      }))
    }, 2000)

    return () => clearInterval(interval)
  }, [effectiveType, downlink, isOnline])

  // Navigation timing for TTFB
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        setMetrics(prev => ({
          ...prev,
          ttfb: navigation.responseStart - navigation.requestStart
        }))
      }
    }
  }, [])

  if (!isVisible) {
    return null
  }

  const getMetricColor = (value: number, thresholds: { good: number; fair: number }) => {
    if (value <= thresholds.good) return 'text-green-400'
    if (value <= thresholds.fair) return 'text-yellow-400'
    return 'text-red-400'
  }

  const formatMs = (ms: number | null) => {
    if (ms === null) return '-'
    return `${ms.toFixed(0)}ms`
  }

  const formatMB = (bytes: number) => {
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 max-w-sm">
      <div className="bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl overflow-hidden">
        {/* Header */}
        <div 
          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-800/50 transition-colors"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center gap-2">
            <Activity className="w-4 h-4 text-blue-400" />
            <span className="text-sm font-medium text-gray-300">Performance</span>
          </div>
          <div className="flex items-center gap-2">
            {/* Overall status indicator */}
            <div className={`w-2 h-2 rounded-full ${
              metrics.memoryUsage < 70 && (metrics.lcp || 0) < 2500 ? 'bg-green-400' :
              metrics.memoryUsage < 85 && (metrics.lcp || 0) < 4000 ? 'bg-yellow-400' :
              'bg-red-400'
            } animate-pulse`} />
            <span className="text-xs text-gray-400">
              {isExpanded ? '▼' : '▶'}
            </span>
          </div>
        </div>

        {/* Expanded metrics */}
        {isExpanded && (
          <div className="p-3 border-t border-gray-700 space-y-3">
            {/* Core Web Vitals */}
            <div>
              <h4 className="text-xs font-medium text-gray-400 mb-2 flex items-center gap-1">
                <Zap className="w-3 h-3" />
                Core Web Vitals
              </h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-400">LCP:</span>
                  <span className={getMetricColor(metrics.lcp || 0, { good: 2500, fair: 4000 })}>
                    {formatMs(metrics.lcp)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">FCP:</span>
                  <span className={getMetricColor(metrics.fcp || 0, { good: 1800, fair: 3000 })}>
                    {formatMs(metrics.fcp)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">CLS:</span>
                  <span className={getMetricColor((metrics.cls || 0) * 1000, { good: 100, fair: 250 })}>
                    {(metrics.cls || 0).toFixed(3)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">TTFB:</span>
                  <span className={getMetricColor(metrics.ttfb || 0, { good: 800, fair: 1800 })}>
                    {formatMs(metrics.ttfb)}
                  </span>
                </div>
              </div>
            </div>

            {/* Memory & Cache */}
            <div>
              <h4 className="text-xs font-medium text-gray-400 mb-2 flex items-center gap-1">
                <Database className="w-3 h-3" />
                Memory & Cache
              </h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-400">Memory:</span>
                  <span className={getMetricColor(metrics.memoryUsage, { good: 70, fair: 85 })}>
                    {metrics.memoryUsage.toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">DB Cache:</span>
                  <span className="text-gray-300">{metrics.dbCacheSize} items</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Hit Rate:</span>
                  <span className={getMetricColor(100 - metrics.dbCacheHitRate, { good: 20, fair: 40 })}>
                    {metrics.dbCacheHitRate.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Network */}
            <div>
              <h4 className="text-xs font-medium text-gray-400 mb-2 flex items-center gap-1">
                <Wifi className="w-3 h-3" />
                Network
              </h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-400">Status:</span>
                  <span className={metrics.isOnline ? 'text-green-400' : 'text-red-400'}>
                    {metrics.isOnline ? 'Online' : 'Offline'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Type:</span>
                  <span className="text-gray-300">{metrics.connectionType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Speed:</span>
                  <span className="text-gray-300">{metrics.downlink.toFixed(1)} Mbps</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="pt-2 border-t border-gray-700">
              <div className="flex gap-2">
                <button
                  onClick={() => dbOptimizer.clearCache()}
                  className="flex-1 text-xs px-2 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
                >
                  Clear Cache
                </button>
                <button
                  onClick={() => {
                    if (typeof window !== 'undefined') {
                      window.location.reload()
                    }
                  }}
                  className="flex-1 text-xs px-2 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
                >
                  Reload
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PerformanceMonitor 