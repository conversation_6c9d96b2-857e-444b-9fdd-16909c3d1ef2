import { supabase } from './supabase'
import type { UploadFile } from '@/components/upload/FileUpload'
import type { UploadMetadata } from '@/components/upload/UploadForm'
import { parseBlob } from 'music-metadata'
import { UploadMetadata, UploadProgress, UploadResult, UploadValidation } from '@/types/upload'

export interface UploadProgress {
  fileId: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
  url?: string
  publicUrl?: string
}

export interface UploadResult {
  fileId: string
  success: boolean
  url?: string
  publicUrl?: string
  error?: string
  trackId?: string
}

interface AudioMetadata {
  duration?: number
  bitrate?: number
  sampleRate?: number
  format?: string
  title?: string
  artist?: string
  genre?: string[]
  year?: number
}

export class UploadService {
  private readonly BUCKET_NAME = 'audio-tracks'
  private readonly MAX_RETRY_ATTEMPTS = 3
  private static readonly MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB
  private static readonly ALLOWED_TYPES = ['audio/mp3', 'audio/wav', 'audio/flac', 'audio/mpeg', 'audio/ogg']
  private static readonly CHUNK_SIZE = 5 * 1024 * 1024 // 5MB chunks for large files

  // Map form AI tool values to database enum values
  private mapAiTool(aiTool: string): string {
    const mapping: Record<string, string> = {
      'suno': 'suno',
      'udio': 'udio',
      'musicgen': 'other', // Not in DB enum, map to other
      'stable-audio': 'other', // Not in DB enum, map to other
      'soundraw': 'soundraw',
      'mubert': 'mubert',
      'aiva': 'aiva',
      'beatoven': 'other', // Not in DB enum, map to other
      'custom': 'custom',
      'other': 'other'
    }
    return mapping[aiTool] || 'other'
  }

  // Map form genre values to database enum values
  private mapGenre(genre: string): string {
    const mapping: Record<string, string> = {
      'electronic': 'electronic',
      'pop': 'pop',
      'hip hop': 'hip_hop',
      'rock': 'rock',
      'jazz': 'jazz',
      'classical': 'classical',
      'folk': 'folk',
      'country': 'country',
      'r&b/soul': 'r_and_b',
      'reggae': 'reggae',
      'blues': 'blues',
      'punk': 'punk',
      'metal': 'metal',
      'ambient': 'ambient',
      'house': 'electronic', // Map to electronic since house not in enum
      'techno': 'electronic', // Map to electronic since techno not in enum
      'trance': 'electronic', // Map to electronic since trance not in enum
      'dubstep': 'electronic', // Map to electronic since dubstep not in enum
      'indie': 'indie',
      'alternative': 'other', // Not in DB enum, map to other
      'world': 'world',
      'instrumental': 'other', // Not in DB enum, map to other
      'soundtrack': 'other', // Not in DB enum, map to other
      'lo-fi': 'other', // Not in DB enum, map to other
      'experimental': 'experimental',
      'other': 'other'
    }
    return mapping[genre.toLowerCase()] || 'other'
  }

  // Extract audio metadata from file
  private async extractAudioMetadata(file: File): Promise<AudioMetadata> {
    try {
      const metadata = await parseBlob(file)
      
      return {
        duration: metadata.format.duration ? Math.round(metadata.format.duration) : undefined,
        bitrate: metadata.format.bitrate,
        sampleRate: metadata.format.sampleRate,
        format: metadata.format.container,
        title: metadata.common.title,
        artist: metadata.common.artist,
        genre: metadata.common.genre,
        year: metadata.common.year
      }
    } catch (error) {
      console.warn('Failed to extract audio metadata:', error)
      return {}
    }
  }

  async initializeBucket(): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await supabase
        .storage
        .listBuckets()

      if (listError) {
        console.warn('Could not list buckets:', listError.message)
        return
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME)

      if (!bucketExists) {
        // Create bucket if it doesn't exist
        const { error: createError } = await supabase
          .storage
          .createBucket(this.BUCKET_NAME, {
            public: true,
            allowedMimeTypes: ['audio/mpeg', 'audio/wav', 'audio/mp3'],
            fileSizeLimit: 50 * 1024 * 1024 // 50MB
          })

        if (createError) {
          console.warn('Could not create bucket:', createError.message)
        }
      }
    } catch (error) {
      console.warn('Bucket initialization failed:', error)
    }
  }

  async uploadFile(
    file: UploadFile,
    metadata: UploadMetadata,
    userId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    const progressCallback = (progress: number, status: UploadProgress['status'], error?: string) => {
      if (onProgress) {
        onProgress({
          fileId: file.id,
          progress,
          status,
          error
        })
      }
    }

    try {
      // Validate metadata
      if (!metadata.title?.trim()) {
        throw new Error('Track title is required')
      }
      if (!metadata.aiTool) {
        throw new Error('AI tool selection is required')
      }
      if (!metadata.genre) {
        throw new Error('Genre selection is required')
      }

      // Initialize bucket
      await this.initializeBucket()

      progressCallback(10, 'uploading')

      // Extract audio metadata
      console.log('Extracting audio metadata...')
      const audioMetadata = await this.extractAudioMetadata(file.file)
      console.log('Extracted metadata:', audioMetadata)

      progressCallback(20, 'uploading')

      // Generate unique filename
      const timestamp = Date.now()
      const cleanFileName = file.file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
      const fileName = `${userId}/${timestamp}_${cleanFileName}`

      progressCallback(30, 'uploading')

      // Upload file to Supabase Storage with retry logic
      let uploadResult
      let retryCount = 0

      while (retryCount < this.MAX_RETRY_ATTEMPTS) {
        const { data, error } = await supabase.storage
          .from(this.BUCKET_NAME)
          .upload(fileName, file.file, {
            cacheControl: '3600',
            upsert: false
          })

        if (error) {
          retryCount++
          if (retryCount >= this.MAX_RETRY_ATTEMPTS) {
            throw new Error(`Upload failed after ${this.MAX_RETRY_ATTEMPTS} attempts: ${error.message}`)
          }
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
          continue
        }

        uploadResult = data
        break
      }

      if (!uploadResult) {
        throw new Error('Upload failed: No result returned')
      }

      progressCallback(60, 'uploading')

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(fileName)

      progressCallback(80, 'uploading')

      // Create database record with extracted metadata
      const enhancedParameters = {
        ...metadata.tags.length > 0 ? { tags: metadata.tags } : {},
        audioMetadata: {
          bitrate: audioMetadata.bitrate,
          sampleRate: audioMetadata.sampleRate,
          format: audioMetadata.format,
          ...(audioMetadata.title && audioMetadata.title !== metadata.title ? { originalTitle: audioMetadata.title } : {}),
          ...(audioMetadata.artist && audioMetadata.artist !== metadata.artist ? { originalArtist: audioMetadata.artist } : {}),
          ...(audioMetadata.genre && audioMetadata.genre.length > 0 ? { originalGenre: audioMetadata.genre } : {}),
          ...(audioMetadata.year ? { year: audioMetadata.year } : {})
        }
      }

      const trackData = {
        title: metadata.title,
        artist_name: metadata.artist?.trim() || 'Unknown Artist',
        ai_tool: this.mapAiTool(metadata.aiTool),
        genre: this.mapGenre(metadata.genre),
        ai_prompt: metadata.description || null, // Use description as AI prompt
        file_url: urlData.publicUrl,
        file_path: fileName,
        file_size: file.file.size,
        duration: audioMetadata.duration,
        mood: null, // Not provided in current form
        tempo: null, // Not provided in current form
        key_signature: null, // Not provided in current form
        ai_parameters: Object.keys(enhancedParameters).length > 0 ? enhancedParameters : null,
        is_public: metadata.isPublic,
        is_featured: false,
        play_count: 0,
        like_count: 0,
        upload_status: 'completed',
        uploaded_by: userId
      }

      console.log('Inserting track data:', trackData)

      const { data: track, error: dbError } = await supabase
        .from('tracks')
        .insert(trackData)
        .select()
        .single()

      if (dbError) {
        console.error('Database insert error:', dbError)
        console.error('Error details:', {
          message: dbError.message,
          details: dbError.details,
          hint: dbError.hint,
          code: dbError.code
        })
        // Clean up uploaded file if database insert fails
        await this.deleteFile(fileName)
        throw new Error(`Database error: ${dbError.message}`)
      }

      console.log('Track inserted successfully:', track)

      progressCallback(100, 'success')

      return {
        fileId: file.id,
        success: true,
        url: urlData.publicUrl,
        publicUrl: urlData.publicUrl,
        trackId: track.id
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      progressCallback(0, 'error', errorMessage)

      return {
        fileId: file.id,
        success: false,
        error: errorMessage
      }
    }
  }

  async uploadFiles(
    files: UploadFile[],
    metadataArray: UploadMetadata[],
    userId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = []

    // Upload files sequentially to avoid overwhelming the server
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const metadata = metadataArray[i] || metadataArray[0] // Use first metadata if array is shorter

      const result = await this.uploadFile(file, metadata, userId, onProgress)
      results.push(result)

      // Small delay between uploads
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    return results
  }

  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath])

      if (error) {
        console.error('Error deleting file:', error.message)
        return false
      }

      return true
    } catch (error) {
      console.error('Error deleting file:', error)
      return false
    }
  }

  async getUserUploadStats(userId: string): Promise<{
    totalTracks: number
    totalSize: number
    publicTracks: number
    privateTracks: number
  }> {
    try {
      const { data: tracks, error } = await supabase
        .from('tracks')
        .select('file_size, is_public')
        .eq('user_id', userId)

      if (error) {
        throw new Error(`Failed to get user stats: ${error.message}`)
      }

      const stats = tracks?.reduce((acc, track) => {
        acc.totalTracks++
        acc.totalSize += track.file_size || 0
        if (track.is_public) {
          acc.publicTracks++
        } else {
          acc.privateTracks++
        }
        return acc
      }, {
        totalTracks: 0,
        totalSize: 0,
        publicTracks: 0,
        privateTracks: 0
      }) || {
        totalTracks: 0,
        totalSize: 0,
        publicTracks: 0,
        privateTracks: 0
      }

      return stats
    } catch (error) {
      console.error('Error getting user upload stats:', error)
      return {
        totalTracks: 0,
        totalSize: 0,
        publicTracks: 0,
        privateTracks: 0
      }
    }
  }

  static async validateFile(file: File): Promise<UploadValidation> {
    const validation: UploadValidation = {
      isValid: true,
      errors: [],
      warnings: []
    }

    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      validation.isValid = false
      validation.errors.push(`File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${this.formatFileSize(this.MAX_FILE_SIZE)})`)
    }

    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      validation.isValid = false
      validation.errors.push(`File type ${file.type} is not supported. Allowed types: ${this.ALLOWED_TYPES.join(', ')}`)
    }

    // Check file name
    if (file.name.length > 255) {
      validation.isValid = false
      validation.errors.push('File name is too long (maximum 255 characters)')
    }

    // Add warnings for large files
    if (file.size > 50 * 1024 * 1024) { // 50MB
      validation.warnings.push('Large files may take longer to upload and process')
    }

    // Try to get basic audio metadata
    try {
      const audioContext = new AudioContext()
      const arrayBuffer = await file.arrayBuffer()
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
      
      validation.fileInfo = {
        format: file.type,
        duration: audioBuffer.duration,
        bitrate: 0, // Would need additional analysis
        sampleRate: audioBuffer.sampleRate,
        channels: audioBuffer.numberOfChannels,
        size: file.size
      }

      audioContext.close()

      // Validate duration
      if (audioBuffer.duration < 10) {
        validation.warnings.push('Track is very short (less than 10 seconds)')
      }
      if (audioBuffer.duration > 600) { // 10 minutes
        validation.warnings.push('Track is very long (over 10 minutes)')
      }
    } catch (error) {
      validation.warnings.push('Could not analyze audio metadata')
    }

    return validation
  }

  static async uploadTrack(
    file: File,
    metadata: UploadMetadata,
    onProgress: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Validate file first
      const validation = await this.validateFile(file)
      if (!validation.isValid) {
        return {
          success: false,
          fileName: file.name,
          error: validation.errors.join('; ')
        }
      }

      // Stage 1: Upload file
      onProgress({
        stage: 'uploading',
        percentage: 0
      })

      const fileName = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`
      const filePath = `tracks/${fileName}`

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('audio-files')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`)
      }

      onProgress({
        stage: 'uploading',
        percentage: 50
      })

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('audio-files')
        .getPublicUrl(filePath)

      onProgress({
        stage: 'processing',
        percentage: 70
      })

      // Stage 2: Save track metadata to database
      onProgress({
        stage: 'metadata',
        percentage: 80
      })

      const { data: trackData, error: dbError } = await supabase
        .from('tracks')
        .insert({
          title: metadata.title,
          artist: metadata.artist,
          album: metadata.album,
          genre: metadata.genre,
          description: metadata.description,
          ai_tool: metadata.aiTool,
          is_public: metadata.isPublic,
          file_url: publicUrl,
          file_path: filePath,
          file_size: file.size,
          duration: validation.fileInfo?.duration,
          bitrate: validation.fileInfo?.bitrate,
          sample_rate: validation.fileInfo?.sampleRate,
          channels: validation.fileInfo?.channels,
          file_type: file.type,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (dbError) {
        // Clean up uploaded file on database error
        await supabase.storage
          .from('audio-files')
          .remove([filePath])
        
        throw new Error(`Database error: ${dbError.message}`)
      }

      // Stage 3: Process tags
      if (metadata.tags.length > 0) {
        const tagInserts = metadata.tags.map(tag => ({
          track_id: trackData.id,
          tag_name: tag.toLowerCase().trim()
        }))

        await supabase
          .from('track_tags')
          .insert(tagInserts)
      }

      onProgress({
        stage: 'complete',
        percentage: 100
      })

      return {
        success: true,
        trackId: trackData.id,
        fileName: file.name,
        url: publicUrl,
        metadata: {
          duration: validation.fileInfo?.duration,
          bitrate: validation.fileInfo?.bitrate,
          sampleRate: validation.fileInfo?.sampleRate,
          channels: validation.fileInfo?.channels,
          format: file.type
        },
        warnings: validation.warnings
      }

    } catch (error) {
      console.error('Upload failed:', error)
      return {
        success: false,
        fileName: file.name,
        error: error instanceof Error ? error.message : 'Unknown upload error'
      }
    }
  }

  static async uploadCoverArt(file: File, trackId: string): Promise<string | null> {
    try {
      if (!file.type.startsWith('image/')) {
        throw new Error('File must be an image')
      }

      const fileName = `${trackId}-cover-${Date.now()}.${file.name.split('.').pop()}`
      const filePath = `covers/${fileName}`

      const { data, error } = await supabase.storage
        .from('cover-images')
        .upload(filePath, file)

      if (error) throw error

      const { data: { publicUrl } } = supabase.storage
        .from('cover-images')
        .getPublicUrl(filePath)

      // Update track with cover image URL
      await supabase
        .from('tracks')
        .update({ cover_image_url: publicUrl })
        .eq('id', trackId)

      return publicUrl
    } catch (error) {
      console.error('Cover art upload failed:', error)
      return null
    }
  }

  static async checkDuplicate(metadata: UploadMetadata): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('tracks')
        .select('id')
        .eq('title', metadata.title)
        .eq('artist', metadata.artist)
        .limit(1)

      if (error) throw error

      return data && data.length > 0
    } catch (error) {
      console.error('Duplicate check failed:', error)
      return false
    }
  }

  static async getTotalUploadSize(userId: string): Promise<number> {
    try {
      const { data, error } = await supabase
        .from('tracks')
        .select('file_size')
        .eq('user_id', userId)

      if (error) throw error

      return data?.reduce((total, track) => total + (track.file_size || 0), 0) || 0
    } catch (error) {
      console.error('Failed to get upload size:', error)
      return 0
    }
  }

  static async getUploadQuota(userId: string): Promise<{ used: number; limit: number; remaining: number }> {
    try {
      const used = await this.getTotalUploadSize(userId)
      const limit = 5 * 1024 * 1024 * 1024 // 5GB default limit
      
      return {
        used,
        limit,
        remaining: Math.max(0, limit - used)
      }
    } catch (error) {
      console.error('Failed to get upload quota:', error)
      return { used: 0, limit: 0, remaining: 0 }
    }
  }

  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  static getEstimatedUploadTime(fileSize: number, connectionSpeed: number = 1): Promise<number> {
    // connectionSpeed in Mbps, returns time in seconds
    const speedBytesPerSecond = (connectionSpeed * 1024 * 1024) / 8
    const estimatedSeconds = fileSize / speedBytesPerSecond
    
    // Add 20% buffer for processing time
    return Promise.resolve(Math.ceil(estimatedSeconds * 1.2))
  }
}

export { UploadProgress, UploadResult } from '@/types/upload'
export const uploadService = UploadService 