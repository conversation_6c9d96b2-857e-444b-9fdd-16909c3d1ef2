'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  SkipB<PERSON>, 
  SkipFor<PERSON>, 
  Loader2, 
  Music, 
  AlertTriangle,
  RefreshCw,
  Wifi,
  WifiOff
} from 'lucide-react'
import { AudioTrack, AudioPlayerProps } from '@/types/audio'
import { 
  handleAudioError, 
  TunamiError, 
  ErrorType, 
  showErrorToast, 
  showSuccessToast,
  defaultRetryHandler 
} from '@/lib/error-handler'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'

interface EnhancedAudioPlayerProps extends AudioPlayerProps {
  enableRetry?: boolean
  maxRetries?: number
  showNetworkStatus?: boolean
  onError?: (error: TunamiError) => void
  onRetry?: () => void
  onNetworkStatusChange?: (isOnline: boolean) => void
}

interface AudioState {
  isPlaying: boolean
  isLoading: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean
  error: TunamiError | null
  retryCount: number
  isOnline: boolean
  loadAttempts: number
}

export default function EnhancedAudioPlayer({
  track,
  autoPlay = false,
  showControls = true,
  showTrackInfo = true,
  showVolumeControl = true,
  showSeekBar = true,
  showSkipButtons = false,
  enableKeyboardShortcuts = true,
  enableRetry = true,
  maxRetries = 3,
  showNetworkStatus = true,
  hasNext = false,
  hasPrevious = false,
  className = '',
  onPlay,
  onPause,
  onEnded,
  onNext,
  onPrevious,
  onTimeUpdate,
  onLoadedMetadata,
  onSeek,
  onVolumeChange,
  onError,
  onRetry,
  onNetworkStatusChange
}: EnhancedAudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null)
  const progressBarRef = useRef<HTMLDivElement>(null)
  const volumeBarRef = useRef<HTMLDivElement>(null)
  const retryTimeoutRef = useRef<NodeJS.Timeout>()
  
  const [state, setState] = useState<AudioState>({
    isPlaying: false,
    isLoading: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    error: null,
    retryCount: 0,
    isOnline: navigator.onLine,
    loadAttempts: 0
  })

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => {
      setState(prev => ({ ...prev, isOnline: true }))
      onNetworkStatusChange?.(true)
      
      // Auto-retry if we have an error and network comes back
      if (state.error && enableRetry) {
        handleRetryPlayback()
      }
    }

    const handleOffline = () => {
      setState(prev => ({ ...prev, isOnline: false }))
      onNetworkStatusChange?.(false)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [state.error, enableRetry, onNetworkStatusChange])

  // Clear error when track changes
  useEffect(() => {
    setState(prev => ({ 
      ...prev, 
      error: null, 
      retryCount: 0, 
      loadAttempts: 0 
    }))
  }, [track?.id])

  // Enhanced error handling
  const handleAudioErrorEvent = useCallback((event: React.SyntheticEvent<HTMLAudioElement>) => {
    const audio = audioRef.current
    if (!audio) return

    const tunamiError = handleAudioError(event.nativeEvent, audio)
    
    setState(prev => ({ 
      ...prev, 
      error: tunamiError, 
      isLoading: false, 
      isPlaying: false 
    }))

    // Show user-friendly error message
    showErrorToast(tunamiError)
    
    // Call custom error handler
    onError?.(tunamiError)

    // Auto-retry for retryable errors
    if (tunamiError.retryable && enableRetry && state.retryCount < maxRetries) {
      scheduleRetry()
    }
  }, [enableRetry, maxRetries, state.retryCount, onError])

  // Schedule automatic retry
  const scheduleRetry = useCallback(() => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
    }

    const delay = Math.min(1000 * Math.pow(2, state.retryCount), 10000) // Exponential backoff
    
    retryTimeoutRef.current = setTimeout(() => {
      handleRetryPlayback()
    }, delay)
  }, [state.retryCount])

  // Retry playback
  const handleRetryPlayback = useCallback(async () => {
    if (!track || state.retryCount >= maxRetries) return

    setState(prev => ({ 
      ...prev, 
      retryCount: prev.retryCount + 1, 
      error: null, 
      isLoading: true 
    }))

    onRetry?.()

    try {
      await defaultRetryHandler.execute(async () => {
        if (!audioRef.current) throw new Error('Audio element not available')
        
        const audio = audioRef.current
        audio.load()
        
        return new Promise<void>((resolve, reject) => {
          const handleCanPlay = () => {
            audio.removeEventListener('canplay', handleCanPlay)
            audio.removeEventListener('error', handleError)
            resolve()
          }
          
          const handleError = (e: Event) => {
            audio.removeEventListener('canplay', handleCanPlay)
            audio.removeEventListener('error', handleError)
            reject(new Error('Failed to load audio'))
          }
          
          audio.addEventListener('canplay', handleCanPlay)
          audio.addEventListener('error', handleError)
        })
      })

      setState(prev => ({ ...prev, isLoading: false, error: null }))
      showSuccessToast('Audio loaded successfully')
      
      if (autoPlay || state.isPlaying) {
        await handlePlay()
      }
    } catch (error) {
      const tunamiError = handleAudioError(error, audioRef.current || undefined)
      setState(prev => ({ 
        ...prev, 
        error: tunamiError, 
        isLoading: false 
      }))
      showErrorToast(tunamiError)
    }
  }, [track, state.retryCount, maxRetries, autoPlay, state.isPlaying, onRetry])

  // Enhanced play functionality with error handling
  const handlePlay = useCallback(async () => {
    if (!audioRef.current || !track) return
    
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      await audioRef.current.play()
      setState(prev => ({ ...prev, isPlaying: true, isLoading: false }))
      onPlay?.()
    } catch (error) {
      const tunamiError = handleAudioError(error, audioRef.current || undefined)
      setState(prev => ({ 
        ...prev, 
        error: tunamiError, 
        isLoading: false, 
        isPlaying: false 
      }))
      showErrorToast(tunamiError)
      onError?.(tunamiError)
    }
  }, [track, onPlay, onError])

  // Handle track changes with error recovery
  useEffect(() => {
    if (track && audioRef.current) {
      const audio = audioRef.current
      
      setState(prev => ({ 
        ...prev, 
        isPlaying: false,
        currentTime: 0,
        duration: 0,
        isLoading: true,
        error: null,
        loadAttempts: prev.loadAttempts + 1
      }))

      audio.src = track.src
      audio.load()

      if (autoPlay) {
        setTimeout(() => {
          handlePlay()
        }, 100)
      }
    }
  }, [track, autoPlay, handlePlay])

  // Volume control
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = state.isMuted ? 0 : state.volume
    }
  }, [state.volume, state.isMuted])

  // Pause functionality
  const handlePause = useCallback(() => {
    if (!audioRef.current) return
    audioRef.current.pause()
    setState(prev => ({ ...prev, isPlaying: false }))
    onPause?.()
  }, [onPause])

  // Toggle play/pause
  const togglePlayPause = useCallback(() => {
    if (state.error && enableRetry) {
      handleRetryPlayback()
    } else if (state.isPlaying) {
      handlePause()
    } else {
      handlePlay()
    }
  }, [state.isPlaying, state.error, enableRetry, handlePlay, handlePause, handleRetryPlayback])

  // Seek functionality
  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !progressBarRef.current || !state.duration) return

    const rect = progressBarRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    const newTime = percentage * state.duration

    audioRef.current.currentTime = newTime
    setState(prev => ({ ...prev, currentTime: newTime }))
    onSeek?.(newTime)
  }

  // Volume control
  const handleVolumeChange = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!volumeBarRef.current) return

    const rect = volumeBarRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    
    setState(prev => ({ ...prev, volume: percentage, isMuted: false }))
    onVolumeChange?.(percentage)
  }

  // Toggle mute
  const toggleMute = useCallback(() => {
    setState(prev => ({ ...prev, isMuted: !prev.isMuted }))
  }, [])

  // Audio event handlers
  const handleTimeUpdate = () => {
    if (audioRef.current) {
      const time = audioRef.current.currentTime
      setState(prev => ({ ...prev, currentTime: time }))
      onTimeUpdate?.(time)
    }
  }

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      const dur = audioRef.current.duration
      setState(prev => ({ ...prev, duration: dur, isLoading: false }))
      onLoadedMetadata?.(dur)
    }
  }

  const handleEnded = () => {
    setState(prev => ({ ...prev, isPlaying: false, currentTime: 0 }))
    onEnded?.()
  }

  const handleLoadStart = () => {
    setState(prev => ({ ...prev, isLoading: true }))
  }

  const handleCanPlay = () => {
    setState(prev => ({ ...prev, isLoading: false, error: null }))
  }

  // Cleanup
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [])

  // Format time
  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const progressPercentage = state.duration > 0 ? (state.currentTime / state.duration) * 100 : 0

  // Error state UI
  if (state.error) {
    return (
      <div className={`bg-gray-900 rounded-lg border border-red-500 p-4 ${className}`}>
        <div className="flex items-center space-x-3 mb-3">
          <AlertTriangle className="w-6 h-6 text-red-400 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <p className="text-red-300 font-medium text-sm">Audio Error</p>
            <p className="text-gray-400 text-xs truncate">{state.error.userMessage}</p>
          </div>
          {showNetworkStatus && !state.isOnline && (
            <WifiOff className="w-5 h-5 text-red-400" />
          )}
        </div>
        
        {enableRetry && state.retryCount < maxRetries && (
          <button
            onClick={handleRetryPlayback}
            disabled={state.isLoading}
            className="w-full bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white px-3 py-2 rounded text-sm font-medium transition-colors flex items-center justify-center gap-2"
          >
            {state.isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
            {state.isLoading ? 'Retrying...' : `Retry (${maxRetries - state.retryCount} left)`}
          </button>
        )}
      </div>
    )
  }

  // No track state
  if (!track) {
    return (
      <div className={`flex items-center justify-center p-8 text-gray-400 ${className}`}>
        <Music className="w-8 h-8 mr-3" />
        <span>No track selected</span>
      </div>
    )
  }

  return (
    <ComponentErrorBoundary>
      <div className={`bg-gray-900 rounded-lg border border-gray-800 p-4 ${className}`}>
        {/* Hidden HTML5 Audio Element */}
        <audio
          ref={audioRef}
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onEnded={handleEnded}
          onLoadStart={handleLoadStart}
          onCanPlay={handleCanPlay}
          onError={handleAudioErrorEvent}
          preload="metadata"
        />

        {/* Network Status Indicator */}
        {showNetworkStatus && !state.isOnline && (
          <div className="bg-red-900 border border-red-700 rounded-lg p-2 mb-4 flex items-center gap-2">
            <WifiOff className="w-4 h-4 text-red-400" />
            <span className="text-red-300 text-sm">No internet connection</span>
          </div>
        )}

        {/* Track Info Section */}
        {showTrackInfo && (
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <Music className="w-6 h-6 text-white" />
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className="text-white font-medium truncate">{track.title}</h3>
              <p className="text-gray-400 text-sm truncate">{track.artist}</p>
              {track.aiTool && (
                <p className="text-purple-400 text-xs">Generated with {track.aiTool}</p>
              )}
            </div>

            {showNetworkStatus && state.isOnline && (
              <Wifi className="w-4 h-4 text-green-400" />
            )}
          </div>
        )}

        {/* Controls Section */}
        {showControls && (
          <div className="space-y-4">
            {/* Progress Bar */}
            {showSeekBar && (
              <div className="space-y-2">
                <div 
                  ref={progressBarRef}
                  onClick={handleSeek}
                  className="relative w-full h-2 bg-gray-700 rounded-full overflow-hidden cursor-pointer group"
                >
                  <div 
                    className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-200"
                    style={{ width: `${progressPercentage}%` }}
                  />
                  <div className="absolute top-1/2 left-0 w-3 h-3 bg-white rounded-full shadow-md transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                       style={{ left: `${progressPercentage}%`, marginLeft: '-6px' }} />
                </div>
                
                <div className="flex justify-between text-xs text-gray-400">
                  <span>{formatTime(state.currentTime)}</span>
                  <span>{formatTime(state.duration)}</span>
                </div>
              </div>
            )}

            {/* Control Buttons */}
            <div className="flex items-center justify-center space-x-4">
              {showSkipButtons && (
                <button
                  onClick={onPrevious}
                  disabled={!hasPrevious}
                  className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  <SkipBack className="w-5 h-5" />
                </button>
              )}

              <button
                onClick={togglePlayPause}
                disabled={state.isLoading && !state.error}
                className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                {state.isLoading ? (
                  <Loader2 className="w-5 h-5 text-white animate-spin" />
                ) : state.error ? (
                  <RefreshCw className="w-5 h-5 text-white" />
                ) : state.isPlaying ? (
                  <Pause className="w-5 h-5 text-white" />
                ) : (
                  <Play className="w-5 h-5 text-white ml-0.5" />
                )}
              </button>

              {showSkipButtons && (
                <button
                  onClick={onNext}
                  disabled={!hasNext}
                  className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  <SkipForward className="w-5 h-5" />
                </button>
              )}
            </div>

            {/* Volume Control */}
            {showVolumeControl && (
              <div className="flex items-center justify-center space-x-3">
                <button
                  onClick={toggleMute}
                  className="text-gray-400 hover:text-white transition-colors duration-200"
                >
                  {state.isMuted || state.volume === 0 ? (
                    <VolumeX className="w-4 h-4" />
                  ) : (
                    <Volume2 className="w-4 h-4" />
                  )}
                </button>
                
                <div 
                  ref={volumeBarRef}
                  onClick={handleVolumeChange}
                  className="relative w-20 h-1 bg-gray-700 rounded-full cursor-pointer group"
                >
                  <div 
                    className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-200"
                    style={{ width: `${state.isMuted ? 0 : state.volume * 100}%` }}
                  />
                  <div className="absolute top-1/2 left-0 w-2 h-2 bg-white rounded-full shadow-md transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                       style={{ left: `${(state.isMuted ? 0 : state.volume) * 100}%`, marginLeft: '-4px' }} />
                </div>
                
                <span className="text-xs text-gray-400 w-8 text-right">
                  {Math.round((state.isMuted ? 0 : state.volume) * 100)}%
                </span>
              </div>
            )}
          </div>
        )}

        {/* Status Information */}
        {(state.retryCount > 0 || state.loadAttempts > 1) && (
          <div className="mt-3 text-xs text-gray-500 text-center">
            {state.retryCount > 0 && `Retry attempt: ${state.retryCount}/${maxRetries}`}
            {state.retryCount > 0 && state.loadAttempts > 1 && ' • '}
            {state.loadAttempts > 1 && `Load attempts: ${state.loadAttempts}`}
          </div>
        )}
      </div>
    </ComponentErrorBoundary>
  )
} 