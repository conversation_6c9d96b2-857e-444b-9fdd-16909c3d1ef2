'use client'

import { useEffect, useState } from 'react'
import { toast } from 'react-hot-toast'

interface ServiceWorkerState {
  registration: ServiceWorkerRegistration | null
  installing: boolean
  waiting: boolean
  active: boolean
  error: string | null
  updateAvailable: boolean
}

export function ServiceWorkerRegistration() {
  const [swState, setSwState] = useState<ServiceWorkerState>({
    registration: null,
    installing: false,
    waiting: false,
    active: false,
    error: null,
    updateAvailable: false
  })

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      registerServiceWorker()
    } else {
      setSwState(prev => ({ 
        ...prev, 
        error: 'Service Worker not supported' 
      }))
    }
  }, [])

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      })

      setSwState(prev => ({ 
        ...prev, 
        registration,
        installing: !!registration.installing,
        waiting: !!registration.waiting,
        active: !!registration.active
      }))

      // Listen for service worker state changes
      if (registration.installing) {
        trackInstalling(registration.installing)
      }

      if (registration.waiting) {
        setSwState(prev => ({ ...prev, updateAvailable: true }))
        showUpdateAvailable()
      }

      if (registration.active) {
        console.log('🎵 Service Worker active')
      }

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          trackInstalling(newWorker)
        }
      })

      // Check for updates every 60 seconds
      setInterval(() => {
        registration.update()
      }, 60000)

    } catch (error) {
      console.error('🎵 Service Worker registration failed:', error)
      setSwState(prev => ({ 
        ...prev, 
        error: `Registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }))
    }
  }

  const trackInstalling = (worker: ServiceWorker) => {
    setSwState(prev => ({ ...prev, installing: true }))

    worker.addEventListener('statechange', () => {
      if (worker.state === 'installed') {
        setSwState(prev => ({ 
          ...prev, 
          installing: false,
          waiting: true,
          updateAvailable: true
        }))
        
        // If there's already an active service worker, show update prompt
        if (navigator.serviceWorker.controller) {
          showUpdateAvailable()
        } else {
          // First install
          setSwState(prev => ({ 
            ...prev, 
            waiting: false,
            active: true
          }))
          console.log('🎵 Service Worker installed successfully')
        }
      }

      if (worker.state === 'activated') {
        setSwState(prev => ({ 
          ...prev, 
          waiting: false,
          active: true,
          updateAvailable: false
        }))
        console.log('🎵 Service Worker activated')
      }
    })
  }

  const showUpdateAvailable = () => {
    toast.success('Update Available: A new version of Tunami is ready!', {
      duration: 10000,
      action: {
        label: 'Update Now',
        onClick: () => refreshApp()
      }
    })
  }

  const refreshApp = () => {
    if (swState.registration?.waiting) {
      // Tell the waiting service worker to skip waiting
      swState.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
      
      // Listen for the controlling service worker to change
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload()
      })
    } else {
      window.location.reload()
    }
  }

  // Listen for messages from service worker
  useEffect(() => {
    const messageHandler = (event: MessageEvent) => {
      if (event.data?.type === 'SW_ACTIVATED') {
        setSwState(prev => ({ 
          ...prev, 
          active: true,
          updateAvailable: false
        }))
        
        toast.success('Tunami has been updated to the latest version.')
      }

      if (event.data?.type === 'CACHE_UPDATED') {
        console.log('🎵 Cache updated:', event.data.cache)
      }

      if (event.data?.type === 'OFFLINE_READY') {
        toast('Tunami is now available offline.', {
          icon: '🔌',
          duration: 5000
        })
      }
    }

    navigator.serviceWorker?.addEventListener('message', messageHandler)

    return () => {
      navigator.serviceWorker?.removeEventListener('message', messageHandler)
    }
  }, [])

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => {
      toast.success('Back online! Syncing data...', {
        icon: '🌐'
      })
    }

    const handleOffline = () => {
      toast.error('You are now offline. Some features may be limited.', {
        icon: '📱'
      })
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Show error if service worker fails
  useEffect(() => {
    if (swState.error) {
      toast.error(swState.error, {
        duration: 10000
      })
    }
  }, [swState.error])

  // This component doesn't render anything visible
  return null
} 