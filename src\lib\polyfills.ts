// Polyfill system for cross-browser compatibility
import { browserDetector } from './browser-detection'

interface PolyfillConfig {
  name: string
  condition: () => boolean
  load: () => Promise<void>
  priority: 'high' | 'medium' | 'low'
}

class PolyfillManager {
  private loaded = new Set<string>()
  private loading = new Set<string>()

  // Core polyfills configuration
  private polyfills: PolyfillConfig[] = [
    // ES6 Features
    {
      name: 'es6-promise',
      condition: () => typeof Promise === 'undefined',
      load: () => this.loadScript('https://cdn.jsdelivr.net/npm/es6-promise@4.2.8/dist/es6-promise.auto.min.js'),
      priority: 'high'
    },
    {
      name: 'array-includes',
      condition: () => !Array.prototype.includes,
      load: () => this.loadArrayIncludes(),
      priority: 'high'
    },
    {
      name: 'object-assign',
      condition: () => typeof Object.assign !== 'function',
      load: () => this.loadObjectAssign(),
      priority: 'high'
    },

    // Web APIs
    {
      name: 'intersection-observer',
      condition: () => !window.IntersectionObserver,
      load: () => this.loadScript('https://cdn.jsdelivr.net/npm/intersection-observer@0.12.2/intersection-observer.js'),
      priority: 'medium'
    },
    {
      name: 'resize-observer',
      condition: () => !window.ResizeObserver,
      load: () => this.loadScript('https://cdn.jsdelivr.net/npm/resize-observer-polyfill@1.5.1/dist/ResizeObserver.js'),
      priority: 'medium'
    },
    {
      name: 'web-audio-context',
      condition: () => !window.AudioContext && !window.webkitAudioContext,
      load: () => this.loadWebAudioPolyfill(),
      priority: 'medium'
    },

    // CSS Features
    {
      name: 'css-supports',
      condition: () => !window.CSS || !window.CSS.supports,
      load: () => this.loadCSSSupportsPolyfill(),
      priority: 'low'
    },
    {
      name: 'custom-elements',
      condition: () => !window.customElements,
      load: () => this.loadScript('https://cdn.jsdelivr.net/npm/@webcomponents/custom-elements@1.6.0/custom-elements.min.js'),
      priority: 'low'
    },

    // Audio-specific polyfills
    {
      name: 'media-session',
      condition: () => !('mediaSession' in navigator),
      load: () => this.loadMediaSessionPolyfill(),
      priority: 'low'
    }
  ]

  async initializePolyfills(): Promise<void> {
    const browserInfo = browserDetector.detect()
    console.log('🔧 Initializing polyfills for:', browserInfo.name, browserInfo.version)

    // Filter and sort polyfills by priority
    const neededPolyfills = this.polyfills
      .filter(polyfill => polyfill.condition())
      .sort((a, b) => {
        const priorities = { high: 3, medium: 2, low: 1 }
        return priorities[b.priority] - priorities[a.priority]
      })

    console.log('📦 Loading polyfills:', neededPolyfills.map(p => p.name))

    // Load high priority polyfills first (sequential)
    const highPriority = neededPolyfills.filter(p => p.priority === 'high')
    for (const polyfill of highPriority) {
      await this.loadPolyfill(polyfill)
    }

    // Load medium and low priority polyfills in parallel
    const otherPolyfills = neededPolyfills.filter(p => p.priority !== 'high')
    await Promise.allSettled(
      otherPolyfills.map(polyfill => this.loadPolyfill(polyfill))
    )

    console.log('✅ Polyfills loaded successfully')
  }

  private async loadPolyfill(polyfill: PolyfillConfig): Promise<void> {
    if (this.loaded.has(polyfill.name) || this.loading.has(polyfill.name)) {
      return
    }

    this.loading.add(polyfill.name)

    try {
      await polyfill.load()
      this.loaded.add(polyfill.name)
      console.log(`✅ Loaded polyfill: ${polyfill.name}`)
    } catch (error) {
      console.warn(`⚠️ Failed to load polyfill: ${polyfill.name}`, error)
    } finally {
      this.loading.delete(polyfill.name)
    }
  }

  private loadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = src
      script.async = true
      script.onload = () => resolve()
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`))
      document.head.appendChild(script)
    })
  }

  // Custom polyfill implementations
  private async loadArrayIncludes(): Promise<void> {
    if (!Array.prototype.includes) {
      Array.prototype.includes = function(searchElement: any, fromIndex: number = 0) {
        return this.indexOf(searchElement, fromIndex) !== -1
      }
    }
  }

  private async loadObjectAssign(): Promise<void> {
    if (typeof Object.assign !== 'function') {
      Object.assign = function(target: any, ...sources: any[]) {
        if (target == null) {
          throw new TypeError('Cannot convert undefined or null to object')
        }

        const to = Object(target)

        for (let index = 0; index < sources.length; index++) {
          const nextSource = sources[index]

          if (nextSource != null) {
            for (const nextKey in nextSource) {
              if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                to[nextKey] = nextSource[nextKey]
              }
            }
          }
        }

        return to
      }
    }
  }

  private async loadWebAudioPolyfill(): Promise<void> {
    // Basic Web Audio API polyfill for older browsers
    if (!window.AudioContext && window.webkitAudioContext) {
      window.AudioContext = window.webkitAudioContext
    }

    // Polyfill AudioContext methods if needed
    if (window.AudioContext) {
      const AudioContext = window.AudioContext
      
      // Polyfill createGain for older versions
      if (!AudioContext.prototype.createGain && AudioContext.prototype.createGainNode) {
        AudioContext.prototype.createGain = AudioContext.prototype.createGainNode
      }

      // Polyfill createDelay for older versions
      if (!AudioContext.prototype.createDelay && AudioContext.prototype.createDelayNode) {
        AudioContext.prototype.createDelay = AudioContext.prototype.createDelayNode
      }

      // Polyfill createScriptProcessor for older versions
      if (!AudioContext.prototype.createScriptProcessor && AudioContext.prototype.createJavaScriptNode) {
        AudioContext.prototype.createScriptProcessor = AudioContext.prototype.createJavaScriptNode
      }
    }
  }

  private async loadCSSSupportsPolyfill(): Promise<void> {
    if (!window.CSS) {
      window.CSS = {} as CSS
    }

    if (!window.CSS.supports) {
      window.CSS.supports = function(property: string, value?: string): boolean {
        try {
          const element = document.createElement('div')
          const style = element.style

          if (value !== undefined) {
            ;(style as any)[property] = value
            return (style as any)[property] === value
          } else {
            // Property-only check
            return property in style
          }
        } catch {
          return false
        }
      }
    }
  }

  private async loadMediaSessionPolyfill(): Promise<void> {
    if (!('mediaSession' in navigator)) {
      // Create a basic MediaSession polyfill
      const mediaSession = {
        metadata: null,
        playbackState: 'none',
        setActionHandler: function(action: string, handler: (() => void) | null) {
          console.log(`MediaSession polyfill: setActionHandler(${action})`)
          // Store handlers for potential future use
          if (!this.actionHandlers) {
            this.actionHandlers = new Map()
          }
          this.actionHandlers.set(action, handler)
        },
        actionHandlers: new Map()
      }

      ;(navigator as any).mediaSession = mediaSession
    }
  }

  // Browser-specific optimizations
  async applyBrowserOptimizations(): Promise<void> {
    const browserInfo = browserDetector.detect()

    switch (browserInfo.name) {
      case 'Safari':
        await this.applySafariOptimizations(browserInfo)
        break
      case 'Firefox':
        await this.applyFirefoxOptimizations(browserInfo)
        break
      case 'Chrome':
        await this.applyChromeOptimizations(browserInfo)
        break
      case 'Edge':
        await this.applyEdgeOptimizations(browserInfo)
        break
    }
  }

  private async applySafariOptimizations(browserInfo: any): Promise<void> {
    // Safari-specific audio optimizations
    if (browserInfo.platform === 'iOS') {
      // iOS Safari audio unlock mechanism
      const unlockAudio = () => {
        const audio = new Audio()
        audio.volume = 0
        audio.play().then(() => {
          audio.pause()
          document.removeEventListener('touchstart', unlockAudio)
        }).catch(() => {
          // Ignore unlock failures
        })
      }

      document.addEventListener('touchstart', unlockAudio, { once: true })
    }

    // Safari AudioContext optimization
    if (window.webkitAudioContext && !window.AudioContext) {
      window.AudioContext = window.webkitAudioContext
    }
  }

  private async applyFirefoxOptimizations(browserInfo: any): Promise<void> {
    // Firefox audio optimizations
    if (browserInfo.features.webAudio) {
      // Optimize audio context settings for Firefox
      const originalAudioContext = window.AudioContext
      if (originalAudioContext) {
        window.AudioContext = function(this: AudioContext, ...args: any[]) {
          const context = new originalAudioContext(...args)
          
          // Firefox-specific optimizations
          if (context.state === 'suspended') {
            document.addEventListener('click', () => {
              context.resume()
            }, { once: true })
          }
          
          return context
        } as any
      }
    }
  }

  private async applyChromeOptimizations(browserInfo: any): Promise<void> {
    // Chrome autoplay policy handling
    const handleAutoplayPolicy = () => {
      document.addEventListener('click', () => {
        // Resume any suspended audio contexts
        if (window.AudioContext) {
          const contexts = (window as any).__audioContexts || []
          contexts.forEach((context: AudioContext) => {
            if (context.state === 'suspended') {
              context.resume()
            }
          })
        }
      }, { once: true })
    }

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', handleAutoplayPolicy)
    } else {
      handleAutoplayPolicy()
    }
  }

  private async applyEdgeOptimizations(browserInfo: any): Promise<void> {
    // Edge-specific optimizations
    if (browserInfo.engine === 'EdgeHTML') {
      // Legacy Edge optimizations
      if (!window.AudioContext && window.webkitAudioContext) {
        window.AudioContext = window.webkitAudioContext
      }
    }
  }

  // Check if polyfill is loaded
  isLoaded(polyfillName: string): boolean {
    return this.loaded.has(polyfillName)
  }

  // Get list of loaded polyfills
  getLoadedPolyfills(): string[] {
    return Array.from(this.loaded)
  }
}

// Create singleton instance
const polyfillManager = new PolyfillManager()

// Export main functions
export const initializePolyfills = () => polyfillManager.initializePolyfills()
export const applyBrowserOptimizations = () => polyfillManager.applyBrowserOptimizations()
export const isPolyfillLoaded = (name: string) => polyfillManager.isLoaded(name)
export const getLoadedPolyfills = () => polyfillManager.getLoadedPolyfills()

export default polyfillManager 