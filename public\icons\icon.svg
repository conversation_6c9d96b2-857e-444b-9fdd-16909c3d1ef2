<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="512" height="512" rx="80" fill="url(#bg)"/>
  
  <!-- Music Note -->
  <g transform="translate(256,256)">
    <!-- Note stem -->
    <rect x="60" y="-120" width="8" height="180" fill="white" rx="4"/>
    
    <!-- Note head -->
    <ellipse cx="45" cy="60" rx="25" ry="18" fill="white"/>
    
    <!-- Musical waves -->
    <path d="M -80 -40 Q -60 -60 -40 -40 Q -20 -20 0 -40 Q 20 -60 40 -40" 
          stroke="white" stroke-width="6" fill="none" stroke-linecap="round"/>
    <path d="M -80 0 Q -60 -20 -40 0 Q -20 20 0 0 Q 20 -20 40 0" 
          stroke="white" stroke-width="6" fill="none" stroke-linecap="round"/>
    <path d="M -80 40 Q -60 20 -40 40 Q -20 60 0 40 Q 20 20 40 40" 
          stroke="white" stroke-width="6" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- Tunami text -->
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="48" font-weight="bold" 
        text-anchor="middle" fill="white">TUNAMI</text>
</svg> 