# Test info

- Name: Audio Compatibility Tests >> should handle autoplay policies correctly
- Location: D:\Vinod\Work\Cursor_Projects\Tunami\tests\audio-compatibility.spec.ts:110:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at D:\Vinod\Work\Cursor_Projects\Tunami\tests\audio-compatibility.spec.ts:143:42
```

# Page snapshot

```yaml
- paragraph: Initializing browser compatibility...
```

# Test source

```ts
   43 |       expect(audioCapabilities.aac).toBe(true)
   44 |     } else if (browserName === 'firefox') {
   45 |       // Firefox specifics
   46 |       expect(audioCapabilities.ogg).toBe(true)
   47 |     } else if (browserName === 'chromium') {
   48 |       // Chrome specifics
   49 |       expect(audioCapabilities.webm).toBe(true)
   50 |     }
   51 |   })
   52 |
   53 |   test('should handle audio format fallbacks', async ({ page }) => {
   54 |     // Test audio source selection logic
   55 |     const fallbackTest = await page.evaluate(() => {
   56 |       const sources = [
   57 |         { format: 'flac', src: 'test.flac', type: 'audio/flac' },
   58 |         { format: 'ogg', src: 'test.ogg', type: 'audio/ogg' },
   59 |         { format: 'mp3', src: 'test.mp3', type: 'audio/mpeg' }
   60 |       ]
   61 |       
   62 |       const audio = document.createElement('audio')
   63 |       const supportedFormats = sources.filter(source => 
   64 |         audio.canPlayType(source.type) !== ''
   65 |       )
   66 |       
   67 |       return {
   68 |         totalFormats: sources.length,
   69 |         supportedFormats: supportedFormats.length,
   70 |         firstSupported: supportedFormats[0]?.format || 'none'
   71 |       }
   72 |     })
   73 |
   74 |     expect(fallbackTest.supportedFormats).toBeGreaterThan(0)
   75 |     expect(['flac', 'ogg', 'mp3']).toContain(fallbackTest.firstSupported)
   76 |   })
   77 |
   78 |   test('should enable audio context on user interaction', async ({ page, browserName }) => {
   79 |     // Test audio context activation
   80 |     const audioContextTest = await page.evaluate(async () => {
   81 |       if (!(window.AudioContext || window.webkitAudioContext)) {
   82 |         return { error: 'No AudioContext support' }
   83 |       }
   84 |
   85 |       const AudioContext = window.AudioContext || window.webkitAudioContext
   86 |       const context = new AudioContext()
   87 |       
   88 |       const initialState = context.state
   89 |       
   90 |       // Try to resume context (simulates user interaction)
   91 |       if (context.state === 'suspended') {
   92 |         await context.resume()
   93 |       }
   94 |       
   95 |       return {
   96 |         initialState,
   97 |         finalState: context.state,
   98 |         sampleRate: context.sampleRate
   99 |       }
  100 |     })
  101 |
  102 |     if ('error' in audioContextTest) {
  103 |       test.skip('AudioContext not supported in this browser')
  104 |     }
  105 |
  106 |     expect(['suspended', 'running']).toContain(audioContextTest.initialState)
  107 |     expect(audioContextTest.sampleRate).toBeGreaterThan(0)
  108 |   })
  109 |
  110 |   test('should handle autoplay policies correctly', async ({ page, browserName }) => {
  111 |     // Test autoplay behavior across browsers
  112 |     const autoplayTest = await page.evaluate(async () => {
  113 |       const audio = new Audio()
  114 |       audio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA'
  115 |       
  116 |       const results = {
  117 |         mutedAutoplay: false,
  118 |         unmutedAutoplay: false,
  119 |         error: null
  120 |       }
  121 |       
  122 |       try {
  123 |         // Test muted autoplay
  124 |         audio.muted = true
  125 |         await audio.play()
  126 |         results.mutedAutoplay = true
  127 |         audio.pause()
  128 |         
  129 |         // Test unmuted autoplay (likely to fail without user gesture)
  130 |         audio.muted = false
  131 |         await audio.play()
  132 |         results.unmutedAutoplay = true
  133 |         audio.pause()
  134 |       } catch (error) {
  135 |         results.error = error.name
  136 |       }
  137 |       
  138 |       return results
  139 |     })
  140 |
  141 |     // Muted autoplay should work in most modern browsers
  142 |     if (browserName !== 'webkit') {
> 143 |       expect(autoplayTest.mutedAutoplay).toBe(true)
      |                                          ^ Error: expect(received).toBe(expected) // Object.is equality
  144 |     }
  145 |     
  146 |     // Unmuted autoplay typically requires user gesture
  147 |     if (autoplayTest.error) {
  148 |       expect(['NotAllowedError', 'AbortError']).toContain(autoplayTest.error)
  149 |     }
  150 |   })
  151 |
  152 |   test('should render audio controls correctly', async ({ page }) => {
  153 |     // Navigate to a page with audio controls (assuming upload or player page exists)
  154 |     const hasAudioControls = await page.evaluate(() => {
  155 |       // Check for custom audio control elements
  156 |       const audioElements = document.querySelectorAll('audio, [data-testid*="audio"], [class*="audio"]')
  157 |       return audioElements.length > 0
  158 |     })
  159 |
  160 |     // This test might need adjustment based on actual component structure
  161 |     console.log('Audio controls found:', hasAudioControls)
  162 |   })
  163 |
  164 |   test('should handle network quality audio selection', async ({ page, browserName }) => {
  165 |     // Test network-based quality selection
  166 |     const networkTest = await page.evaluate(() => {
  167 |       const connection = (navigator as any).connection
  168 |       if (!connection) return { error: 'No network information available' }
  169 |       
  170 |       const getQualityForConnection = (effectiveType: string, downlink: number) => {
  171 |         if (downlink < 0.5 || effectiveType === '2g') return 'low'
  172 |         if (downlink < 2 || effectiveType === '3g') return 'medium'
  173 |         if (downlink < 10 || effectiveType === '4g') return 'high'
  174 |         return 'lossless'
  175 |       }
  176 |       
  177 |       return {
  178 |         effectiveType: connection.effectiveType || 'unknown',
  179 |         downlink: connection.downlink || 0,
  180 |         recommendedQuality: getQualityForConnection(connection.effectiveType, connection.downlink)
  181 |       }
  182 |     })
  183 |
  184 |     if ('error' in networkTest) {
  185 |       test.skip('Network information API not available')
  186 |     }
  187 |
  188 |     expect(['slow-2g', '2g', '3g', '4g', 'unknown']).toContain(networkTest.effectiveType)
  189 |     expect(['low', 'medium', 'high', 'lossless']).toContain(networkTest.recommendedQuality)
  190 |   })
  191 |
  192 |   test('should handle mobile-specific audio features', async ({ page, browserName, isMobile }) => {
  193 |     if (!isMobile) {
  194 |       test.skip('Mobile-specific test')
  195 |     }
  196 |
  197 |     const mobileAudioTest = await page.evaluate(() => {
  198 |       const audio = document.createElement('audio')
  199 |       
  200 |       return {
  201 |         playsinline: audio.hasAttribute('playsinline') || audio.playsInline !== undefined,
  202 |         webkitPlaysinline: audio.hasAttribute('webkit-playsinline'),
  203 |         touchEvents: 'ontouchstart' in window,
  204 |         orientationChange: 'onorientationchange' in window
  205 |       }
  206 |     })
  207 |
  208 |     // Mobile browsers should support touch events
  209 |     expect(mobileAudioTest.touchEvents).toBe(true)
  210 |     
  211 |     // iOS Safari should support playsinline
  212 |     if (browserName === 'webkit' && isMobile) {
  213 |       expect(mobileAudioTest.playsinline).toBe(true)
  214 |     }
  215 |   })
  216 |
  217 |   test('should maintain audio state during navigation', async ({ page }) => {
  218 |     // This would test audio state persistence
  219 |     // Skip if no audio functionality is available on current page
  220 |     const hasAudio = await page.evaluate(() => {
  221 |       return document.querySelector('audio') !== null
  222 |     })
  223 |     
  224 |     if (!hasAudio) {
  225 |       test.skip('No audio elements found for state testing')
  226 |     }
  227 |     
  228 |     // Test would involve playing audio, navigating, and checking state
  229 |     console.log('Audio state persistence test - implementation depends on app structure')
  230 |   })
  231 | })
  232 |
  233 | // Helper function to enable audio interaction
  234 | async function enableAudioIfNeeded(page: Page) {
  235 |   try {
  236 |     // Look for any audio interaction prompts or buttons
  237 |     const audioEnableButton = page.locator('button:has-text("Enable Audio")')
  238 |     if (await audioEnableButton.isVisible({ timeout: 2000 })) {
  239 |       await audioEnableButton.click()
  240 |     }
  241 |     
  242 |     // Simulate a click to enable audio context (for autoplay policies)
  243 |     await page.click('body')
```