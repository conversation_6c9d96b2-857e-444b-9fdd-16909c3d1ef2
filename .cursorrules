# Tunami Project - Cursor Rules with MCP Integration

## Project Context

This is a Next.js 14 music streaming platform called Tunami with TypeScript, Supabase, and Tailwind CSS.

## MCP Server Tools Available

### Filesystem Operations (tunami-filesystem)

- read_file: Read files from allowed directories (src, public, uploads, logs)
- write_file: Write files securely
- list_directory: List directory contents
- create_directory: Create new directories
- get_file_info: Get file metadata

### Memory Management (tunami-memory)

- store_memory: Store data in categories (userPreferences, playlists, sessions, analytics, recommendations)
- retrieve_memory: Get stored memories
- search_memories: Search across memory categories
- list_memories: List all memories by category

### Music Data Fetching (tunami-fetch)

- fetch_music_metadata: Get metadata from MusicBrainz, Last.fm
- fetch_lyrics: Retrieve song lyrics
- fetch_album_art: Get album artwork URLs
- fetch_url: General web requests to music domains

### GitHub Integration (tunami-github)

- get_repository: Get repo information
- list_repository_files: Browse repo contents
- get_file_content: Read file contents from GitHub
- list_issues: View GitHub issues
- create_issue: Create new issues
- get_commits: View commit history

### Database Operations (tunami-postgres)

- list_tables: Show database tables
- describe_table: Get table schema
- execute_query: Run SELECT queries (read-only)
- get_table_stats: Database statistics

### Context7 Documentation (context7)

- resolve_library_id: Convert library names to Context7-compatible IDs
- get_library_docs: Fetch up-to-date documentation and code examples
- Real-time documentation for React, Next.js, Supabase, TypeScript, etc.
- Version-specific code examples
- No more outdated or hallucinated APIs

## Development Guidelines

1. **Use MCP tools** when working with:

   - File operations (prefer tunami-filesystem over direct file access)
   - Storing user preferences or analytics (use tunami-memory)
   - Fetching music metadata (use tunami-fetch)
   - GitHub operations (use tunami-github)
   - Database queries (use tunami-postgres)
   - Up-to-date documentation (add "use context7" to prompts)

2. **Security**: All MCP servers have built-in security restrictions

   - Filesystem: Limited to specific directories
   - Fetch: Restricted to music-related domains
   - Postgres: Read-only queries only

3. **Error Handling**: Always handle MCP tool errors gracefully

4. **Performance**: Use memory server to cache frequently accessed data

## MCP Integration Prompts

When I ask about:

- "user data" or "preferences" → Use tunami-memory server
- "music info" or "lyrics" → Use tunami-fetch server
- "file operations" → Use tunami-filesystem server
- "GitHub" or "repository" → Use tunami-github server
- "database" or "tables" → Use tunami-postgres server
- "documentation" or "examples" → Add "use context7" to prompts

## Context7 Usage Examples

For up-to-date documentation and code examples, add "use context7" to prompts:

- "Create a Supabase auth component for Next.js 14. use context7"
- "Build a music player component with React hooks. use context7"
- "Show me how to implement real-time subscriptions with Supabase. use context7"
- "Write a Next.js API route with proper TypeScript types. use context7"
- "Create a responsive playlist component with TailwindCSS. use context7"

## Project Structure

- `/src/app` - Next.js 14 app router pages
- `/src/components` - React components
- `/src/lib` - Utility functions and configurations
- `/src/types` - TypeScript type definitions
- `/public` - Static assets
- `/mcp-servers` - Model Context Protocol servers

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Database**: Supabase (PostgreSQL)
- **Styling**: Tailwind CSS
- **Authentication**: Supabase Auth
- **Audio**: HTML5 Audio API
- **State Management**: React hooks and context

## Code Style

- Use TypeScript for all new files
- Follow Next.js 14 app router conventions
- Use Tailwind CSS for styling
- Implement proper error handling
- Use async/await for asynchronous operations
- Follow React best practices (hooks, functional components)

## Audio System

- Support for MP3, WAV, FLAC formats
- Real-time playback controls
- Volume management
- Progress tracking
- Playlist functionality

## Database Schema (Supabase)

- `users` - User authentication and profiles
- `tracks` - Music track metadata
- `playlists` - User playlists
- `user_tracks` - User-track relationships
- `upload_history` - Track upload records

## Authentication Flow

- Supabase Auth for login/signup
- Protected routes with middleware
- Session management
- User profile management

## Key Features

- Music streaming and playback
- User authentication
- Track uploading
- Playlist management
- Search functionality
- User dashboard
- Audio controls

## Development Workflow

1. Use MCP servers for enhanced AI assistance
2. Test changes locally on http://localhost:3003
3. Commit changes with descriptive messages
4. Follow TypeScript best practices
5. Ensure responsive design with Tailwind CSS
