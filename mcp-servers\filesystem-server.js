#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import fs from "fs/promises";
import path from "path";

// Create MCP server for Tunami filesystem operations
const server = new McpServer({
  name: "tunami-filesystem",
  version: "1.0.0"
});

// Define allowed directories for Tunami
const ALLOWED_DIRS = [
  path.resolve("../src"),
  path.resolve("../public"),
  path.resolve("../uploads"),
  path.resolve("../logs")
];

// Helper function to check if path is allowed
function isPathAllowed(filePath) {
  const resolvedPath = path.resolve(filePath);
  return ALLOWED_DIRS.some(allowedDir => 
    resolvedPath.startsWith(allowedDir)
  );
}

// Read file tool
server.tool(
  "read_file",
  {
    path: z.string().describe("Path to the file to read")
  },
  async ({ path: filePath }) => {
    if (!isPathAllowed(filePath)) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: Access denied to path ${filePath}` 
        }],
        isError: true
      };
    }

    try {
      const content = await fs.readFile(filePath, 'utf-8');
      return {
        content: [{ 
          type: "text", 
          text: `File: ${filePath}\n\n${content}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error reading file: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Write file tool
server.tool(
  "write_file",
  {
    path: z.string().describe("Path to the file to write"),
    content: z.string().describe("Content to write to the file")
  },
  async ({ path: filePath, content }) => {
    if (!isPathAllowed(filePath)) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: Access denied to path ${filePath}` 
        }],
        isError: true
      };
    }

    try {
      await fs.writeFile(filePath, content, 'utf-8');
      return {
        content: [{ 
          type: "text", 
          text: `Successfully wrote to ${filePath}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error writing file: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// List directory tool
server.tool(
  "list_directory",
  {
    path: z.string().describe("Path to the directory to list")
  },
  async ({ path: dirPath }) => {
    if (!isPathAllowed(dirPath)) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: Access denied to path ${dirPath}` 
        }],
        isError: true
      };
    }

    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      const listing = entries.map(entry => {
        const type = entry.isDirectory() ? "[DIR]" : "[FILE]";
        return `${type} ${entry.name}`;
      }).join('\n');

      return {
        content: [{ 
          type: "text", 
          text: `Directory listing for ${dirPath}:\n\n${listing}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error listing directory: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Create directory tool
server.tool(
  "create_directory",
  {
    path: z.string().describe("Path to the directory to create")
  },
  async ({ path: dirPath }) => {
    if (!isPathAllowed(dirPath)) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: Access denied to path ${dirPath}` 
        }],
        isError: true
      };
    }

    try {
      await fs.mkdir(dirPath, { recursive: true });
      return {
        content: [{ 
          type: "text", 
          text: `Successfully created directory ${dirPath}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error creating directory: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// File info tool
server.tool(
  "get_file_info",
  {
    path: z.string().describe("Path to get file information")
  },
  async ({ path: filePath }) => {
    if (!isPathAllowed(filePath)) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: Access denied to path ${filePath}` 
        }],
        isError: true
      };
    }

    try {
      const stats = await fs.stat(filePath);
      const info = {
        path: filePath,
        size: stats.size,
        isDirectory: stats.isDirectory(),
        isFile: stats.isFile(),
        created: stats.birthtime.toISOString(),
        modified: stats.mtime.toISOString(),
        accessed: stats.atime.toISOString()
      };

      return {
        content: [{ 
          type: "text", 
          text: `File info for ${filePath}:\n${JSON.stringify(info, null, 2)}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error getting file info: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Start the server
const transport = new StdioServerTransport();
await server.connect(transport);

console.error("Tunami Filesystem MCP Server running on stdio"); 