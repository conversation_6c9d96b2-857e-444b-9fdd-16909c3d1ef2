#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import os from 'os';

const CURSOR_CONFIG_PATHS = {
  win32: path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'User', 'settings.json'),
  darwin: path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'User', 'settings.json'),
  linux: path.join(os.homedir(), '.config', 'Cursor', 'User', 'settings.json')
};

const PROJECT_ROOT = path.resolve('..');
const MCP_SERVERS_PATH = path.resolve('.');

async function setupCursorMCP() {
  console.log('🎵 Setting up Tunami MCP Servers for Cursor...\n');

  // Get the appropriate config path for the current OS
  const configPath = CURSOR_CONFIG_PATHS[os.platform()];
  
  if (!configPath) {
    console.error('❌ Unsupported operating system');
    process.exit(1);
  }

  console.log(`📁 Cursor config path: ${configPath}`);

  // MCP server configuration
  const mcpConfig = {
    "mcp.servers": {
      "tunami-filesystem": {
        "command": "node",
        "args": [path.join(MCP_SERVERS_PATH, "filesystem-server.js")],
        "cwd": MCP_SERVERS_PATH,
        "description": "Secure filesystem operations for Tunami project"
      },
      "tunami-memory": {
        "command": "node",
        "args": [path.join(MCP_SERVERS_PATH, "memory-server.js")],
        "cwd": MCP_SERVERS_PATH,
        "description": "Persistent memory for user preferences and analytics"
      },
      "tunami-fetch": {
        "command": "node",
        "args": [path.join(MCP_SERVERS_PATH, "fetch-server.js")],
        "cwd": MCP_SERVERS_PATH,
        "description": "Music metadata and lyrics fetching"
      },
      "tunami-github": {
        "command": "node",
        "args": [path.join(MCP_SERVERS_PATH, "github-server.js")],
        "cwd": MCP_SERVERS_PATH,
        "env": {
          "GITHUB_TOKEN": "${env:GITHUB_TOKEN}"
        },
        "description": "GitHub repository management and API integration"
      },
      "tunami-postgres": {
        "command": "node",
        "args": [path.join(MCP_SERVERS_PATH, "postgres-server.js")],
        "cwd": MCP_SERVERS_PATH,
        "env": {
          "SUPABASE_DB_HOST": "${env:SUPABASE_DB_HOST}",
          "SUPABASE_DB_USER": "${env:SUPABASE_DB_USER}",
          "SUPABASE_DB_PASSWORD": "${env:SUPABASE_DB_PASSWORD}"
        },
        "description": "Supabase PostgreSQL database operations"
      },
      "context7": {
        "command": "npx",
        "args": ["-y", "@upstash/context7-mcp@latest"],
        "description": "Up-to-date documentation and code examples for libraries"
      }
    }
  };

  try {
    // Ensure the config directory exists
    await fs.mkdir(path.dirname(configPath), { recursive: true });

    // Read existing config or create new one
    let existingConfig = {};
    try {
      const configContent = await fs.readFile(configPath, 'utf-8');
      existingConfig = JSON.parse(configContent);
    } catch (error) {
      console.log('📝 Creating new Cursor configuration...');
    }

    // Merge MCP configuration
    const updatedConfig = {
      ...existingConfig,
      ...mcpConfig
    };

    // Write updated configuration
    await fs.writeFile(configPath, JSON.stringify(updatedConfig, null, 2), 'utf-8');

    console.log('✅ MCP servers configured successfully!');
    console.log('\n🔧 Next steps:');
    console.log('1. Restart Cursor IDE');
    console.log('2. Set up environment variables in .env file');
    console.log('3. Test MCP servers with: npm run start:memory');
    console.log('4. Use MCP tools in Cursor chat interface');
    
    console.log('\n💡 Example usage in Cursor:');
    console.log('- "Use tunami-memory to store user preference for dark mode"');
    console.log('- "Fetch lyrics for a song using tunami-fetch"');
    console.log('- "List files in src/components using tunami-filesystem"');
    console.log('- "Create a Next.js component with TypeScript. use context7"');
    console.log('- "Show me how to use Supabase auth in React. use context7"');

  } catch (error) {
    console.error('❌ Error setting up MCP configuration:', error.message);
    console.log('\n🔧 Manual setup:');
    console.log('Add this to your Cursor settings.json:');
    console.log(JSON.stringify(mcpConfig, null, 2));
  }
}

// Check if dependencies are installed
async function checkDependencies() {
  try {
    await fs.access('node_modules');
    console.log('✅ Dependencies found');
  } catch {
    console.log('📦 Installing dependencies...');
    const { spawn } = await import('child_process');
    
    return new Promise((resolve, reject) => {
      const npm = spawn('npm', ['install'], { stdio: 'inherit' });
      npm.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Dependencies installed');
          resolve();
        } else {
          reject(new Error('Failed to install dependencies'));
        }
      });
    });
  }
}

// Main setup function
async function main() {
  try {
    await checkDependencies();
    await setupCursorMCP();
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

main(); 