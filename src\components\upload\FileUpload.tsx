'use client'

import { useState, useCallback, useRef } from 'react'
import { Upload, X, File, AlertCircle, CheckCircle, Music, Loader } from 'lucide-react'

export interface UploadFile {
  file: File
  id: string
  preview?: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

interface FileUploadProps {
  onFilesSelected: (files: UploadFile[]) => void
  onFileRemove: (fileId: string) => void
  maxFiles?: number
  maxSizeBytes?: number
  acceptedTypes?: string[]
  className?: string
}

const DEFAULT_MAX_SIZE = 50 * 1024 * 1024 // 50MB
const DEFAULT_ACCEPTED_TYPES = ['audio/mpeg', 'audio/wav', 'audio/mp3']

export default function FileUpload({
  onFilesSelected,
  onFileRemove,
  maxFiles = 5,
  maxSizeBytes = DEFAULT_MAX_SIZE,
  acceptedTypes = DEFAULT_ACCEPTED_TYPES,
  className = ''
}: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<UploadFile[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const generateFileId = () => Math.random().toString(36).substring(2, 15)

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      return `Invalid file type. Only ${acceptedTypes.join(', ')} are allowed.`
    }

    // Check file size
    if (file.size > maxSizeBytes) {
      const maxSizeMB = maxSizeBytes / (1024 * 1024)
      return `File size too large. Maximum ${maxSizeMB}MB allowed.`
    }

    return null
  }

  const processFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files)
    const newFiles: UploadFile[] = []

    // Check if adding these files would exceed the max limit
    if (uploadedFiles.length + fileArray.length > maxFiles) {
      alert(`Maximum ${maxFiles} files allowed`)
      return
    }

    fileArray.forEach(file => {
      const validationError = validateFile(file)
      
      const uploadFile: UploadFile = {
        file,
        id: generateFileId(),
        progress: 0,
        status: validationError ? 'error' : 'pending',
        error: validationError || undefined
      }

      newFiles.push(uploadFile)
    })

    setUploadedFiles(prev => [...prev, ...newFiles])
    onFilesSelected(newFiles)
  }, [uploadedFiles.length, maxFiles, maxSizeBytes, acceptedTypes, onFilesSelected])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      processFiles(files)
    }
  }, [processFiles])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      processFiles(files)
    }
    // Reset input value to allow re-uploading the same file
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [processFiles])

  const handleRemoveFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId))
    onFileRemove(fileId)
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return <File className="w-5 h-5 text-gray-400" />
      case 'uploading':
        return <Loader className="w-5 h-5 text-blue-500 animate-spin" />
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />
      default:
        return <File className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return 'border-gray-600 bg-gray-800'
      case 'uploading':
        return 'border-blue-500 bg-blue-900/20'
      case 'success':
        return 'border-green-500 bg-green-900/20'
      case 'error':
        return 'border-red-500 bg-red-900/20'
      default:
        return 'border-gray-600 bg-gray-800'
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drop Zone */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={openFileDialog}
        className={`
          relative border-2 border-dashed rounded-xl p-8 text-center cursor-pointer
          transition-all duration-200 ease-in-out
          ${isDragging 
            ? 'border-purple-400 bg-purple-500/10 scale-105' 
            : 'border-gray-600 bg-gray-800/50 hover:border-purple-500 hover:bg-purple-500/5'
          }
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          className="hidden"
        />

        <div className="space-y-4">
          {/* Upload Icon */}
          <div className={`
            mx-auto w-16 h-16 rounded-full flex items-center justify-center
            transition-colors duration-200
            ${isDragging ? 'bg-purple-500 text-white' : 'bg-gray-700 text-gray-300'}
          `}>
            <Upload className="w-8 h-8" />
          </div>

          {/* Upload Text */}
          <div>
            <p className="text-lg font-semibold text-white mb-2">
              {isDragging ? 'Drop your audio files here' : 'Upload your music'}
            </p>
            <p className="text-gray-400 text-sm">
              Drag & drop your files or{' '}
              <span className="text-purple-400 font-medium">browse</span>
            </p>
          </div>

          {/* File Requirements */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>Supported formats: MP3, WAV</p>
            <p>Maximum {maxFiles} files, {formatFileSize(maxSizeBytes)} each</p>
          </div>
        </div>
      </div>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-300 flex items-center gap-2">
            <Music className="w-4 h-4" />
            Uploaded Files ({uploadedFiles.length}/{maxFiles})
          </h3>

          <div className="space-y-2">
            {uploadedFiles.map(uploadFile => (
              <div
                key={uploadFile.id}
                className={`
                  p-4 rounded-lg border transition-all duration-200
                  ${getStatusColor(uploadFile.status)}
                `}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    {/* Status Icon */}
                    <div className="mt-0.5">
                      {getStatusIcon(uploadFile.status)}
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-white truncate">
                        {uploadFile.file.name}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {formatFileSize(uploadFile.file.size)} • {uploadFile.file.type}
                      </p>

                      {/* Error Message */}
                      {uploadFile.error && (
                        <p className="text-xs text-red-400 mt-1">
                          {uploadFile.error}
                        </p>
                      )}

                      {/* Progress Bar */}
                      {uploadFile.status === 'uploading' && (
                        <div className="mt-2">
                          <div className="flex justify-between text-xs text-gray-400 mb-1">
                            <span>Uploading...</span>
                            <span>{uploadFile.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-1.5">
                            <div
                              className="bg-purple-500 h-1.5 rounded-full transition-all duration-300"
                              style={{ width: `${uploadFile.progress}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Remove Button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleRemoveFile(uploadFile.id)
                    }}
                    className="p-1 rounded-full hover:bg-gray-700 transition-colors"
                    aria-label="Remove file"
                  >
                    <X className="w-4 h-4 text-gray-400 hover:text-red-400" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
} 