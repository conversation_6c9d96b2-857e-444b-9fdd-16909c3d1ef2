import { test, expect, Page } from '@playwright/test'

// Audio compatibility test across browsers
test.describe('Audio Compatibility Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the main page
    await page.goto('/')
    
    // Wait for the app to load
    await page.waitForLoadState('networkidle')
    
    // Handle any audio permission prompts or user interaction requirements
    await enableAudioIfNeeded(page)
  })

  test('should detect browser audio capabilities', async ({ page, browserName }) => {
    // Inject browser testing script
    const audioCapabilities = await page.evaluate(() => {
      const audio = document.createElement('audio')
      return {
        mp3: audio.canPlayType('audio/mpeg') !== '',
        wav: audio.canPlayType('audio/wav') !== '',
        ogg: audio.canPlayType('audio/ogg') !== '',
        webm: audio.canPlayType('audio/webm') !== '',
        aac: audio.canPlayType('audio/aac') !== '',
        webAudio: !!(window.AudioContext || window.webkitAudioContext),
        mediaSession: 'mediaSession' in navigator
      }
    })

    console.log(`Audio capabilities for ${browserName}:`, audioCapabilities)

    // All browsers should support at least MP3
    expect(audioCapabilities.mp3).toBe(true)
    
    // WebAudio should be supported in modern browsers
    expect(audioCapabilities.webAudio).toBe(true)
    
    // Browser-specific expectations
    if (browserName === 'webkit') {
      // Safari specifics
      expect(audioCapabilities.aac).toBe(true)
    } else if (browserName === 'firefox') {
      // Firefox specifics
      expect(audioCapabilities.ogg).toBe(true)
    } else if (browserName === 'chromium') {
      // Chrome specifics
      expect(audioCapabilities.webm).toBe(true)
    }
  })

  test('should handle audio format fallbacks', async ({ page }) => {
    // Test audio source selection logic
    const fallbackTest = await page.evaluate(() => {
      const sources = [
        { format: 'flac', src: 'test.flac', type: 'audio/flac' },
        { format: 'ogg', src: 'test.ogg', type: 'audio/ogg' },
        { format: 'mp3', src: 'test.mp3', type: 'audio/mpeg' }
      ]
      
      const audio = document.createElement('audio')
      const supportedFormats = sources.filter(source => 
        audio.canPlayType(source.type) !== ''
      )
      
      return {
        totalFormats: sources.length,
        supportedFormats: supportedFormats.length,
        firstSupported: supportedFormats[0]?.format || 'none'
      }
    })

    expect(fallbackTest.supportedFormats).toBeGreaterThan(0)
    expect(['flac', 'ogg', 'mp3']).toContain(fallbackTest.firstSupported)
  })

  test('should enable audio context on user interaction', async ({ page, browserName }) => {
    // Test audio context activation
    const audioContextTest = await page.evaluate(async () => {
      if (!(window.AudioContext || window.webkitAudioContext)) {
        return { error: 'No AudioContext support' }
      }

      const AudioContext = window.AudioContext || window.webkitAudioContext
      const context = new AudioContext()
      
      const initialState = context.state
      
      // Try to resume context (simulates user interaction)
      if (context.state === 'suspended') {
        await context.resume()
      }
      
      return {
        initialState,
        finalState: context.state,
        sampleRate: context.sampleRate
      }
    })

    if ('error' in audioContextTest) {
      test.skip('AudioContext not supported in this browser')
    }

    expect(['suspended', 'running']).toContain(audioContextTest.initialState)
    expect(audioContextTest.sampleRate).toBeGreaterThan(0)
  })

  test('should handle autoplay policies correctly', async ({ page, browserName }) => {
    // Test autoplay behavior across browsers
    const autoplayTest = await page.evaluate(async () => {
      const audio = new Audio()
      audio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA'
      
      const results = {
        mutedAutoplay: false,
        unmutedAutoplay: false,
        error: null
      }
      
      try {
        // Test muted autoplay
        audio.muted = true
        await audio.play()
        results.mutedAutoplay = true
        audio.pause()
        
        // Test unmuted autoplay (likely to fail without user gesture)
        audio.muted = false
        await audio.play()
        results.unmutedAutoplay = true
        audio.pause()
      } catch (error) {
        results.error = error.name
      }
      
      return results
    })

    // Muted autoplay should work in most modern browsers
    if (browserName !== 'webkit') {
      expect(autoplayTest.mutedAutoplay).toBe(true)
    }
    
    // Unmuted autoplay typically requires user gesture
    if (autoplayTest.error) {
      expect(['NotAllowedError', 'AbortError']).toContain(autoplayTest.error)
    }
  })

  test('should render audio controls correctly', async ({ page }) => {
    // Navigate to a page with audio controls (assuming upload or player page exists)
    const hasAudioControls = await page.evaluate(() => {
      // Check for custom audio control elements
      const audioElements = document.querySelectorAll('audio, [data-testid*="audio"], [class*="audio"]')
      return audioElements.length > 0
    })

    // This test might need adjustment based on actual component structure
    console.log('Audio controls found:', hasAudioControls)
  })

  test('should handle network quality audio selection', async ({ page, browserName }) => {
    // Test network-based quality selection
    const networkTest = await page.evaluate(() => {
      const connection = (navigator as any).connection
      if (!connection) return { error: 'No network information available' }
      
      const getQualityForConnection = (effectiveType: string, downlink: number) => {
        if (downlink < 0.5 || effectiveType === '2g') return 'low'
        if (downlink < 2 || effectiveType === '3g') return 'medium'
        if (downlink < 10 || effectiveType === '4g') return 'high'
        return 'lossless'
      }
      
      return {
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        recommendedQuality: getQualityForConnection(connection.effectiveType, connection.downlink)
      }
    })

    if ('error' in networkTest) {
      test.skip('Network information API not available')
    }

    expect(['slow-2g', '2g', '3g', '4g', 'unknown']).toContain(networkTest.effectiveType)
    expect(['low', 'medium', 'high', 'lossless']).toContain(networkTest.recommendedQuality)
  })

  test('should handle mobile-specific audio features', async ({ page, browserName, isMobile }) => {
    if (!isMobile) {
      test.skip('Mobile-specific test')
    }

    const mobileAudioTest = await page.evaluate(() => {
      const audio = document.createElement('audio')
      
      return {
        playsinline: audio.hasAttribute('playsinline') || audio.playsInline !== undefined,
        webkitPlaysinline: audio.hasAttribute('webkit-playsinline'),
        touchEvents: 'ontouchstart' in window,
        orientationChange: 'onorientationchange' in window
      }
    })

    // Mobile browsers should support touch events
    expect(mobileAudioTest.touchEvents).toBe(true)
    
    // iOS Safari should support playsinline
    if (browserName === 'webkit' && isMobile) {
      expect(mobileAudioTest.playsinline).toBe(true)
    }
  })

  test('should maintain audio state during navigation', async ({ page }) => {
    // This would test audio state persistence
    // Skip if no audio functionality is available on current page
    const hasAudio = await page.evaluate(() => {
      return document.querySelector('audio') !== null
    })
    
    if (!hasAudio) {
      test.skip('No audio elements found for state testing')
    }
    
    // Test would involve playing audio, navigating, and checking state
    console.log('Audio state persistence test - implementation depends on app structure')
  })
})

// Helper function to enable audio interaction
async function enableAudioIfNeeded(page: Page) {
  try {
    // Look for any audio interaction prompts or buttons
    const audioEnableButton = page.locator('button:has-text("Enable Audio")')
    if (await audioEnableButton.isVisible({ timeout: 2000 })) {
      await audioEnableButton.click()
    }
    
    // Simulate a click to enable audio context (for autoplay policies)
    await page.click('body')
    
    // Wait for any audio initialization
    await page.waitForTimeout(500)
  } catch (error) {
    // Ignore errors if no interaction is needed
    console.log('No audio interaction needed or failed:', error.message)
  }
}

// Performance test for audio loading
test.describe('Audio Performance Tests', () => {
  test('should load audio files efficiently', async ({ page }) => {
    // Test audio loading performance
    const loadingPerformance = await page.evaluate(async () => {
      const start = performance.now()
      
      const audio = new Audio()
      audio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA'
      
      return new Promise((resolve, reject) => {
        audio.oncanplay = () => {
          resolve({
            loadTime: performance.now() - start,
            duration: audio.duration || 0,
            readyState: audio.readyState
          })
        }
        
        audio.onerror = () => {
          reject(new Error('Audio loading failed'))
        }
        
        // Set timeout for loading
        setTimeout(() => {
          reject(new Error('Audio loading timeout'))
        }, 5000)
        
        audio.load()
      })
    })

    expect(loadingPerformance.loadTime).toBeLessThan(1000) // Should load within 1 second
    expect(loadingPerformance.readyState).toBeGreaterThanOrEqual(3) // HAVE_FUTURE_DATA or better
  })

  test('should handle multiple audio sources efficiently', async ({ page }) => {
    // Test concurrent audio loading
    const concurrentTest = await page.evaluate(async () => {
      const sources = [
        'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA',
        'data:audio/mpeg;base64,//tQxAAAAAAAAAAAAAAAAAAA',
        'data:audio/ogg;base64,T2dnUwACAAAAAAAAAAA='
      ]
      
      const start = performance.now()
      
      const loadPromises = sources.map(src => {
        const audio = new Audio()
        audio.src = src
        
        return new Promise((resolve, reject) => {
          audio.oncanplay = () => resolve(true)
          audio.onerror = () => resolve(false) // Don't reject, just mark as failed
          setTimeout(() => resolve(false), 2000) // Timeout after 2 seconds
          audio.load()
        })
      })
      
      const results = await Promise.all(loadPromises)
      
      return {
        totalTime: performance.now() - start,
        successCount: results.filter(Boolean).length,
        totalSources: sources.length
      }
    })

    expect(concurrentTest.totalTime).toBeLessThan(3000) // Should complete within 3 seconds
    expect(concurrentTest.successCount).toBeGreaterThan(0) // At least one should succeed
  })
}) 