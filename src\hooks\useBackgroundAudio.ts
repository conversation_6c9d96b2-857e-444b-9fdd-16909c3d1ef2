'use client'

import { useEffect, useRef } from 'react'

interface BackgroundAudioConfig {
  audioRef: React.RefObject<HTMLAudioElement>
  isPlaying: boolean
}

export function useBackgroundAudio({ audioRef, isPlaying }: BackgroundAudioConfig) {
  const wakeLockRef = useRef<WakeLockSentinel | null>(null)
  const visibilityTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Request wake lock to prevent screen from sleeping during playback
  const requestWakeLock = async () => {
    if ('wakeLock' in navigator && isPlaying) {
      try {
        wakeLockRef.current = await navigator.wakeLock.request('screen')
        console.log('Wake lock acquired')
      } catch (error) {
        console.warn('Failed to acquire wake lock:', error)
      }
    }
  }

  // Release wake lock
  const releaseWakeLock = () => {
    if (wakeLockRef.current) {
      wakeLockRef.current.release()
      wakeLockRef.current = null
      console.log('Wake lock released')
    }
  }

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!audioRef.current) return

      if (document.hidden) {
        // Page is now hidden (backgrounded)
        console.log('Page backgrounded - continuing audio playback')
        
        // Ensure audio continues playing in background
        if (isPlaying && audioRef.current.paused) {
          // Attempt to resume playback after a short delay
          visibilityTimeoutRef.current = setTimeout(() => {
            if (audioRef.current && !audioRef.current.paused) {
              audioRef.current.play().catch(console.warn)
            }
          }, 100)
        }
      } else {
        // Page is now visible (foregrounded)
        console.log('Page foregrounded')
        
        // Clear any pending visibility timeout
        if (visibilityTimeoutRef.current) {
          clearTimeout(visibilityTimeoutRef.current)
          visibilityTimeoutRef.current = null
        }

        // Re-acquire wake lock if playing
        if (isPlaying) {
          requestWakeLock()
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      if (visibilityTimeoutRef.current) {
        clearTimeout(visibilityTimeoutRef.current)
      }
    }
  }, [audioRef, isPlaying])

  // Handle page unload/beforeunload
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Release wake lock before page unloads
      releaseWakeLock()
    }

    const handleUnload = () => {
      // Ensure audio is paused on page unload
      if (audioRef.current) {
        audioRef.current.pause()
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('unload', handleUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('unload', handleUnload)
    }
  }, [audioRef])

  // Handle audio interruptions (phone calls, etc.)
  useEffect(() => {
    if (!audioRef.current) return

    const audio = audioRef.current

    const handleInterruption = () => {
      console.log('Audio interrupted')
      // Audio will automatically pause, we don't need to do anything
    }

    const handleInterruptionEnd = () => {
      console.log('Audio interruption ended')
      // Optionally resume playback after interruption
      if (isPlaying) {
        audio.play().catch(console.warn)
      }
    }

    // These events are primarily for iOS
    audio.addEventListener('webkitbeginfullscreen', handleInterruption)
    audio.addEventListener('webkitendfullscreen', handleInterruptionEnd)

    return () => {
      audio.removeEventListener('webkitbeginfullscreen', handleInterruption)
      audio.removeEventListener('webkitendfullscreen', handleInterruptionEnd)
    }
  }, [audioRef, isPlaying])

  // Manage wake lock based on playback state
  useEffect(() => {
    if (isPlaying) {
      requestWakeLock()
    } else {
      releaseWakeLock()
    }

    // Cleanup on unmount
    return () => {
      releaseWakeLock()
    }
  }, [isPlaying])

  // Handle audio focus for Android
  useEffect(() => {
    if (!audioRef.current) return

    const audio = audioRef.current

    // Set audio session category for better background playback
    const setAudioSessionCategory = () => {
      try {
        // This is more of a hint for the browser
        audio.setAttribute('preload', 'auto')
        audio.setAttribute('controls', 'false')
        
        // For iOS, ensure playsinline is set
        audio.setAttribute('playsinline', 'true')
        audio.setAttribute('webkit-playsinline', 'true')
      } catch (error) {
        console.warn('Failed to set audio session category:', error)
      }
    }

    setAudioSessionCategory()
  }, [audioRef])

  // Function to handle audio focus changes (for manual use)
  const handleAudioFocusChange = (hasFocus: boolean) => {
    if (!audioRef.current) return

    if (hasFocus) {
      // Gained audio focus - can resume playback
      if (isPlaying && audioRef.current.paused) {
        audioRef.current.play().catch(console.warn)
      }
    } else {
      // Lost audio focus - should pause playback
      if (!audioRef.current.paused) {
        audioRef.current.pause()
      }
    }
  }

  return {
    requestWakeLock,
    releaseWakeLock,
    handleAudioFocusChange
  }
} 