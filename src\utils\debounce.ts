/**
 * Debounce function to limit the rate at which a function can fire
 * @param func The function to debounce
 * @param wait The number of milliseconds to delay
 * @param immediate Whether to trigger the function on the leading edge
 * @returns The debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(later, wait)
    
    if (callNow) {
      func(...args)
    }
  }
}

/**
 * Throttle function to limit the rate at which a function can fire
 * @param func The function to throttle
 * @param limit The number of milliseconds to limit
 * @returns The throttled function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * Advanced debounce with immediate execution option
 * Can execute the function immediately on the first call
 */
export function debounceAdvanced<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    const callNow = immediate && !timeoutId

    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      timeoutId = null
      if (!immediate) {
        func(...args)
      }
    }, delay)

    if (callNow) {
      func(...args)
    }
  }
}

/**
 * Debounce with promise support
 * Returns a promise that resolves with the function result
 */
export function debouncePromise<T extends (...args: any[]) => Promise<any>>(
  func: T,
  delay: number
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  let timeoutId: NodeJS.Timeout | null = null
  let resolvePromise: ((value: any) => void) | null = null
  let rejectPromise: ((reason: any) => void) | null = null

  return (...args: Parameters<T>): Promise<ReturnType<T>> => {
    return new Promise((resolve, reject) => {
      // Clear previous timeout and reject previous promise
      if (timeoutId) {
        clearTimeout(timeoutId)
        if (rejectPromise) {
          rejectPromise(new Error('Debounced call cancelled'))
        }
      }

      resolvePromise = resolve
      rejectPromise = reject

      timeoutId = setTimeout(async () => {
        try {
          const result = await func(...args)
          if (resolvePromise) {
            resolvePromise(result)
          }
        } catch (error) {
          if (rejectPromise) {
            rejectPromise(error)
          }
        } finally {
          timeoutId = null
          resolvePromise = null
          rejectPromise = null
        }
      }, delay)
    })
  }
} 