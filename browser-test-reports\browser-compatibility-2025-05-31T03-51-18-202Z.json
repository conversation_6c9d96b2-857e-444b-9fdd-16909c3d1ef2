{"timestamp": "2025-05-31T03:55:10.945Z", "summary": {"totalBrowsers": 5, "successfulTests": 4, "failedTests": 1, "desktopBrowsers": 3, "mobileBrowsers": 2, "overallCompatibility": 80}, "browsers": {"chromium": {"tests": [], "summary": {"passed": 0, "failed": 0, "skipped": 0}, "output": "{\n  \"config\": {\n    \"configFile\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\playwright.config.ts\",\n    \"rootDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n    \"forbidOnly\": false,\n    \"fullyParallel\": true,\n    \"globalSetup\": null,\n    \"globalTeardown\": null,\n    \"globalTimeout\": 0,\n    \"grep\": {},\n    \"grepInvert\": null,\n    \"maxFailures\": 0,\n    \"metadata\": {\n      \"actualWorkers\": 2\n    },\n    \"preserveOutput\": \"always\",\n    \"reporter\": [\n      [\n        \"json\"\n      ]\n    ],\n    \"reportSlowTests\": {\n      \"max\": 5,\n      \"threshold\": 300000\n    },\n    \"quiet\": false,\n    \"projects\": [\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"chromium\",\n        \"name\": \"chromium\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"firefox\",\n        \"name\": \"firefox\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"webkit\",\n        \"name\": \"webkit\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"mobile-chrome\",\n        \"name\": \"mobile-chrome\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"mobile-safari\",\n        \"name\": \"mobile-safari\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"microsoft-edge\",\n        \"name\": \"microsoft-edge\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"tablet\",\n        \"name\": \"tablet\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      }\n    ],\n    \"shard\": null,\n    \"updateSnapshots\": \"missing\",\n    \"updateSourceMethod\": \"patch\",\n    \"version\": \"1.52.0\",\n    \"workers\": 2,\n    \"webServer\": {\n      \"command\": \"npm run dev\",\n      \"url\": \"http://localhost:3003\",\n      \"reuseExistingServer\": true,\n      \"timeout\": 120000\n    }\n  },\n  \"suites\": [\n    {\n      \"title\": \"audio-compatibility.spec.ts\",\n      \"file\": \"audio-compatibility.spec.ts\",\n      \"column\": 0,\n      \"line\": 0,\n      \"specs\": [],\n      \"suites\": [\n        {\n          \"title\": \"Audio Compatibility Tests\",\n          \"file\": \"audio-compatibility.spec.ts\",\n          \"line\": 4,\n          \"column\": 6,\n          \"specs\": [\n            {\n              \"title\": \"should detect browser audio capabilities\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"passed\",\n                      \"duration\": 14049,\n                      \"errors\": [],\n                      \"stdout\": [\n                        {\n                          \"text\": \"Audio capabilities for chromium: {\\n  mp3: \\u001b[33mtrue\\u001b[39m,\\n  wav: \\u001b[33mtrue\\u001b[39m,\\n  ogg: \\u001b[33mtrue\\u001b[39m,\\n  webm: \\u001b[33mtrue\\u001b[39m,\\n  aac: \\u001b[33mfalse\\u001b[39m,\\n  webAudio: \\u001b[33mtrue\\u001b[39m,\\n  mediaSession: \\u001b[33mtrue\\u001b[39m\\n}\\n\"\n                        }\n                      ],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:51:28.788Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-1cc4184a696e03c3e916\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 17,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle audio format fallbacks\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 13182,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:51:28.576Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-9b94a9375855c6fc76b5\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 53,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should enable audio context on user interaction\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 8826,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:51:42.480Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-e4685227bfaea69f9a3f\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 78,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle autoplay policies correctly\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"failed\",\n                      \"duration\": 8332,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:143:42\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 42,\n                          \"line\": 143\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 141 |\\u001b[39m     \\u001b[90m// Muted autoplay should work in most modern browsers\\u001b[39m\\n \\u001b[90m 142 |\\u001b[39m     \\u001b[36mif\\u001b[39m (browserName \\u001b[33m!==\\u001b[39m \\u001b[32m'webkit'\\u001b[39m) {\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 143 |\\u001b[39m       expect(autoplayTest\\u001b[33m.\\u001b[39mmutedAutoplay)\\u001b[33m.\\u001b[39mtoBe(\\u001b[36mtrue\\u001b[39m)\\n \\u001b[90m     |\\u001b[39m                                          \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 144 |\\u001b[39m     }\\n \\u001b[90m 145 |\\u001b[39m     \\n \\u001b[90m 146 |\\u001b[39m     \\u001b[90m// Unmuted autoplay typically requires user gesture\\u001b[39m\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 42,\n                            \"line\": 143\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n\\n  141 |     // Muted autoplay should work in most modern browsers\\n  142 |     if (browserName !== 'webkit') {\\n> 143 |       expect(autoplayTest.mutedAutoplay).toBe(true)\\n      |                                          ^\\n  144 |     }\\n  145 |     \\n  146 |     // Unmuted autoplay typically requires user gesture\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:143:42\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:51:43.325Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-chromium\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-chromium\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-chromium\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 42,\n                        \"line\": 143\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-2415de0a0047c50ec07b\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 110,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should render audio controls correctly\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 6610,\n                      \"errors\": [],\n                      \"stdout\": [\n                        {\n                          \"text\": \"Audio controls found: \\u001b[33mfalse\\u001b[39m\\n\"\n                        }\n                      ],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:51:51.319Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-cd14c09a9da449d21ea6\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 152,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle network quality audio selection\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 2,\n                      \"parallelIndex\": 0,\n                      \"status\": \"passed\",\n                      \"duration\": 5870,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:51:54.055Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-edb2cde9b9776f488d54\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 164,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle mobile-specific audio features\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"skipped\",\n                      \"duration\": 6202,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:51:57.937Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-2b1c2620e5591157ed35\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 192,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should maintain audio state during navigation\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 2,\n                      \"parallelIndex\": 0,\n                      \"status\": \"skipped\",\n                      \"duration\": 5250,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:52:00.218Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-3c9349deb48aed013395\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 217,\n              \"column\": 7\n            }\n          ]\n        },\n        {\n          \"title\": \"Audio Performance Tests\",\n          \"file\": \"audio-compatibility.spec.ts\",\n          \"line\": 254,\n          \"column\": 6,\n          \"specs\": [\n            {\n              \"title\": \"should load audio files efficiently\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"failed\",\n                      \"duration\": 266,\n                      \"error\": {\n                        \"message\": \"Error: page.evaluate: Error: Audio loading failed\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\",\n                        \"stack\": \"Error: page.evaluate: Error: Audio loading failed\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:257:43\",\n                        \"location\": {\n                          \"file\": \"eval at evaluate (:313:29), <anonymous>\",\n                          \"column\": 18,\n                          \"line\": 14\n                        }\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"eval at evaluate (:313:29), <anonymous>\",\n                            \"column\": 18,\n                            \"line\": 14\n                          },\n                          \"message\": \"Error: page.evaluate: Error: Audio loading failed\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:257:43\"\n                        },\n                        {\n                          \"message\": \"Error: ENOENT: no such file or directory, open 'D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\eval at evaluate (:313:29), <anonymous>'\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:52:04.147Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--48620-oad-audio-files-efficiently-chromium\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--48620-oad-audio-files-efficiently-chromium\\\\video.webm\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"eval at evaluate (:313:29), <anonymous>\",\n                        \"column\": 18,\n                        \"line\": 14\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-291796f4ed5de876839f\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 255,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle multiple audio sources efficiently\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"chromium\",\n                  \"projectName\": \"chromium\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 3,\n                      \"parallelIndex\": 1,\n                      \"status\": \"failed\",\n                      \"duration\": 498,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:322:41\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 41,\n                          \"line\": 322\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 320 |\\u001b[39m\\n \\u001b[90m 321 |\\u001b[39m     expect(concurrentTest\\u001b[33m.\\u001b[39mtotalTime)\\u001b[33m.\\u001b[39mtoBeLessThan(\\u001b[35m3000\\u001b[39m) \\u001b[90m// Should complete within 3 seconds\\u001b[39m\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 322 |\\u001b[39m     expect(concurrentTest\\u001b[33m.\\u001b[39msuccessCount)\\u001b[33m.\\u001b[39mtoBeGreaterThan(\\u001b[35m0\\u001b[39m) \\u001b[90m// At least one should succeed\\u001b[39m\\n \\u001b[90m     |\\u001b[39m                                         \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 323 |\\u001b[39m   })\\n \\u001b[90m 324 |\\u001b[39m }) \\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 41,\n                            \"line\": 322\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\\n\\n  320 |\\n  321 |     expect(concurrentTest.totalTime).toBeLessThan(3000) // Should complete within 3 seconds\\n> 322 |     expect(concurrentTest.successCount).toBeGreaterThan(0) // At least one should succeed\\n      |                                         ^\\n  323 |   })\\n  324 | }) \\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:322:41\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:52:06.118Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-chromium\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-chromium\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-chromium\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 41,\n                        \"line\": 322\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-bd530bdcc73643dace0a\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 289,\n              \"column\": 7\n            }\n          ]\n        }\n      ]\n    }\n  ],\n  \"errors\": [],\n  \"stats\": {\n    \"startTime\": \"2025-05-31T03:51:23.519Z\",\n    \"duration\": 43372.11,\n    \"expected\": 5,\n    \"skipped\": 2,\n    \"unexpected\": 3,\n    \"flaky\": 0\n  }\n}\n", "errors": "", "type": "desktop", "success": true}, "firefox": {"type": "desktop", "success": false, "error": "Command failed: npx playwright test --project=firefox --reporter=json\n"}, "webkit": {"tests": [], "summary": {"passed": 0, "failed": 0, "skipped": 0}, "output": "{\n  \"config\": {\n    \"configFile\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\playwright.config.ts\",\n    \"rootDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n    \"forbidOnly\": false,\n    \"fullyParallel\": true,\n    \"globalSetup\": null,\n    \"globalTeardown\": null,\n    \"globalTimeout\": 0,\n    \"grep\": {},\n    \"grepInvert\": null,\n    \"maxFailures\": 0,\n    \"metadata\": {\n      \"actualWorkers\": 2\n    },\n    \"preserveOutput\": \"always\",\n    \"reporter\": [\n      [\n        \"json\"\n      ]\n    ],\n    \"reportSlowTests\": {\n      \"max\": 5,\n      \"threshold\": 300000\n    },\n    \"quiet\": false,\n    \"projects\": [\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"chromium\",\n        \"name\": \"chromium\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"firefox\",\n        \"name\": \"firefox\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"webkit\",\n        \"name\": \"webkit\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"mobile-chrome\",\n        \"name\": \"mobile-chrome\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"mobile-safari\",\n        \"name\": \"mobile-safari\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"microsoft-edge\",\n        \"name\": \"microsoft-edge\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"tablet\",\n        \"name\": \"tablet\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      }\n    ],\n    \"shard\": null,\n    \"updateSnapshots\": \"missing\",\n    \"updateSourceMethod\": \"patch\",\n    \"version\": \"1.52.0\",\n    \"workers\": 2,\n    \"webServer\": {\n      \"command\": \"npm run dev\",\n      \"url\": \"http://localhost:3003\",\n      \"reuseExistingServer\": true,\n      \"timeout\": 120000\n    }\n  },\n  \"suites\": [\n    {\n      \"title\": \"audio-compatibility.spec.ts\",\n      \"file\": \"audio-compatibility.spec.ts\",\n      \"column\": 0,\n      \"line\": 0,\n      \"specs\": [],\n      \"suites\": [\n        {\n          \"title\": \"Audio Compatibility Tests\",\n          \"file\": \"audio-compatibility.spec.ts\",\n          \"line\": 4,\n          \"column\": 6,\n          \"specs\": [\n            {\n              \"title\": \"should detect browser audio capabilities\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"failed\",\n                      \"duration\": 11817,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:38:40\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 40,\n                          \"line\": 38\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 36 |\\u001b[39m     \\n \\u001b[90m 37 |\\u001b[39m     \\u001b[90m// WebAudio should be supported in modern browsers\\u001b[39m\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 38 |\\u001b[39m     expect(audioCapabilities\\u001b[33m.\\u001b[39mwebAudio)\\u001b[33m.\\u001b[39mtoBe(\\u001b[36mtrue\\u001b[39m)\\n \\u001b[90m    |\\u001b[39m                                        \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 39 |\\u001b[39m     \\n \\u001b[90m 40 |\\u001b[39m     \\u001b[90m// Browser-specific expectations\\u001b[39m\\n \\u001b[90m 41 |\\u001b[39m     \\u001b[36mif\\u001b[39m (browserName \\u001b[33m===\\u001b[39m \\u001b[32m'webkit'\\u001b[39m) {\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 40,\n                            \"line\": 38\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n\\n  36 |     \\n  37 |     // WebAudio should be supported in modern browsers\\n> 38 |     expect(audioCapabilities.webAudio).toBe(true)\\n     |                                        ^\\n  39 |     \\n  40 |     // Browser-specific expectations\\n  41 |     if (browserName === 'webkit') {\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:38:40\"\n                        }\n                      ],\n                      \"stdout\": [\n                        {\n                          \"text\": \"Audio capabilities for webkit: {\\n  mp3: \\u001b[33mtrue\\u001b[39m,\\n  wav: \\u001b[33mtrue\\u001b[39m,\\n  ogg: \\u001b[33mfalse\\u001b[39m,\\n  webm: \\u001b[33mtrue\\u001b[39m,\\n  aac: \\u001b[33mtrue\\u001b[39m,\\n  webAudio: \\u001b[33mfalse\\u001b[39m,\\n  mediaSession: \\u001b[33mfalse\\u001b[39m\\n}\\n\"\n                        }\n                      ],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:14.582Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--25dc6--browser-audio-capabilities-webkit\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--25dc6--browser-audio-capabilities-webkit\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--25dc6--browser-audio-capabilities-webkit\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 40,\n                        \"line\": 38\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-0ac05dde4139e461f700\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 17,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle audio format fallbacks\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 11368,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:14.646Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-48916ba7f5c303def5a5\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 53,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should enable audio context on user interaction\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"skipped\",\n                      \"duration\": 7159,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:26.301Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-273e7aea7e1a3783474d\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 78,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle autoplay policies correctly\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 2,\n                      \"parallelIndex\": 0,\n                      \"status\": \"failed\",\n                      \"duration\": 6407,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoContain\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // indexOf\\u001b[22m\\n\\nExpected value: \\u001b[32m\\\"NotSupportedError\\\"\\u001b[39m\\nReceived array: \\u001b[31m[\\\"NotAllowedError\\\", \\\"AbortError\\\"]\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoContain\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // indexOf\\u001b[22m\\n\\nExpected value: \\u001b[32m\\\"NotSupportedError\\\"\\u001b[39m\\nReceived array: \\u001b[31m[\\\"NotAllowedError\\\", \\\"AbortError\\\"]\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:148:49\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 49,\n                          \"line\": 148\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 146 |\\u001b[39m     \\u001b[90m// Unmuted autoplay typically requires user gesture\\u001b[39m\\n \\u001b[90m 147 |\\u001b[39m     \\u001b[36mif\\u001b[39m (autoplayTest\\u001b[33m.\\u001b[39merror) {\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 148 |\\u001b[39m       expect([\\u001b[32m'NotAllowedError'\\u001b[39m\\u001b[33m,\\u001b[39m \\u001b[32m'AbortError'\\u001b[39m])\\u001b[33m.\\u001b[39mtoContain(autoplayTest\\u001b[33m.\\u001b[39merror)\\n \\u001b[90m     |\\u001b[39m                                                 \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 149 |\\u001b[39m     }\\n \\u001b[90m 150 |\\u001b[39m   })\\n \\u001b[90m 151 |\\u001b[39m\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 49,\n                            \"line\": 148\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoContain\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // indexOf\\u001b[22m\\n\\nExpected value: \\u001b[32m\\\"NotSupportedError\\\"\\u001b[39m\\nReceived array: \\u001b[31m[\\\"NotAllowedError\\\", \\\"AbortError\\\"]\\u001b[39m\\n\\n  146 |     // Unmuted autoplay typically requires user gesture\\n  147 |     if (autoplayTest.error) {\\n> 148 |       expect(['NotAllowedError', 'AbortError']).toContain(autoplayTest.error)\\n      |                                                 ^\\n  149 |     }\\n  150 |   })\\n  151 |\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:148:49\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:29.561Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-webkit\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-webkit\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-webkit\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 49,\n                        \"line\": 148\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-0d353d3fe6e8b65f5012\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 110,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should render audio controls correctly\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 5839,\n                      \"errors\": [],\n                      \"stdout\": [\n                        {\n                          \"text\": \"Audio controls found: \\u001b[33mfalse\\u001b[39m\\n\"\n                        }\n                      ],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:33.470Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-67aaa3193631a668e706\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 152,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle network quality audio selection\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 3,\n                      \"parallelIndex\": 0,\n                      \"status\": \"skipped\",\n                      \"duration\": 6880,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:38.093Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-00c7ccff21eeea6f330a\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 164,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle mobile-specific audio features\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"skipped\",\n                      \"duration\": 6777,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:39.317Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-6ef669bf87f84d2d24c1\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 192,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should maintain audio state during navigation\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 3,\n                      \"parallelIndex\": 0,\n                      \"status\": \"skipped\",\n                      \"duration\": 6746,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:45.080Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-e20dcb5c8c378ada89ac\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 217,\n              \"column\": 7\n            }\n          ]\n        },\n        {\n          \"title\": \"Audio Performance Tests\",\n          \"file\": \"audio-compatibility.spec.ts\",\n          \"line\": 254,\n          \"column\": 6,\n          \"specs\": [\n            {\n              \"title\": \"should load audio files efficiently\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"failed\",\n                      \"duration\": 1205,\n                      \"error\": {\n                        \"message\": \"Error: page.evaluate: Error: Audio loading failed\",\n                        \"stack\": \"Error: page.evaluate: Error: Audio loading failed\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:257:43\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 43,\n                          \"line\": 257\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 255 |\\u001b[39m   test(\\u001b[32m'should load audio files efficiently'\\u001b[39m\\u001b[33m,\\u001b[39m \\u001b[36masync\\u001b[39m ({ page }) \\u001b[33m=>\\u001b[39m {\\n \\u001b[90m 256 |\\u001b[39m     \\u001b[90m// Test audio loading performance\\u001b[39m\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 257 |\\u001b[39m     \\u001b[36mconst\\u001b[39m loadingPerformance \\u001b[33m=\\u001b[39m \\u001b[36mawait\\u001b[39m page\\u001b[33m.\\u001b[39mevaluate(\\u001b[36masync\\u001b[39m () \\u001b[33m=>\\u001b[39m {\\n \\u001b[90m     |\\u001b[39m                                           \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 258 |\\u001b[39m       \\u001b[36mconst\\u001b[39m start \\u001b[33m=\\u001b[39m performance\\u001b[33m.\\u001b[39mnow()\\n \\u001b[90m 259 |\\u001b[39m       \\n \\u001b[90m 260 |\\u001b[39m       \\u001b[36mconst\\u001b[39m audio \\u001b[33m=\\u001b[39m \\u001b[36mnew\\u001b[39m \\u001b[33mAudio\\u001b[39m()\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 43,\n                            \"line\": 257\n                          },\n                          \"message\": \"Error: page.evaluate: Error: Audio loading failed\\n\\n  255 |   test('should load audio files efficiently', async ({ page }) => {\\n  256 |     // Test audio loading performance\\n> 257 |     const loadingPerformance = await page.evaluate(async () => {\\n      |                                           ^\\n  258 |       const start = performance.now()\\n  259 |       \\n  260 |       const audio = new Audio()\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:257:43\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:46.103Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--48620-oad-audio-files-efficiently-webkit\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--48620-oad-audio-files-efficiently-webkit\\\\video.webm\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 43,\n                        \"line\": 257\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-079a1a7337b5219ab70d\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 255,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle multiple audio sources efficiently\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"webkit\",\n                  \"projectName\": \"webkit\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 4,\n                      \"parallelIndex\": 1,\n                      \"status\": \"failed\",\n                      \"duration\": 1590,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:322:41\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 41,\n                          \"line\": 322\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 320 |\\u001b[39m\\n \\u001b[90m 321 |\\u001b[39m     expect(concurrentTest\\u001b[33m.\\u001b[39mtotalTime)\\u001b[33m.\\u001b[39mtoBeLessThan(\\u001b[35m3000\\u001b[39m) \\u001b[90m// Should complete within 3 seconds\\u001b[39m\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 322 |\\u001b[39m     expect(concurrentTest\\u001b[33m.\\u001b[39msuccessCount)\\u001b[33m.\\u001b[39mtoBeGreaterThan(\\u001b[35m0\\u001b[39m) \\u001b[90m// At least one should succeed\\u001b[39m\\n \\u001b[90m     |\\u001b[39m                                         \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 323 |\\u001b[39m   })\\n \\u001b[90m 324 |\\u001b[39m }) \\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 41,\n                            \"line\": 322\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\\n\\n  320 |\\n  321 |     expect(concurrentTest.totalTime).toBeLessThan(3000) // Should complete within 3 seconds\\n> 322 |     expect(concurrentTest.successCount).toBeGreaterThan(0) // At least one should succeed\\n      |                                         ^\\n  323 |   })\\n  324 | }) \\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:322:41\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:49.922Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-webkit\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-webkit\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-webkit\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 41,\n                        \"line\": 322\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-876e816eb2fed8b9a971\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 289,\n              \"column\": 7\n            }\n          ]\n        }\n      ]\n    }\n  ],\n  \"errors\": [],\n  \"stats\": {\n    \"startTime\": \"2025-05-31T03:53:12.439Z\",\n    \"duration\": 39468.210999999996,\n    \"expected\": 2,\n    \"skipped\": 4,\n    \"unexpected\": 4,\n    \"flaky\": 0\n  }\n}\n", "errors": "", "type": "desktop", "success": true}, "mobile-chrome": {"tests": [], "summary": {"passed": 0, "failed": 0, "skipped": 0}, "output": "{\n  \"config\": {\n    \"configFile\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\playwright.config.ts\",\n    \"rootDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n    \"forbidOnly\": false,\n    \"fullyParallel\": true,\n    \"globalSetup\": null,\n    \"globalTeardown\": null,\n    \"globalTimeout\": 0,\n    \"grep\": {},\n    \"grepInvert\": null,\n    \"maxFailures\": 0,\n    \"metadata\": {\n      \"actualWorkers\": 2\n    },\n    \"preserveOutput\": \"always\",\n    \"reporter\": [\n      [\n        \"json\"\n      ]\n    ],\n    \"reportSlowTests\": {\n      \"max\": 5,\n      \"threshold\": 300000\n    },\n    \"quiet\": false,\n    \"projects\": [\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"chromium\",\n        \"name\": \"chromium\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"firefox\",\n        \"name\": \"firefox\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"webkit\",\n        \"name\": \"webkit\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"mobile-chrome\",\n        \"name\": \"mobile-chrome\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"mobile-safari\",\n        \"name\": \"mobile-safari\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"microsoft-edge\",\n        \"name\": \"microsoft-edge\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"tablet\",\n        \"name\": \"tablet\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      }\n    ],\n    \"shard\": null,\n    \"updateSnapshots\": \"missing\",\n    \"updateSourceMethod\": \"patch\",\n    \"version\": \"1.52.0\",\n    \"workers\": 2,\n    \"webServer\": {\n      \"command\": \"npm run dev\",\n      \"url\": \"http://localhost:3003\",\n      \"reuseExistingServer\": true,\n      \"timeout\": 120000\n    }\n  },\n  \"suites\": [\n    {\n      \"title\": \"audio-compatibility.spec.ts\",\n      \"file\": \"audio-compatibility.spec.ts\",\n      \"column\": 0,\n      \"line\": 0,\n      \"specs\": [],\n      \"suites\": [\n        {\n          \"title\": \"Audio Compatibility Tests\",\n          \"file\": \"audio-compatibility.spec.ts\",\n          \"line\": 4,\n          \"column\": 6,\n          \"specs\": [\n            {\n              \"title\": \"should detect browser audio capabilities\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"passed\",\n                      \"duration\": 8572,\n                      \"errors\": [],\n                      \"stdout\": [\n                        {\n                          \"text\": \"Audio capabilities for chromium: {\\n  mp3: \\u001b[33mtrue\\u001b[39m,\\n  wav: \\u001b[33mtrue\\u001b[39m,\\n  ogg: \\u001b[33mtrue\\u001b[39m,\\n  webm: \\u001b[33mtrue\\u001b[39m,\\n  aac: \\u001b[33mfalse\\u001b[39m,\\n  webAudio: \\u001b[33mtrue\\u001b[39m,\\n  mediaSession: \\u001b[33mtrue\\u001b[39m\\n}\\n\"\n                        }\n                      ],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:58.583Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-ea4b73df142850f86457\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 17,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle audio format fallbacks\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 8621,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:53:58.605Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-0533ab6509b329d578f3\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 53,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should enable audio context on user interaction\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"passed\",\n                      \"duration\": 6358,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:07.471Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-1ce5714cbb5b13b02c90\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 78,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle autoplay policies correctly\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"failed\",\n                      \"duration\": 6284,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:143:42\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 42,\n                          \"line\": 143\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 141 |\\u001b[39m     \\u001b[90m// Muted autoplay should work in most modern browsers\\u001b[39m\\n \\u001b[90m 142 |\\u001b[39m     \\u001b[36mif\\u001b[39m (browserName \\u001b[33m!==\\u001b[39m \\u001b[32m'webkit'\\u001b[39m) {\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 143 |\\u001b[39m       expect(autoplayTest\\u001b[33m.\\u001b[39mmutedAutoplay)\\u001b[33m.\\u001b[39mtoBe(\\u001b[36mtrue\\u001b[39m)\\n \\u001b[90m     |\\u001b[39m                                          \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 144 |\\u001b[39m     }\\n \\u001b[90m 145 |\\u001b[39m     \\n \\u001b[90m 146 |\\u001b[39m     \\u001b[90m// Unmuted autoplay typically requires user gesture\\u001b[39m\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 42,\n                            \"line\": 143\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n\\n  141 |     // Muted autoplay should work in most modern browsers\\n  142 |     if (browserName !== 'webkit') {\\n> 143 |       expect(autoplayTest.mutedAutoplay).toBe(true)\\n      |                                          ^\\n  144 |     }\\n  145 |     \\n  146 |     // Unmuted autoplay typically requires user gesture\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:143:42\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:07.505Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-mobile-chrome\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-mobile-chrome\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-mobile-chrome\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 42,\n                        \"line\": 143\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-5fc7dc55c6e174ab7e63\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 110,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should render audio controls correctly\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"passed\",\n                      \"duration\": 6622,\n                      \"errors\": [],\n                      \"stdout\": [\n                        {\n                          \"text\": \"Audio controls found: \\u001b[33mfalse\\u001b[39m\\n\"\n                        }\n                      ],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:13.836Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-eb524fe3e572f16ab31b\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 152,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle network quality audio selection\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 2,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 6610,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:15.802Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-f81df810da4f36697eef\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 164,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle mobile-specific audio features\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"passed\",\n                      \"duration\": 7481,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:20.465Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-e0129d820b55dfb20300\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 192,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should maintain audio state during navigation\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 2,\n                      \"parallelIndex\": 1,\n                      \"status\": \"skipped\",\n                      \"duration\": 6428,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:22.570Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-23023f28d868abf6b76a\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 217,\n              \"column\": 7\n            }\n          ]\n        },\n        {\n          \"title\": \"Audio Performance Tests\",\n          \"file\": \"audio-compatibility.spec.ts\",\n          \"line\": 254,\n          \"column\": 6,\n          \"specs\": [\n            {\n              \"title\": \"should load audio files efficiently\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"failed\",\n                      \"duration\": 303,\n                      \"error\": {\n                        \"message\": \"Error: page.evaluate: Error: Audio loading failed\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\",\n                        \"stack\": \"Error: page.evaluate: Error: Audio loading failed\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:257:43\",\n                        \"location\": {\n                          \"file\": \"eval at evaluate (:313:29), <anonymous>\",\n                          \"column\": 18,\n                          \"line\": 14\n                        }\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"eval at evaluate (:313:29), <anonymous>\",\n                            \"column\": 18,\n                            \"line\": 14\n                          },\n                          \"message\": \"Error: page.evaluate: Error: Audio loading failed\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\\n    at audio.onerror (eval at evaluate (:313:29), <anonymous>:14:18)\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:257:43\"\n                        },\n                        {\n                          \"message\": \"Error: ENOENT: no such file or directory, open 'D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\eval at evaluate (:313:29), <anonymous>'\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:27.953Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--48620-oad-audio-files-efficiently-mobile-chrome\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--48620-oad-audio-files-efficiently-mobile-chrome\\\\video.webm\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"eval at evaluate (:313:29), <anonymous>\",\n                        \"column\": 18,\n                        \"line\": 14\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-b04907d9586746cc9da5\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 255,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle multiple audio sources efficiently\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-chrome\",\n                  \"projectName\": \"mobile-chrome\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 3,\n                      \"parallelIndex\": 0,\n                      \"status\": \"failed\",\n                      \"duration\": 565,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:322:41\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 41,\n                          \"line\": 322\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 320 |\\u001b[39m\\n \\u001b[90m 321 |\\u001b[39m     expect(concurrentTest\\u001b[33m.\\u001b[39mtotalTime)\\u001b[33m.\\u001b[39mtoBeLessThan(\\u001b[35m3000\\u001b[39m) \\u001b[90m// Should complete within 3 seconds\\u001b[39m\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 322 |\\u001b[39m     expect(concurrentTest\\u001b[33m.\\u001b[39msuccessCount)\\u001b[33m.\\u001b[39mtoBeGreaterThan(\\u001b[35m0\\u001b[39m) \\u001b[90m// At least one should succeed\\u001b[39m\\n \\u001b[90m     |\\u001b[39m                                         \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 323 |\\u001b[39m   })\\n \\u001b[90m 324 |\\u001b[39m }) \\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 41,\n                            \"line\": 322\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\\n\\n  320 |\\n  321 |     expect(concurrentTest.totalTime).toBeLessThan(3000) // Should complete within 3 seconds\\n> 322 |     expect(concurrentTest.successCount).toBeGreaterThan(0) // At least one should succeed\\n      |                                         ^\\n  323 |   })\\n  324 | }) \\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:322:41\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:30.180Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-mobile-chrome\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-mobile-chrome\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-mobile-chrome\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 41,\n                        \"line\": 322\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-18d8ad9d8c0b46cf83d8\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 289,\n              \"column\": 7\n            }\n          ]\n        }\n      ]\n    }\n  ],\n  \"errors\": [],\n  \"stats\": {\n    \"startTime\": \"2025-05-31T03:53:55.945Z\",\n    \"duration\": 35048.323000000004,\n    \"expected\": 6,\n    \"skipped\": 1,\n    \"unexpected\": 3,\n    \"flaky\": 0\n  }\n}\n", "errors": "", "type": "mobile", "success": true}, "mobile-safari": {"tests": [], "summary": {"passed": 0, "failed": 0, "skipped": 0}, "output": "{\n  \"config\": {\n    \"configFile\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\playwright.config.ts\",\n    \"rootDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n    \"forbidOnly\": false,\n    \"fullyParallel\": true,\n    \"globalSetup\": null,\n    \"globalTeardown\": null,\n    \"globalTimeout\": 0,\n    \"grep\": {},\n    \"grepInvert\": null,\n    \"maxFailures\": 0,\n    \"metadata\": {\n      \"actualWorkers\": 2\n    },\n    \"preserveOutput\": \"always\",\n    \"reporter\": [\n      [\n        \"json\"\n      ]\n    ],\n    \"reportSlowTests\": {\n      \"max\": 5,\n      \"threshold\": 300000\n    },\n    \"quiet\": false,\n    \"projects\": [\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"chromium\",\n        \"name\": \"chromium\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"firefox\",\n        \"name\": \"firefox\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"webkit\",\n        \"name\": \"webkit\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"mobile-chrome\",\n        \"name\": \"mobile-chrome\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"mobile-safari\",\n        \"name\": \"mobile-safari\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"microsoft-edge\",\n        \"name\": \"microsoft-edge\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      },\n      {\n        \"outputDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/test-results\",\n        \"repeatEach\": 1,\n        \"retries\": 0,\n        \"metadata\": {\n          \"actualWorkers\": 2\n        },\n        \"id\": \"tablet\",\n        \"name\": \"tablet\",\n        \"testDir\": \"D:/Vinod/Work/Cursor_Projects/Tunami/tests\",\n        \"testIgnore\": [],\n        \"testMatch\": [\n          \"**/*.@(spec|test).?(c|m)[jt]s?(x)\"\n        ],\n        \"timeout\": 30000\n      }\n    ],\n    \"shard\": null,\n    \"updateSnapshots\": \"missing\",\n    \"updateSourceMethod\": \"patch\",\n    \"version\": \"1.52.0\",\n    \"workers\": 2,\n    \"webServer\": {\n      \"command\": \"npm run dev\",\n      \"url\": \"http://localhost:3003\",\n      \"reuseExistingServer\": true,\n      \"timeout\": 120000\n    }\n  },\n  \"suites\": [\n    {\n      \"title\": \"audio-compatibility.spec.ts\",\n      \"file\": \"audio-compatibility.spec.ts\",\n      \"column\": 0,\n      \"line\": 0,\n      \"specs\": [],\n      \"suites\": [\n        {\n          \"title\": \"Audio Compatibility Tests\",\n          \"file\": \"audio-compatibility.spec.ts\",\n          \"line\": 4,\n          \"column\": 6,\n          \"specs\": [\n            {\n              \"title\": \"should detect browser audio capabilities\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 0,\n                      \"parallelIndex\": 0,\n                      \"status\": \"failed\",\n                      \"duration\": 7877,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:38:40\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 40,\n                          \"line\": 38\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 36 |\\u001b[39m     \\n \\u001b[90m 37 |\\u001b[39m     \\u001b[90m// WebAudio should be supported in modern browsers\\u001b[39m\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 38 |\\u001b[39m     expect(audioCapabilities\\u001b[33m.\\u001b[39mwebAudio)\\u001b[33m.\\u001b[39mtoBe(\\u001b[36mtrue\\u001b[39m)\\n \\u001b[90m    |\\u001b[39m                                        \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 39 |\\u001b[39m     \\n \\u001b[90m 40 |\\u001b[39m     \\u001b[90m// Browser-specific expectations\\u001b[39m\\n \\u001b[90m 41 |\\u001b[39m     \\u001b[36mif\\u001b[39m (browserName \\u001b[33m===\\u001b[39m \\u001b[32m'webkit'\\u001b[39m) {\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 40,\n                            \"line\": 38\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n\\n  36 |     \\n  37 |     // WebAudio should be supported in modern browsers\\n> 38 |     expect(audioCapabilities.webAudio).toBe(true)\\n     |                                        ^\\n  39 |     \\n  40 |     // Browser-specific expectations\\n  41 |     if (browserName === 'webkit') {\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:38:40\"\n                        }\n                      ],\n                      \"stdout\": [\n                        {\n                          \"text\": \"Audio capabilities for webkit: {\\n  mp3: \\u001b[33mtrue\\u001b[39m,\\n  wav: \\u001b[33mtrue\\u001b[39m,\\n  ogg: \\u001b[33mfalse\\u001b[39m,\\n  webm: \\u001b[33mtrue\\u001b[39m,\\n  aac: \\u001b[33mtrue\\u001b[39m,\\n  webAudio: \\u001b[33mfalse\\u001b[39m,\\n  mediaSession: \\u001b[33mfalse\\u001b[39m\\n}\\n\"\n                        }\n                      ],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:37.641Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--25dc6--browser-audio-capabilities-mobile-safari\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--25dc6--browser-audio-capabilities-mobile-safari\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--25dc6--browser-audio-capabilities-mobile-safari\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 40,\n                        \"line\": 38\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-976356bae1da9b5efe71\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 17,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle audio format fallbacks\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 7607,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:37.629Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-11be665239eabe5706bd\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 53,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should enable audio context on user interaction\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"skipped\",\n                      \"duration\": 7315,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:45.399Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-61a92c6173244f82e8be\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 78,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle autoplay policies correctly\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 2,\n                      \"parallelIndex\": 0,\n                      \"status\": \"failed\",\n                      \"duration\": 6400,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoContain\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // indexOf\\u001b[22m\\n\\nExpected value: \\u001b[32m\\\"NotSupportedError\\\"\\u001b[39m\\nReceived array: \\u001b[31m[\\\"NotAllowedError\\\", \\\"AbortError\\\"]\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoContain\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // indexOf\\u001b[22m\\n\\nExpected value: \\u001b[32m\\\"NotSupportedError\\\"\\u001b[39m\\nReceived array: \\u001b[31m[\\\"NotAllowedError\\\", \\\"AbortError\\\"]\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:148:49\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 49,\n                          \"line\": 148\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 146 |\\u001b[39m     \\u001b[90m// Unmuted autoplay typically requires user gesture\\u001b[39m\\n \\u001b[90m 147 |\\u001b[39m     \\u001b[36mif\\u001b[39m (autoplayTest\\u001b[33m.\\u001b[39merror) {\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 148 |\\u001b[39m       expect([\\u001b[32m'NotAllowedError'\\u001b[39m\\u001b[33m,\\u001b[39m \\u001b[32m'AbortError'\\u001b[39m])\\u001b[33m.\\u001b[39mtoContain(autoplayTest\\u001b[33m.\\u001b[39merror)\\n \\u001b[90m     |\\u001b[39m                                                 \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 149 |\\u001b[39m     }\\n \\u001b[90m 150 |\\u001b[39m   })\\n \\u001b[90m 151 |\\u001b[39m\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 49,\n                            \"line\": 148\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoContain\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // indexOf\\u001b[22m\\n\\nExpected value: \\u001b[32m\\\"NotSupportedError\\\"\\u001b[39m\\nReceived array: \\u001b[31m[\\\"NotAllowedError\\\", \\\"AbortError\\\"]\\u001b[39m\\n\\n  146 |     // Unmuted autoplay typically requires user gesture\\n  147 |     if (autoplayTest.error) {\\n> 148 |       expect(['NotAllowedError', 'AbortError']).toContain(autoplayTest.error)\\n      |                                                 ^\\n  149 |     }\\n  150 |   })\\n  151 |\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:148:49\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:47.643Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-mobile-safari\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-mobile-safari\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--ce106-autoplay-policies-correctly-mobile-safari\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 49,\n                        \"line\": 148\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-012d94d306783361d911\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 110,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should render audio controls correctly\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"passed\",\n                      \"duration\": 5449,\n                      \"errors\": [],\n                      \"stdout\": [\n                        {\n                          \"text\": \"Audio controls found: \\u001b[33mfalse\\u001b[39m\\n\"\n                        }\n                      ],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:52.727Z\",\n                      \"annotations\": [],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"expected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-bd99278dd0db122a8405\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 152,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle network quality audio selection\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 3,\n                      \"parallelIndex\": 0,\n                      \"status\": \"skipped\",\n                      \"duration\": 6859,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:56.251Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-6049e5a0a2467207d303\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 164,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle mobile-specific audio features\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 1,\n                      \"parallelIndex\": 1,\n                      \"status\": \"failed\",\n                      \"duration\": 5883,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:213:43\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 43,\n                          \"line\": 213\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 211 |\\u001b[39m     \\u001b[90m// iOS Safari should support playsinline\\u001b[39m\\n \\u001b[90m 212 |\\u001b[39m     \\u001b[36mif\\u001b[39m (browserName \\u001b[33m===\\u001b[39m \\u001b[32m'webkit'\\u001b[39m \\u001b[33m&&\\u001b[39m isMobile) {\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 213 |\\u001b[39m       expect(mobileAudioTest\\u001b[33m.\\u001b[39mplaysinline)\\u001b[33m.\\u001b[39mtoBe(\\u001b[36mtrue\\u001b[39m)\\n \\u001b[90m     |\\u001b[39m                                           \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 214 |\\u001b[39m     }\\n \\u001b[90m 215 |\\u001b[39m   })\\n \\u001b[90m 216 |\\u001b[39m\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 43,\n                            \"line\": 213\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBe\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m) // Object.is equality\\u001b[22m\\n\\nExpected: \\u001b[32mtrue\\u001b[39m\\nReceived: \\u001b[31mfalse\\u001b[39m\\n\\n  211 |     // iOS Safari should support playsinline\\n  212 |     if (browserName === 'webkit' && isMobile) {\\n> 213 |       expect(mobileAudioTest.playsinline).toBe(true)\\n      |                                           ^\\n  214 |     }\\n  215 |   })\\n  216 |\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:213:43\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:54:58.185Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--94cee-ile-specific-audio-features-mobile-safari\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--94cee-ile-specific-audio-features-mobile-safari\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--94cee-ile-specific-audio-features-mobile-safari\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 43,\n                        \"line\": 213\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-30a034911def5b2b21a3\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 192,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should maintain audio state during navigation\",\n              \"ok\": true,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [\n                    {\n                      \"type\": \"skip\"\n                    }\n                  ],\n                  \"expectedStatus\": \"skipped\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 3,\n                      \"parallelIndex\": 0,\n                      \"status\": \"skipped\",\n                      \"duration\": 5684,\n                      \"errors\": [],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:55:03.240Z\",\n                      \"annotations\": [\n                        {\n                          \"type\": \"skip\"\n                        }\n                      ],\n                      \"attachments\": []\n                    }\n                  ],\n                  \"status\": \"skipped\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-875d443b6fcd210fc7c8\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 217,\n              \"column\": 7\n            }\n          ]\n        },\n        {\n          \"title\": \"Audio Performance Tests\",\n          \"file\": \"audio-compatibility.spec.ts\",\n          \"line\": 254,\n          \"column\": 6,\n          \"specs\": [\n            {\n              \"title\": \"should load audio files efficiently\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 4,\n                      \"parallelIndex\": 1,\n                      \"status\": \"failed\",\n                      \"duration\": 997,\n                      \"error\": {\n                        \"message\": \"Error: page.evaluate: Error: Audio loading failed\",\n                        \"stack\": \"Error: page.evaluate: Error: Audio loading failed\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:257:43\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 43,\n                          \"line\": 257\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 255 |\\u001b[39m   test(\\u001b[32m'should load audio files efficiently'\\u001b[39m\\u001b[33m,\\u001b[39m \\u001b[36masync\\u001b[39m ({ page }) \\u001b[33m=>\\u001b[39m {\\n \\u001b[90m 256 |\\u001b[39m     \\u001b[90m// Test audio loading performance\\u001b[39m\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 257 |\\u001b[39m     \\u001b[36mconst\\u001b[39m loadingPerformance \\u001b[33m=\\u001b[39m \\u001b[36mawait\\u001b[39m page\\u001b[33m.\\u001b[39mevaluate(\\u001b[36masync\\u001b[39m () \\u001b[33m=>\\u001b[39m {\\n \\u001b[90m     |\\u001b[39m                                           \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 258 |\\u001b[39m       \\u001b[36mconst\\u001b[39m start \\u001b[33m=\\u001b[39m performance\\u001b[33m.\\u001b[39mnow()\\n \\u001b[90m 259 |\\u001b[39m       \\n \\u001b[90m 260 |\\u001b[39m       \\u001b[36mconst\\u001b[39m audio \\u001b[33m=\\u001b[39m \\u001b[36mnew\\u001b[39m \\u001b[33mAudio\\u001b[39m()\\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 43,\n                            \"line\": 257\n                          },\n                          \"message\": \"Error: page.evaluate: Error: Audio loading failed\\n\\n  255 |   test('should load audio files efficiently', async ({ page }) => {\\n  256 |     // Test audio loading performance\\n> 257 |     const loadingPerformance = await page.evaluate(async () => {\\n      |                                           ^\\n  258 |       const start = performance.now()\\n  259 |       \\n  260 |       const audio = new Audio()\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:257:43\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:55:06.551Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--48620-oad-audio-files-efficiently-mobile-safari\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--48620-oad-audio-files-efficiently-mobile-safari\\\\video.webm\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 43,\n                        \"line\": 257\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-15e59d8df3c1e119ff42\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 255,\n              \"column\": 7\n            },\n            {\n              \"title\": \"should handle multiple audio sources efficiently\",\n              \"ok\": false,\n              \"tags\": [],\n              \"tests\": [\n                {\n                  \"timeout\": 30000,\n                  \"annotations\": [],\n                  \"expectedStatus\": \"passed\",\n                  \"projectId\": \"mobile-safari\",\n                  \"projectName\": \"mobile-safari\",\n                  \"results\": [\n                    {\n                      \"workerIndex\": 5,\n                      \"parallelIndex\": 1,\n                      \"status\": \"failed\",\n                      \"duration\": 794,\n                      \"error\": {\n                        \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\",\n                        \"stack\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:322:41\",\n                        \"location\": {\n                          \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                          \"column\": 41,\n                          \"line\": 322\n                        },\n                        \"snippet\": \"\\u001b[0m \\u001b[90m 320 |\\u001b[39m\\n \\u001b[90m 321 |\\u001b[39m     expect(concurrentTest\\u001b[33m.\\u001b[39mtotalTime)\\u001b[33m.\\u001b[39mtoBeLessThan(\\u001b[35m3000\\u001b[39m) \\u001b[90m// Should complete within 3 seconds\\u001b[39m\\n\\u001b[31m\\u001b[1m>\\u001b[22m\\u001b[39m\\u001b[90m 322 |\\u001b[39m     expect(concurrentTest\\u001b[33m.\\u001b[39msuccessCount)\\u001b[33m.\\u001b[39mtoBeGreaterThan(\\u001b[35m0\\u001b[39m) \\u001b[90m// At least one should succeed\\u001b[39m\\n \\u001b[90m     |\\u001b[39m                                         \\u001b[31m\\u001b[1m^\\u001b[22m\\u001b[39m\\n \\u001b[90m 323 |\\u001b[39m   })\\n \\u001b[90m 324 |\\u001b[39m }) \\u001b[0m\"\n                      },\n                      \"errors\": [\n                        {\n                          \"location\": {\n                            \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                            \"column\": 41,\n                            \"line\": 322\n                          },\n                          \"message\": \"Error: \\u001b[2mexpect(\\u001b[22m\\u001b[31mreceived\\u001b[39m\\u001b[2m).\\u001b[22mtoBeGreaterThan\\u001b[2m(\\u001b[22m\\u001b[32mexpected\\u001b[39m\\u001b[2m)\\u001b[22m\\n\\nExpected: > \\u001b[32m0\\u001b[39m\\nReceived:   \\u001b[31m0\\u001b[39m\\n\\n  320 |\\n  321 |     expect(concurrentTest.totalTime).toBeLessThan(3000) // Should complete within 3 seconds\\n> 322 |     expect(concurrentTest.successCount).toBeGreaterThan(0) // At least one should succeed\\n      |                                         ^\\n  323 |   })\\n  324 | }) \\n    at D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts:322:41\"\n                        }\n                      ],\n                      \"stdout\": [],\n                      \"stderr\": [],\n                      \"retry\": 0,\n                      \"startTime\": \"2025-05-31T03:55:09.917Z\",\n                      \"annotations\": [],\n                      \"attachments\": [\n                        {\n                          \"name\": \"screenshot\",\n                          \"contentType\": \"image/png\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-mobile-safari\\\\test-failed-1.png\"\n                        },\n                        {\n                          \"name\": \"video\",\n                          \"contentType\": \"video/webm\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-mobile-safari\\\\video.webm\"\n                        },\n                        {\n                          \"name\": \"_error-context-0\",\n                          \"contentType\": \"text/markdown\",\n                          \"path\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\test-results\\\\audio-compatibility-Audio--99475-e-audio-sources-efficiently-mobile-safari\\\\error-context.md\"\n                        }\n                      ],\n                      \"errorLocation\": {\n                        \"file\": \"D:\\\\Vinod\\\\Work\\\\Cursor_Projects\\\\Tunami\\\\tests\\\\audio-compatibility.spec.ts\",\n                        \"column\": 41,\n                        \"line\": 322\n                      }\n                    }\n                  ],\n                  \"status\": \"unexpected\"\n                }\n              ],\n              \"id\": \"cc0461d0c1b1e0e630ee-65bffc012a343b630b86\",\n              \"file\": \"audio-compatibility.spec.ts\",\n              \"line\": 289,\n              \"column\": 7\n            }\n          ]\n        }\n      ]\n    }\n  ],\n  \"errors\": [],\n  \"stats\": {\n    \"startTime\": \"2025-05-31T03:54:35.033Z\",\n    \"duration\": 35853.915,\n    \"expected\": 2,\n    \"skipped\": 3,\n    \"unexpected\": 5,\n    \"flaky\": 0\n  }\n}\n", "errors": "", "type": "mobile", "success": true}}, "recommendations": [{"type": "error", "browser": "firefox", "message": "firefox testing failed: Command failed: npx playwright test --project=firefox --reporter=json\n", "action": "Check browser compatibility and fix blocking issues"}], "compatibility": {"audioSupport": {"chromium": {"totalTests": 0, "passed": 0, "failed": 0, "score": 0}, "firefox": {"totalTests": 0, "passed": 0, "failed": 0, "score": 0}, "webkit": {"totalTests": 0, "passed": 0, "failed": 0, "score": 0}, "mobile-chrome": {"totalTests": 0, "passed": 0, "failed": 0, "score": 0}, "mobile-safari": {"totalTests": 0, "passed": 0, "failed": 0, "score": 0}}, "webFeatures": {"chromium": {"serviceWorker": false, "webAudio": false, "intersectionObserver": false, "localStorage": false}, "firefox": {"serviceWorker": false, "webAudio": false, "intersectionObserver": false, "localStorage": false}, "webkit": {"serviceWorker": false, "webAudio": false, "intersectionObserver": false, "localStorage": false}, "mobile-chrome": {"serviceWorker": false, "webAudio": false, "intersectionObserver": false, "localStorage": false}, "mobile-safari": {"serviceWorker": false, "webAudio": false, "intersectionObserver": false, "localStorage": false}}, "cssSupport": {"chromium": {"grid": false, "flexbox": false, "customProperties": false, "backdropFilter": false}, "firefox": {"grid": false, "flexbox": false, "customProperties": false, "backdropFilter": false}, "webkit": {"grid": false, "flexbox": false, "customProperties": false, "backdropFilter": false}, "mobile-chrome": {"grid": false, "flexbox": false, "customProperties": false, "backdropFilter": false}, "mobile-safari": {"grid": false, "flexbox": false, "customProperties": false, "backdropFilter": false}}, "performanceScores": {"chromium": {"averageDuration": 0, "testsCount": 0}, "firefox": {"averageDuration": 0, "testsCount": 0}, "webkit": {"averageDuration": 0, "testsCount": 0}, "mobile-chrome": {"averageDuration": 0, "testsCount": 0}, "mobile-safari": {"averageDuration": 0, "testsCount": 0}}}}