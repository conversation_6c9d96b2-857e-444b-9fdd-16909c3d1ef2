'use client'

import { useState } from 'react'
import { Share2, <PERSON><PERSON>, Twitter, Facebook, X, Check } from 'lucide-react'
import { createShareLink } from '@/lib/social'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Modal } from '@/components/ui/Modal'
import { Card } from '@/components/ui/Card'

interface ShareModalProps {
  isOpen: boolean
  onClose: () => void
  type: 'track' | 'playlist'
  id: string
  title: string
  subtitle?: string
}

export function ShareModal({ isOpen, onClose, type, id, title, subtitle }: ShareModalProps) {
  const [shareCode, setShareCode] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [copied, setCopied] = useState(false)
  const [expiresInDays, setExpiresInDays] = useState<number | undefined>(undefined)

  const generateShareLink = async () => {
    try {
      setIsGenerating(true)
      const code = await createShareLink(type, id, expiresInDays)
      setShareCode(code)
    } catch (error) {
      console.error('Error generating share link:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const getShareUrl = () => {
    if (!shareCode) return ''
    return `${window.location.origin}/share/${shareCode}`
  }

  const copyToClipboard = async () => {
    const url = getShareUrl()
    try {
      await navigator.clipboard.writeText(url)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const shareToTwitter = () => {
    const url = getShareUrl()
    const text = `Check out this ${type}: "${title}" on Tunami`
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`
    window.open(twitterUrl, '_blank')
  }

  const shareToFacebook = () => {
    const url = getShareUrl()
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`
    window.open(facebookUrl, '_blank')
  }

  const handleClose = () => {
    setShareCode(null)
    setCopied(false)
    setExpiresInDays(undefined)
    onClose()
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title={`Share ${type}`}>
      <div className="space-y-6">
        {/* Content Preview */}
        <Card className="bg-gray-700/50 border-gray-600 p-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
              <Share2 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-white">{title}</h3>
              {subtitle && <p className="text-gray-400 text-sm">{subtitle}</p>}
              <p className="text-gray-500 text-xs capitalize">{type}</p>
            </div>
          </div>
        </Card>

        {!shareCode ? (
          <div className="space-y-4">
            {/* Expiration Options */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Link Expiration (Optional)
              </label>
              <select
                value={expiresInDays || ''}
                onChange={(e) => setExpiresInDays(e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="">Never expires</option>
                <option value="1">1 day</option>
                <option value="7">1 week</option>
                <option value="30">1 month</option>
                <option value="90">3 months</option>
              </select>
            </div>

            <Button
              onClick={generateShareLink}
              disabled={isGenerating}
              className="w-full flex items-center justify-center gap-2"
            >
              <Share2 className="w-4 h-4" />
              {isGenerating ? 'Generating Link...' : 'Generate Share Link'}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Share URL */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Share Link
              </label>
              <div className="flex gap-2">
                <Input
                  value={getShareUrl()}
                  readOnly
                  className="bg-gray-700 border-gray-600 text-gray-300"
                />
                <Button
                  onClick={copyToClipboard}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  {copied ? 'Copied!' : 'Copy'}
                </Button>
              </div>
            </div>

            {/* Social Sharing */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">
                Share on Social Media
              </label>
              <div className="flex gap-3">
                <Button
                  onClick={shareToTwitter}
                  variant="outline"
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 border-blue-600"
                >
                  <Twitter className="w-4 h-4" />
                  Twitter
                </Button>
                <Button
                  onClick={shareToFacebook}
                  variant="outline"
                  className="flex items-center gap-2 bg-blue-800 hover:bg-blue-900 border-blue-800"
                >
                  <Facebook className="w-4 h-4" />
                  Facebook
                </Button>
              </div>
            </div>

            {/* Generate New Link */}
            <div className="pt-4 border-t border-gray-600">
              <Button
                onClick={() => {
                  setShareCode(null)
                  setCopied(false)
                }}
                variant="outline"
                size="sm"
                className="text-gray-400"
              >
                Generate New Link
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  )
}

// Hook for using the share modal
export function useShareModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [shareData, setShareData] = useState<{
    type: 'track' | 'playlist'
    id: string
    title: string
    subtitle?: string
  } | null>(null)

  const openShareModal = (
    type: 'track' | 'playlist',
    id: string,
    title: string,
    subtitle?: string
  ) => {
    setShareData({ type, id, title, subtitle })
    setIsOpen(true)
  }

  const closeShareModal = () => {
    setIsOpen(false)
    setShareData(null)
  }

  const ShareModalComponent = shareData ? (
    <ShareModal
      isOpen={isOpen}
      onClose={closeShareModal}
      type={shareData.type}
      id={shareData.id}
      title={shareData.title}
      subtitle={shareData.subtitle}
    />
  ) : null

  return {
    ShareModalComponent,
    openShareModal,
    closeShareModal,
    isOpen
  }
} 