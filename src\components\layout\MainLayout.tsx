'use client'

import { useState } from 'react'
import { Menu, X } from 'lucide-react'
import Sidebar from './Sidebar'
import { AudioProvider } from '@/contexts/AudioContext'
import GlobalAudioPlayer from '@/components/audio/GlobalAudioPlayer'
import MiniPlayer from '@/components/audio/MiniPlayer'

interface MainLayoutProps {
  children: React.ReactNode
}

export default function MainLayout({ children }: MainLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <AudioProvider>
      <div className="min-h-screen bg-gray-950 text-white">
        {/* Global Audio Player (hidden) */}
        <GlobalAudioPlayer />
        
        <div className="flex h-screen">
          {/* Sidebar */}
          <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
          
          {/* Main Content */}
          <div className="flex-1 flex flex-col min-w-0">
            {/* Top Bar */}
            <header className="bg-gray-900 border-b border-gray-800 px-4 py-3 flex items-center gap-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md hover:bg-gray-800 transition-colors"
              >
                <Menu className="h-5 w-5" />
              </button>
              
              <div className="flex-1">
                <h1 className="text-xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                  Tunami
                </h1>
              </div>
            </header>
            
            {/* Page Content */}
            <main className="flex-1 overflow-auto pb-24">
              {children}
            </main>
          </div>
        </div>
        
        {/* Mini Player */}
        <MiniPlayer />
      </div>
    </AudioProvider>
  )
} 