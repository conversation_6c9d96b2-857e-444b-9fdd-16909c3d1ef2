'use client'

import { useState } from 'react'
import { 
  <PERSON>, 
  <PERSON>Off, 
  Volume2, 
  VolumeX, 
  Clock, 
  Zap,
  Check,
  X,
  <PERSON>ting<PERSON>,
  TestTube,
  Moon,
  Sun
} from 'lucide-react'
import { usePushNotifications } from '@/hooks/usePushNotifications'

interface NotificationSettingsProps {
  className?: string
  showAdvanced?: boolean
  onClose?: () => void
}

export default function NotificationSettings({
  className = '',
  showAdvanced = true,
  onClose
}: NotificationSettingsProps) {
  const {
    permission,
    subscription,
    preferences,
    isLoading,
    error,
    requestPermission,
    subscribe,
    unsubscribe,
    testNotification,
    sendTestPush,
    savePreferences,
    isSupported,
    isGranted,
    isSubscribed,
    canSubscribe
  } = usePushNotifications()

  const [localPreferences, setLocalPreferences] = useState(preferences)
  const [isTesting, setIsTesting] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const handleToggleNotifications = async () => {
    if (isSubscribed) {
      await unsubscribe()
    } else {
      await subscribe()
    }
  }

  const handlePreferenceChange = (key: keyof typeof preferences, value: any) => {
    const newPreferences = { ...localPreferences, [key]: value }
    setLocalPreferences(newPreferences)
  }

  const handleQuietHoursChange = (field: 'enabled' | 'start' | 'end', value: any) => {
    const newQuietHours = { ...localPreferences.quiet_hours, [field]: value }
    const newPreferences = { ...localPreferences, quiet_hours: newQuietHours }
    setLocalPreferences(newPreferences)
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      await savePreferences(localPreferences)
    } finally {
      setIsSaving(false)
    }
  }

  const handleTestNotification = async () => {
    setIsTesting(true)
    try {
      await testNotification()
    } finally {
      setTimeout(() => setIsTesting(false), 2000)
    }
  }

  const handleTestPush = async () => {
    setIsTesting(true)
    try {
      await sendTestPush()
    } finally {
      setTimeout(() => setIsTesting(false), 2000)
    }
  }

  const getPermissionIcon = () => {
    switch (permission.permission) {
      case 'granted':
        return <Bell className="w-5 h-5 text-green-500" />
      case 'denied':
        return <BellOff className="w-5 h-5 text-red-500" />
      default:
        return <Bell className="w-5 h-5 text-yellow-500" />
    }
  }

  const getPermissionStatus = () => {
    switch (permission.permission) {
      case 'granted':
        return { text: 'Allowed', color: 'text-green-600' }
      case 'denied':
        return { text: 'Blocked', color: 'text-red-600' }
      default:
        return { text: 'Not Requested', color: 'text-yellow-600' }
    }
  }

  if (!isSupported) {
    return (
      <div className={`p-6 bg-gray-100 dark:bg-gray-900 rounded-lg ${className}`}>
        <div className="text-center">
          <BellOff className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Notifications Not Supported
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Your browser or device doesn't support push notifications.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={`max-w-2xl mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <Settings className="w-6 h-6 text-purple-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Notification Settings
          </h2>
        </div>
        
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        )}
      </div>

      <div className="p-6 space-y-6">
        {/* Permission Status */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              {getPermissionIcon()}
              <span className="font-medium text-gray-900 dark:text-white">
                Notification Permission
              </span>
            </div>
            <span className={`text-sm font-medium ${getPermissionStatus().color}`}>
              {getPermissionStatus().text}
            </span>
          </div>
          
          {error && (
            <div className="mb-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
            </div>
          )}

          <div className="flex space-x-3">
            {!isGranted && canSubscribe && (
              <button
                onClick={requestPermission}
                disabled={isLoading}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
              >
                {isLoading ? 'Requesting...' : 'Enable Notifications'}
              </button>
            )}
            
            {isGranted && (
              <button
                onClick={handleToggleNotifications}
                disabled={isLoading}
                className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors ${
                  isSubscribed
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-green-600 text-white hover:bg-green-700'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {isLoading 
                  ? (isSubscribed ? 'Unsubscribing...' : 'Subscribing...')
                  : (isSubscribed ? 'Disable Push' : 'Enable Push')
                }
              </button>
            )}

            {isGranted && (
              <button
                onClick={handleTestNotification}
                disabled={isTesting}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium flex items-center space-x-2"
              >
                <TestTube className="w-4 h-4" />
                <span>{isTesting ? 'Testing...' : 'Test Local'}</span>
              </button>
            )}

            {isSubscribed && (
              <button
                onClick={handleTestPush}
                disabled={isTesting}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium flex items-center space-x-2"
              >
                <Zap className="w-4 h-4" />
                <span>{isTesting ? 'Sending...' : 'Test Push'}</span>
              </button>
            )}
          </div>
        </div>

        {/* Notification Preferences */}
        {isGranted && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Notification Types
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Volume2 className="w-5 h-5 text-green-500" />
                  <div>
                    <span className="font-medium text-gray-900 dark:text-white">New Tracks</span>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      When new AI-generated tracks are available
                    </p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.newTracks}
                    onChange={(e) => handlePreferenceChange('newTracks', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Zap className="w-5 h-5 text-blue-500" />
                  <div>
                    <span className="font-medium text-gray-900 dark:text-white">AI Updates</span>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Platform updates and new AI features
                    </p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.aiUpdates}
                    onChange={(e) => handlePreferenceChange('aiUpdates', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Bell className="w-5 h-5 text-purple-500" />
                  <div>
                    <span className="font-medium text-gray-900 dark:text-white">Playlist Updates</span>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      When your playlists are updated by others
                    </p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.playlistUpdates}
                    onChange={(e) => handlePreferenceChange('playlistUpdates', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Settings className="w-5 h-5 text-orange-500" />
                  <div>
                    <span className="font-medium text-gray-900 dark:text-white">Social Activity</span>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Likes, comments, and follows
                    </p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.socialActivity}
                    onChange={(e) => handlePreferenceChange('socialActivity', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-gray-500" />
                  <div>
                    <span className="font-medium text-gray-900 dark:text-white">System Updates</span>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Important system and security updates
                    </p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.systemUpdates}
                    onChange={(e) => handlePreferenceChange('systemUpdates', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Quiet Hours */}
        {isGranted && showAdvanced && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Quiet Hours
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Moon className="w-5 h-5 text-indigo-500" />
                  <div>
                    <span className="font-medium text-gray-900 dark:text-white">Enable Quiet Hours</span>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Suppress notifications during specified hours
                    </p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.quiet_hours.enabled}
                    onChange={(e) => handleQuietHoursChange('enabled', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                </label>
              </div>

              {localPreferences.quiet_hours.enabled && (
                <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Start Time
                    </label>
                    <input
                      type="time"
                      value={localPreferences.quiet_hours.start}
                      onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                      className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      End Time
                    </label>
                    <input
                      type="time"
                      value={localPreferences.quiet_hours.end}
                      onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                      className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Subscription Info */}
        {isSubscribed && subscription.endpoint && (
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              Subscription Details
            </h4>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p>Endpoint: {subscription.endpoint.substring(0, 50)}...</p>
              <p className="mt-1">Status: Active</p>
            </div>
          </div>
        )}

        {/* Save Button */}
        {isGranted && (
          <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {isSaving ? 'Saving...' : 'Save Preferences'}
            </button>
          </div>
        )}
      </div>
    </div>
  )
} 