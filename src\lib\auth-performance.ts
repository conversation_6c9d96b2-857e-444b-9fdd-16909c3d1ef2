import { User, Session } from '@supabase/supabase-js'
import type { Profile } from '@/types/database'

// Performance monitoring and optimization utilities for authentication

interface AuthPerformanceMetrics {
  loginStartTime: number
  loginEndTime: number
  profileFetchTime: number
  totalAuthTime: number
  cacheHits: number
  cacheMisses: number
}

interface AuthCacheEntry {
  data: any
  timestamp: number
  ttl: number
}

class AuthPerformanceOptimizer {
  private static instance: AuthPerformanceOptimizer
  private metrics: AuthPerformanceMetrics
  private cache: Map<string, AuthCacheEntry>
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5 minutes
  private readonly MAX_CACHE_SIZE = 100

  constructor() {
    this.metrics = {
      loginStartTime: 0,
      loginEndTime: 0,
      profileFetchTime: 0,
      totalAuthTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    }
    this.cache = new Map()
  }

  static getInstance(): AuthPerformanceOptimizer {
    if (!AuthPerformanceOptimizer.instance) {
      AuthPerformanceOptimizer.instance = new AuthPerformanceOptimizer()
    }
    return AuthPerformanceOptimizer.instance
  }

  // Performance tracking methods
  startLoginTimer(): void {
    this.metrics.loginStartTime = performance.now()
  }

  endLoginTimer(): void {
    this.metrics.loginEndTime = performance.now()
    this.metrics.totalAuthTime = this.metrics.loginEndTime - this.metrics.loginStartTime
  }

  trackProfileFetch(startTime: number): void {
    this.metrics.profileFetchTime = performance.now() - startTime
  }

  getMetrics(): AuthPerformanceMetrics {
    return { ...this.metrics }
  }

  // Cache management
  setCache(key: string, data: any, ttl: number = this.DEFAULT_TTL): void {
    // Implement LRU cache behavior
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  getCache(key: string): any | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.metrics.cacheMisses++
      return null
    }

    // Check if cache entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      this.metrics.cacheMisses++
      return null
    }

    this.metrics.cacheHits++
    return entry.data
  }

  clearCache(): void {
    this.cache.clear()
  }

  // Optimized authentication helpers
  async optimizedProfileFetch(
    userId: string,
    fetchFunction: (id: string) => Promise<Profile | null>
  ): Promise<Profile | null> {
    const cacheKey = `profile_${userId}`
    const cached = this.getCache(cacheKey)
    
    if (cached) {
      return cached
    }

    const startTime = performance.now()
    const profile = await fetchFunction(userId)
    this.trackProfileFetch(startTime)

    if (profile) {
      this.setCache(cacheKey, profile)
    }

    return profile
  }

  // Preload critical resources
  async preloadAuthResources(): Promise<void> {
    try {
      // Preload critical CSS for auth pages
      const authStyles = document.createElement('link')
      authStyles.rel = 'preload'
      authStyles.as = 'style'
      authStyles.href = '/auth-styles.css'
      document.head.appendChild(authStyles)

      // Preconnect to OAuth providers
      const googlePreconnect = document.createElement('link')
      googlePreconnect.rel = 'preconnect'
      googlePreconnect.href = 'https://accounts.google.com'
      document.head.appendChild(googlePreconnect)

      const githubPreconnect = document.createElement('link')
      githubPreconnect.rel = 'preconnect'
      githubPreconnect.href = 'https://github.com'
      document.head.appendChild(githubPreconnect)
    } catch (error) {
      console.warn('Failed to preload auth resources:', error)
    }
  }

  // Session optimization
  optimizeSessionStorage(): void {
    try {
      // Clean up old session data
      const keys = Object.keys(localStorage)
      const authKeys = keys.filter(key => key.startsWith('tunami-auth') || key.startsWith('sb-'))
      
      authKeys.forEach(key => {
        try {
          const data = localStorage.getItem(key)
          if (data) {
            const parsed = JSON.parse(data)
            // Remove expired sessions
            if (parsed.expires_at && new Date(parsed.expires_at) < new Date()) {
              localStorage.removeItem(key)
            }
          }
        } catch (e) {
          // Remove corrupted data
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.warn('Failed to optimize session storage:', error)
    }
  }

  // Network optimization
  async optimizeNetworkRequests(): Promise<void> {
    // Enable HTTP/2 server push hints
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready
        if (registration.active) {
          registration.active.postMessage({
            type: 'PRELOAD_AUTH_RESOURCES'
          })
        }
      } catch (error) {
        console.warn('Service worker optimization failed:', error)
      }
    }
  }

  // Memory optimization
  cleanupMemory(): void {
    // Clear expired cache entries
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }

    // Reset metrics if they're getting too large
    if (this.metrics.cacheHits + this.metrics.cacheMisses > 10000) {
      this.metrics.cacheHits = 0
      this.metrics.cacheMisses = 0
    }
  }

  // Performance monitoring
  logPerformanceMetrics(): void {
    if (process.env.NODE_ENV === 'development') {
      console.group('🚀 Auth Performance Metrics')
      console.log('Total Auth Time:', `${this.metrics.totalAuthTime.toFixed(2)}ms`)
      console.log('Profile Fetch Time:', `${this.metrics.profileFetchTime.toFixed(2)}ms`)
      console.log('Cache Hit Rate:', `${((this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses)) * 100).toFixed(1)}%`)
      console.log('Cache Size:', this.cache.size)
      console.groupEnd()
    }
  }

  // Critical rendering path optimization
  optimizeCriticalPath(): void {
    // Defer non-critical JavaScript
    const scripts = document.querySelectorAll('script[src]')
    scripts.forEach(script => {
      if (!script.hasAttribute('async') && !script.hasAttribute('defer')) {
        script.setAttribute('defer', '')
      }
    })

    // Optimize font loading
    const fontLink = document.createElement('link')
    fontLink.rel = 'preload'
    fontLink.as = 'font'
    fontLink.type = 'font/woff2'
    fontLink.crossOrigin = 'anonymous'
    fontLink.href = '/fonts/inter-var.woff2'
    document.head.appendChild(fontLink)
  }

  // Initialize all optimizations
  async initialize(): Promise<void> {
    try {
      await Promise.all([
        this.preloadAuthResources(),
        this.optimizeNetworkRequests()
      ])
      
      this.optimizeSessionStorage()
      this.optimizeCriticalPath()
      
      // Set up periodic cleanup
      setInterval(() => {
        this.cleanupMemory()
      }, 60000) // Clean up every minute

      console.log('🚀 Auth performance optimizations initialized')
    } catch (error) {
      console.warn('Some auth optimizations failed to initialize:', error)
    }
  }
}

// Export singleton instance
export const authPerformance = AuthPerformanceOptimizer.getInstance()

// Utility functions for common auth performance patterns
export const withAuthPerformance = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  operation: string
) => {
  return async (...args: T): Promise<R> => {
    const startTime = performance.now()
    
    try {
      const result = await fn(...args)
      const endTime = performance.now()
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`⚡ ${operation} completed in ${(endTime - startTime).toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      const endTime = performance.now()
      console.error(`❌ ${operation} failed after ${(endTime - startTime).toFixed(2)}ms:`, error)
      throw error
    }
  }
}

// Debounced auth state change handler
export const createDebouncedAuthHandler = (
  handler: (event: string, session: Session | null) => void,
  delay: number = 100
) => {
  let timeoutId: NodeJS.Timeout | null = null
  
  return (event: string, session: Session | null) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      handler(event, session)
    }, delay)
  }
}

// Optimized user data fetcher with retry logic
export const createOptimizedUserFetcher = (
  maxRetries: number = 3,
  retryDelay: number = 1000
) => {
  return async (
    fetchFn: () => Promise<any>,
    cacheKey?: string
  ): Promise<any> => {
    if (cacheKey) {
      const cached = authPerformance.getCache(cacheKey)
      if (cached) return cached
    }

    let lastError: Error | null = null
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const result = await fetchFn()
        
        if (cacheKey && result) {
          authPerformance.setCache(cacheKey, result)
        }
        
        return result
      } catch (error) {
        lastError = error as Error
        
        if (attempt < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)))
        }
      }
    }
    
    throw lastError
  }
} 