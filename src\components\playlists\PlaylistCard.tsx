'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { 
  Music, 
  Play, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Share2, 
  <PERSON>py,
  <PERSON>,
  <PERSON>,
  Clock,
  Calendar
} from 'lucide-react'
import { Playlist } from '@/types/playlist'
import { formatDuration, formatDate } from '@/utils/format'

interface PlaylistCardProps {
  playlist: Playlist
  onEdit?: (playlist: Playlist) => void
  onDelete?: (playlist: Playlist) => void
  onPlay?: (playlist: Playlist) => void
  showOwner?: boolean
  className?: string
}

export default function PlaylistCard({ 
  playlist, 
  onEdit, 
  onDelete, 
  onPlay,
  showOwner = false,
  className = '' 
}: PlaylistCardProps) {
  const [showMenu, setShowMenu] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleMenuClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setShowMenu(!showMenu)
  }

  const handleMenuAction = (e: React.MouseEvent, action: () => void) => {
    e.preventDefault()
    e.stopPropagation()
    setShowMenu(false)
    action()
  }

  const handlePlayClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onPlay?.(playlist)
  }

  const handleShare = async () => {
    if (navigator.share && playlist.is_public) {
      try {
        await navigator.share({
          title: playlist.name,
          text: playlist.description || `Check out this playlist: ${playlist.name}`,
          url: `${window.location.origin}/playlist/${playlist.id}`
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback to clipboard
      const url = `${window.location.origin}/playlist/${playlist.id}`
      await navigator.clipboard.writeText(url)
      // You could show a toast notification here
    }
  }

  const trackCountText = playlist.track_count === 1 ? '1 track' : `${playlist.track_count || 0} tracks`
  const durationText = playlist.total_duration ? formatDuration(playlist.total_duration) : null

  return (
    <div className={`group relative bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden ${className}`}>
      <Link href={`/playlist/${playlist.id}`} className="block">
        {/* Cover Image */}
        <div className="relative aspect-square bg-gradient-to-br from-blue-400 to-purple-500 overflow-hidden">
          {playlist.cover_image_url && !imageError ? (
            <Image
              src={playlist.cover_image_url}
              alt={playlist.name}
              fill
              className="object-cover"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Music className="w-12 h-12 text-white opacity-80" />
            </div>
          )}
          
          {/* Play Button Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
            <button
              onClick={handlePlayClick}
              className="opacity-0 group-hover:opacity-100 transform scale-75 group-hover:scale-100 transition-all duration-200 bg-white text-gray-900 rounded-full p-3 shadow-lg hover:scale-110"
            >
              <Play className="w-6 h-6 ml-1" />
            </button>
          </div>

          {/* Privacy Indicator */}
          <div className="absolute top-2 left-2">
            {playlist.is_public ? (
              <div className="bg-green-500 text-white p-1 rounded-full">
                <Globe className="w-3 h-3" />
              </div>
            ) : (
              <div className="bg-gray-500 text-white p-1 rounded-full">
                <Lock className="w-3 h-3" />
              </div>
            )}
          </div>

          {/* Menu Button */}
          <div className="absolute top-2 right-2">
            <button
              onClick={handleMenuClick}
              className="opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-50 text-white p-1 rounded-full hover:bg-opacity-70"
            >
              <MoreVertical className="w-4 h-4" />
            </button>

            {/* Dropdown Menu */}
            {showMenu && (
              <div className="absolute top-8 right-0 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 py-1 z-10 min-w-[160px]">
                {onEdit && (
                  <button
                    onClick={(e) => handleMenuAction(e, () => onEdit(playlist))}
                    className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-2"
                  >
                    <Edit className="w-4 h-4" />
                    <span>Edit</span>
                  </button>
                )}
                
                {playlist.is_public && (
                  <button
                    onClick={(e) => handleMenuAction(e, handleShare)}
                    className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-2"
                  >
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                )}
                
                <button
                  onClick={(e) => handleMenuAction(e, () => navigator.clipboard.writeText(`${window.location.origin}/playlist/${playlist.id}`))}
                  className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-2"
                >
                  <Copy className="w-4 h-4" />
                  <span>Copy Link</span>
                </button>
                
                {onDelete && (
                  <>
                    <hr className="my-1 border-gray-200 dark:border-gray-600" />
                    <button
                      onClick={(e) => handleMenuAction(e, () => onDelete(playlist))}
                      className="w-full px-3 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>Delete</span>
                    </button>
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Title */}
          <h3 className="font-semibold text-gray-900 dark:text-white text-lg mb-1 line-clamp-1">
            {playlist.name}
          </h3>

          {/* Description */}
          {playlist.description && (
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-2 line-clamp-2">
              {playlist.description}
            </p>
          )}

          {/* Stats */}
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-3">
              <span className="flex items-center space-x-1">
                <Music className="w-3 h-3" />
                <span>{trackCountText}</span>
              </span>
              
              {durationText && (
                <span className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{durationText}</span>
                </span>
              )}
            </div>

            <span className="flex items-center space-x-1">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(playlist.updated_at)}</span>
            </span>
          </div>

          {/* Owner (if showing) */}
          {showOwner && (
            <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Created by User
              </p>
            </div>
          )}
        </div>
      </Link>

      {/* Click outside to close menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  )
} 