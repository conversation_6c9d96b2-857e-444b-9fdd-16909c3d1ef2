import React from 'react'
import { Music } from 'lucide-react'

interface SmoothLoaderProps {
  message?: string
  size?: 'sm' | 'md' | 'lg'
  showIcon?: boolean
  className?: string
}

export const SmoothLoader: React.FC<SmoothLoaderProps> = ({
  message = 'Loading...',
  size = 'md',
  showIcon = true,
  className = ''
}) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  }

  const iconSizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <div className={`flex flex-col items-center justify-center space-y-4 ${className}`}>
      {showIcon && (
        <div className="relative">
          <Music className={`${iconSizeClasses[size]} text-primary-400 animate-pulse`} />
          <div className="absolute inset-0 animate-ping">
            <Music className={`${iconSizeClasses[size]} text-primary-400 opacity-75`} />
          </div>
        </div>
      )}
      
      <div className="flex items-center space-x-2">
        <div className={`${sizeClasses[size]} border-2 border-primary-400 border-t-transparent rounded-full animate-spin`}></div>
        {message && (
          <p className="text-gray-300 animate-pulse">{message}</p>
        )}
      </div>
    </div>
  )
}

export const PageLoader: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => (
  <div className="min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center">
    <SmoothLoader message={message} size="lg" />
  </div>
)

export const InlineLoader: React.FC<{ message?: string }> = ({ message }) => (
  <SmoothLoader message={message} size="sm" showIcon={false} className="py-4" />
) 