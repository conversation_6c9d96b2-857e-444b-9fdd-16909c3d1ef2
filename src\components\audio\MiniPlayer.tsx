'use client'

import { useState, useRef } from 'react'
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward, 
  Volume2, 
  VolumeX, 
  Repeat, 
  Repeat1, 
  Shuffle, 
  Music,
  Loader2
} from 'lucide-react'
import { useAudio } from '@/contexts/AudioContext'

export default function MiniPlayer() {
  const progressRef = useRef<HTMLDivElement>(null)
  const volumeRef = useRef<HTMLDivElement>(null)

  const {
    currentTrack,
    isPlaying,
    isLoading,
    currentTime,
    duration,
    volume,
    isMuted,
    repeatMode,
    isShuffled,
    currentPlaylist,
    playlistMode,
    togglePlayPause,
    nextTrack,
    previousTrack,
    setVolume,
    toggleMute,
    shuffleQueue,
    setRepeatMode,
    seek
  } = useAudio()

  // Don't render if no current track
  if (!currentTrack) return null

  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!progressRef.current || !duration) return
    
    const rect = progressRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    const newTime = percentage * duration
    
    seek(newTime)
  }

  const handleVolumeClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!volumeRef.current) return
    
    const rect = volumeRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    
    setVolume(percentage)
  }



  const handleShuffle = () => {
    shuffleQueue()
  }

  const handleRepeat = () => {
    const modes: Array<'none' | 'one' | 'all'> = ['none', 'one', 'all']
    const currentIndex = modes.indexOf(repeatMode)
    const nextMode = modes[(currentIndex + 1) % modes.length]
    setRepeatMode(nextMode)
  }

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <>
      {/* Mini Player */}
      <div className="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-800 p-4 z-[60]">
        <div className="max-w-7xl mx-auto">
          {/* Progress Bar */}
          <div className="mb-4">
            <div 
              ref={progressRef}
              onClick={handleProgressClick}
              className="relative w-full h-2 bg-gray-700 rounded-full cursor-pointer group hover:h-3 transition-all duration-200"
            >
              <div 
                className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-200"
                style={{ width: `${progressPercentage}%` }}
              />
              <div 
                className="absolute w-3 h-3 bg-white rounded-full shadow-md transform -translate-y-1/2 top-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                style={{ left: `${progressPercentage}%`, marginLeft: '-6px' }}
              />
            </div>
            
            {/* Time Display Below Progress Bar */}
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            {/* Track Info */}
            <div className="flex items-center space-x-4 flex-1 min-w-0">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                <Music className="w-6 h-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="text-white font-medium truncate">{currentTrack.title}</h3>
                <div className="flex items-center space-x-2">
                  <p className="text-gray-400 text-sm truncate">{currentTrack.artist}</p>
                  {playlistMode && currentPlaylist && (
                    <>
                      <span className="text-gray-600">•</span>
                      <p className="text-purple-400 text-sm truncate">
                        from {currentPlaylist.name}
                      </p>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-4">
              {/* Shuffle */}
              <button
                onClick={handleShuffle}
                className={`p-2 rounded-full transition-colors ${
                  isShuffled ? 'text-purple-400' : 'text-gray-400 hover:text-white'
                }`}
                title="Shuffle"
              >
                <Shuffle className="w-4 h-4" />
              </button>

              {/* Previous */}
              <button
                onClick={previousTrack}
                className="p-2 rounded-full text-gray-400 hover:text-white transition-colors"
                title="Previous"
              >
                <SkipBack className="w-5 h-5" />
              </button>

              {/* Play/Pause */}
              <button
                onClick={togglePlayPause}
                disabled={isLoading}
                className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 rounded-full flex items-center justify-center transition-all duration-200"
                title={isPlaying ? 'Pause' : 'Play'}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 text-white animate-spin" />
                ) : isPlaying ? (
                  <Pause className="w-4 h-4 text-white" />
                ) : (
                  <Play className="w-4 h-4 text-white ml-0.5" />
                )}
              </button>

              {/* Next */}
              <button
                onClick={nextTrack}
                className="p-2 rounded-full text-gray-400 hover:text-white transition-colors"
                title="Next"
              >
                <SkipForward className="w-5 h-5" />
              </button>

              {/* Repeat */}
              <button
                onClick={handleRepeat}
                className={`p-2 rounded-full transition-colors ${
                  repeatMode !== 'none' ? 'text-purple-400' : 'text-gray-400 hover:text-white'
                }`}
                title={`Repeat: ${repeatMode}`}
              >
                {repeatMode === 'one' ? <Repeat1 className="w-4 h-4" /> : <Repeat className="w-4 h-4" />}
              </button>
            </div>

            {/* Right Side */}
            <div className="flex items-center space-x-4 flex-1 justify-end">
              {/* Volume */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleMute}
                  className="text-gray-400 hover:text-white transition-colors"
                  title={isMuted ? 'Unmute' : 'Mute'}
                >
                  {isMuted || volume === 0 ? (
                    <VolumeX className="w-4 h-4" />
                  ) : (
                    <Volume2 className="w-4 h-4" />
                  )}
                </button>
                <div 
                  ref={volumeRef}
                  onClick={handleVolumeClick}
                  className="w-20 h-2 bg-gray-700 rounded-full cursor-pointer group hover:h-3 transition-all duration-200"
                >
                  <div 
                    className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-200"
                    style={{ width: `${isMuted ? 0 : volume * 100}%` }}
                  />
                </div>
                <span className="text-xs text-gray-400 w-8 text-right">
                  {Math.round((isMuted ? 0 : volume) * 100)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>


    </>
  )
} 