// Dashboard Analytics Hook
'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import {
  getDashboardStats,
  getRecentlyPlayedTracks,
  getUploadHistory,
  getFavoriteTracks,
  getPersonalizedRecommendations,
  getRecentPlaylists,
  toggleTrackLike,
  recordListeningActivity,
  dashboardTimeFilters
} from '@/lib/dashboard-analytics'
import {
  DashboardStats,
  RecentlyPlayedTrack,
  UploadHistoryItem,
  FavoriteTrack,
  PersonalizedRecommendation,
  DashboardTimeFilter
} from '@/types/dashboard'
import { Playlist } from '@/types/database'

export const useDashboardAnalytics = () => {
  const { user } = useAuth()
  
  // State management
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentlyPlayed, setRecentlyPlayed] = useState<RecentlyPlayedTrack[]>([])
  const [uploadHistory, setUploadHistory] = useState<UploadHistoryItem[]>([])
  const [favorites, setFavorites] = useState<FavoriteTrack[]>([])
  const [recommendations, setRecommendations] = useState<PersonalizedRecommendation[]>([])
  const [recentPlaylists, setRecentPlaylists] = useState<Playlist[]>([])
  
  // Loading states
  const [loading, setLoading] = useState(true)
  const [statsLoading, setStatsLoading] = useState(false)
  const [recentlyPlayedLoading, setRecentlyPlayedLoading] = useState(false)
  const [uploadHistoryLoading, setUploadHistoryLoading] = useState(false)
  const [favoritesLoading, setFavoritesLoading] = useState(false)
  const [recommendationsLoading, setRecommendationsLoading] = useState(false)
  const [playlistsLoading, setPlaylistsLoading] = useState(false)
  
  // Error states
  const [error, setError] = useState<string | null>(null)
  const [statsError, setStatsError] = useState<string | null>(null)
  const [recentlyPlayedError, setRecentlyPlayedError] = useState<string | null>(null)
  const [uploadHistoryError, setUploadHistoryError] = useState<string | null>(null)
  const [favoritesError, setFavoritesError] = useState<string | null>(null)
  const [recommendationsError, setRecommendationsError] = useState<string | null>(null)
  const [playlistsError, setPlaylistsError] = useState<string | null>(null)
  
  // Pagination states
  const [recentlyPlayedOffset, setRecentlyPlayedOffset] = useState(0)
  const [uploadHistoryOffset, setUploadHistoryOffset] = useState(0)
  const [favoritesOffset, setFavoritesOffset] = useState(0)
  const [hasMoreRecentlyPlayed, setHasMoreRecentlyPlayed] = useState(true)
  const [hasMoreUploadHistory, setHasMoreUploadHistory] = useState(true)
  const [hasMoreFavorites, setHasMoreFavorites] = useState(true)
  
  // Time filter
  const [timeFilter, setTimeFilter] = useState<DashboardTimeFilter['value']>('week')

  // Load dashboard stats
  const loadStats = useCallback(async (filter: DashboardTimeFilter['value'] = timeFilter) => {
    if (!user?.id) return

    try {
      setStatsLoading(true)
      setStatsError(null)

      const result = await getDashboardStats(user.id, filter)
      
      if (result.error) throw new Error(result.error)
      
      setStats(result.stats)
    } catch (err: any) {
      setStatsError(err.message || 'Failed to load dashboard stats')
    } finally {
      setStatsLoading(false)
    }
  }, [user?.id, timeFilter])

  // Load recently played tracks
  const loadRecentlyPlayed = useCallback(async (reset = false) => {
    if (!user?.id) return

    try {
      setRecentlyPlayedLoading(true)
      setRecentlyPlayedError(null)

      const offset = reset ? 0 : recentlyPlayedOffset
      const result = await getRecentlyPlayedTracks(user.id, 10, offset)
      
      if (result.error) throw new Error(result.error)
      
      const newTracks = result.tracks || []
      
      if (reset) {
        setRecentlyPlayed(newTracks)
        setRecentlyPlayedOffset(10)
      } else {
        setRecentlyPlayed(prev => [...prev, ...newTracks])
        setRecentlyPlayedOffset(prev => prev + 10)
      }
      
      setHasMoreRecentlyPlayed(newTracks.length === 10)
    } catch (err: any) {
      setRecentlyPlayedError(err.message || 'Failed to load recently played tracks')
    } finally {
      setRecentlyPlayedLoading(false)
    }
  }, [user?.id, recentlyPlayedOffset])

  // Load upload history
  const loadUploadHistory = useCallback(async (reset = false) => {
    if (!user?.id) return

    try {
      setUploadHistoryLoading(true)
      setUploadHistoryError(null)

      const offset = reset ? 0 : uploadHistoryOffset
      const result = await getUploadHistory(user.id, 10, offset)
      
      if (result.error) throw new Error(result.error)
      
      const newUploads = result.uploads || []
      
      if (reset) {
        setUploadHistory(newUploads)
        setUploadHistoryOffset(10)
      } else {
        setUploadHistory(prev => [...prev, ...newUploads])
        setUploadHistoryOffset(prev => prev + 10)
      }
      
      setHasMoreUploadHistory(newUploads.length === 10)
    } catch (err: any) {
      setUploadHistoryError(err.message || 'Failed to load upload history')
    } finally {
      setUploadHistoryLoading(false)
    }
  }, [user?.id, uploadHistoryOffset])

  // Load favorite tracks
  const loadFavorites = useCallback(async (reset = false) => {
    if (!user?.id) return

    try {
      setFavoritesLoading(true)
      setFavoritesError(null)

      const offset = reset ? 0 : favoritesOffset
      const result = await getFavoriteTracks(user.id, 10, offset)
      
      if (result.error) throw new Error(result.error)
      
      const newFavorites = result.favorites || []
      
      if (reset) {
        setFavorites(newFavorites)
        setFavoritesOffset(10)
      } else {
        setFavorites(prev => [...prev, ...newFavorites])
        setFavoritesOffset(prev => prev + 10)
      }
      
      setHasMoreFavorites(newFavorites.length === 10)
    } catch (err: any) {
      setFavoritesError(err.message || 'Failed to load favorite tracks')
    } finally {
      setFavoritesLoading(false)
    }
  }, [user?.id, favoritesOffset])

  // Load recommendations
  const loadRecommendations = useCallback(async () => {
    if (!user?.id) return

    try {
      setRecommendationsLoading(true)
      setRecommendationsError(null)

      const result = await getPersonalizedRecommendations(user.id, 8)
      
      if (result.error) throw new Error(result.error)
      
      setRecommendations(result.recommendations || [])
    } catch (err: any) {
      setRecommendationsError(err.message || 'Failed to load recommendations')
    } finally {
      setRecommendationsLoading(false)
    }
  }, [user?.id])

  // Load recent playlists
  const loadRecentPlaylists = useCallback(async () => {
    if (!user?.id) return

    try {
      setPlaylistsLoading(true)
      setPlaylistsError(null)

      const result = await getRecentPlaylists(user.id, 5)
      
      if (result.error) throw new Error(result.error)
      
      setRecentPlaylists(result.playlists || [])
    } catch (err: any) {
      setPlaylistsError(err.message || 'Failed to load recent playlists')
    } finally {
      setPlaylistsLoading(false)
    }
  }, [user?.id])

  // Toggle track like
  const handleToggleTrackLike = useCallback(async (trackId: string) => {
    if (!user?.id) return { success: false, liked: false, error: 'Not authenticated' }

    try {
      const result = await toggleTrackLike(user.id, trackId)
      
      if (result.error) throw new Error(result.error)
      
      // Update favorites list if track was unliked
      if (!result.liked) {
        setFavorites(prev => prev.filter(fav => fav.id !== trackId))
      }
      
      return { success: true, liked: result.liked, error: null }
    } catch (err: any) {
      return { success: false, liked: false, error: err.message || 'Failed to toggle like' }
    }
  }, [user?.id])

  // Record listening activity
  const recordListening = useCallback(async (
    trackId: string,
    durationListened: number,
    completed: boolean,
    source: string = 'dashboard',
    playlistId?: string
  ) => {
    if (!user?.id) return

    try {
      await recordListeningActivity(user.id, trackId, durationListened, completed, source, playlistId)
      
      // Refresh recently played if this was a significant listen
      if (durationListened > 30) {
        loadRecentlyPlayed(true)
      }
    } catch (err: any) {
      console.error('Failed to record listening activity:', err)
    }
  }, [user?.id, loadRecentlyPlayed])

  // Change time filter
  const changeTimeFilter = useCallback((newFilter: DashboardTimeFilter['value']) => {
    setTimeFilter(newFilter)
    loadStats(newFilter)
  }, [loadStats])

  // Refresh all data
  const refreshAll = useCallback(async () => {
    if (!user?.id) return

    setLoading(true)
    setError(null)

    try {
      await Promise.all([
        loadStats(),
        loadRecentlyPlayed(true),
        loadUploadHistory(true),
        loadFavorites(true),
        loadRecommendations(),
        loadRecentPlaylists()
      ])
    } catch (err: any) {
      setError(err.message || 'Failed to refresh dashboard data')
    } finally {
      setLoading(false)
    }
  }, [user?.id, loadStats, loadRecentlyPlayed, loadUploadHistory, loadFavorites, loadRecommendations, loadRecentPlaylists])

  // Load more functions
  const loadMoreRecentlyPlayed = () => loadRecentlyPlayed(false)
  const loadMoreUploadHistory = () => loadUploadHistory(false)
  const loadMoreFavorites = () => loadFavorites(false)

  // Initial load
  useEffect(() => {
    if (user?.id) {
      refreshAll()
    }
  }, [user?.id])

  return {
    // Data
    stats,
    recentlyPlayed,
    uploadHistory,
    favorites,
    recommendations,
    recentPlaylists,
    
    // Loading states
    loading,
    statsLoading,
    recentlyPlayedLoading,
    uploadHistoryLoading,
    favoritesLoading,
    recommendationsLoading,
    playlistsLoading,
    
    // Error states
    error,
    statsError,
    recentlyPlayedError,
    uploadHistoryError,
    favoritesError,
    recommendationsError,
    playlistsError,
    
    // Pagination
    hasMoreRecentlyPlayed,
    hasMoreUploadHistory,
    hasMoreFavorites,
    
    // Time filter
    timeFilter,
    timeFilters: dashboardTimeFilters,
    
    // Actions
    changeTimeFilter,
    refreshAll,
    loadMoreRecentlyPlayed,
    loadMoreUploadHistory,
    loadMoreFavorites,
    handleToggleTrackLike,
    recordListening,
    
    // Individual refresh functions
    refreshStats: () => loadStats(),
    refreshRecentlyPlayed: () => loadRecentlyPlayed(true),
    refreshUploadHistory: () => loadUploadHistory(true),
    refreshFavorites: () => loadFavorites(true),
    refreshRecommendations: loadRecommendations,
    refreshRecentPlaylists: loadRecentPlaylists
  }
} 