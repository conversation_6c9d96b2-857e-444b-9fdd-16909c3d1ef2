'use client'

import React, { createContext, useContext, useReducer, useEffect, useCallback, useState } from 'react'
import { AudioTrack, mapTrackToAudioTrack } from '@/types/track'
import { PlaylistWithTracks } from '@/types/playlist'

// Queue Management Types
export type RepeatMode = 'none' | 'one' | 'all'
export type QueueItem = {
  track: AudioTrack
  id: string
  addedAt: Date
}

// Global Audio State
export interface GlobalAudioState {
  // Current playback
  currentTrack: AudioTrack | null
  currentQueueIndex: number
  isPlaying: boolean
  isLoading: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean
  
  // Queue management
  queue: QueueItem[]
  originalQueue: QueueItem[] // For shuffle restoration
  isShuffled: boolean
  repeatMode: RepeatMode
  
  // Playlist context
  currentPlaylist: PlaylistWithTracks | null
  playlistMode: boolean // Whether we're playing from a playlist
  
  // Player state
  hasNext: boolean
  hasPrevious: boolean
  isQueueVisible: boolean
  
  // Client state
  isClient: boolean
}

// Action Types
export type AudioAction =
  | { type: 'PLAY' }
  | { type: 'PAUSE' }
  | { type: 'TOGGLE_PLAY_PAUSE' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_CURRENT_TIME'; payload: number }
  | { type: 'SET_DURATION'; payload: number }
  | { type: 'SET_VOLUME'; payload: number }
  | { type: 'TOGGLE_MUTE' }
  | { type: 'LOAD_TRACK'; payload: AudioTrack }
  | { type: 'NEXT_TRACK' }
  | { type: 'PREVIOUS_TRACK' }
  | { type: 'SEEK_TO'; payload: number }
  | { type: 'SET_QUEUE'; payload: AudioTrack[] }
  | { type: 'ADD_TO_QUEUE'; payload: AudioTrack }
  | { type: 'REMOVE_FROM_QUEUE'; payload: string }
  | { type: 'CLEAR_QUEUE' }
  | { type: 'SHUFFLE_QUEUE' }
  | { type: 'UNSHUFFLE_QUEUE' }
  | { type: 'SET_REPEAT_MODE'; payload: RepeatMode }
  | { type: 'TOGGLE_QUEUE_VISIBILITY' }
  | { type: 'PLAY_TRACK_AT_INDEX'; payload: number }
  | { type: 'JUMP_TO_TRACK'; payload: number }
  | { type: 'SET_CLIENT'; payload: boolean }
  | { type: 'PLAY_PLAYLIST'; payload: { playlist: PlaylistWithTracks; startIndex?: number } }
  | { type: 'SET_CURRENT_PLAYLIST'; payload: PlaylistWithTracks | null }
  | { type: 'SET_PLAYLIST_MODE'; payload: boolean }

// Context Interface
export interface AudioContextValue extends GlobalAudioState {
  // Current playback state
  currentTrack: AudioTrack | null
  isPlaying: boolean
  isLoading: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean

  // Queue management
  queue: QueueItem[]
  queuePosition: number
  repeatMode: RepeatMode
  isShuffled: boolean

  // Player controls
  togglePlayPause: () => void
  nextTrack: () => void
  previousTrack: () => void
  seek: (time: number) => void
  setVolume: (volume: number) => void
  toggleMute: () => void

  // Internal audio controls (for GlobalAudioPlayer)
  setCurrentTime: (time: number) => void
  setDuration: (duration: number) => void
  setLoading: (loading: boolean) => void

  // Queue controls
  setQueue: (tracks: AudioTrack[]) => void
  addToQueue: (track: AudioTrack) => void
  removeFromQueue: (id: string) => void
  clearQueue: () => void
  shuffleQueue: () => void
  setRepeatMode: (mode: RepeatMode) => void
  jumpToTrack: (index: number) => void

  // Playlist controls
  playPlaylist: (playlist: PlaylistWithTracks, startIndex?: number) => void
  currentPlaylist: PlaylistWithTracks | null
  playlistMode: boolean

  // State
  isClient: boolean
}

// Initial State
const initialState: GlobalAudioState = {
  currentTrack: null,
  currentQueueIndex: -1,
  isPlaying: false,
  isLoading: false,
  currentTime: 0,
  duration: 0,
  volume: 1,
  isMuted: false,
  queue: [],
  originalQueue: [],
  isShuffled: false,
  repeatMode: 'none',
  currentPlaylist: null,
  playlistMode: false,
  hasNext: false,
  hasPrevious: false,
  isQueueVisible: false,
  isClient: false
}

// Utility functions
const generateQueueId = () => Math.random().toString(36).substr(2, 9)

const createQueueItem = (track: AudioTrack): QueueItem => ({
  track,
  id: generateQueueId(),
  addedAt: new Date()
})

const shuffleArray = <T,>(array: T[]): T[] => {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

const updateNavigationState = (state: GlobalAudioState): Partial<GlobalAudioState> => {
  const { queue, currentQueueIndex, repeatMode } = state
  
  const hasNext = repeatMode === 'all' || currentQueueIndex < queue.length - 1
  const hasPrevious = repeatMode === 'all' || currentQueueIndex > 0
  
  return { hasNext, hasPrevious }
}

// Check if we're on the client side
const isClientSide = () => typeof window !== 'undefined'

// Reducer
const audioReducer = (state: GlobalAudioState, action: AudioAction): GlobalAudioState => {
  switch (action.type) {
    case 'SET_CLIENT':
      return { ...state, isClient: action.payload }
      
    case 'PLAY':
      return { ...state, isPlaying: true }
      
    case 'PAUSE':
      return { ...state, isPlaying: false }
      
    case 'TOGGLE_PLAY_PAUSE':
      return { ...state, isPlaying: !state.isPlaying }
      
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
      
    case 'SET_CURRENT_TIME':
      return { ...state, currentTime: action.payload }
      
    case 'SET_DURATION':
      return { ...state, duration: action.payload }
      
    case 'SET_VOLUME':
      return { ...state, volume: action.payload, isMuted: false }
      
    case 'TOGGLE_MUTE':
      return { ...state, isMuted: !state.isMuted }
      
    case 'SEEK_TO':
      return { ...state, currentTime: action.payload }
      
    case 'LOAD_TRACK': {
      const track = action.payload
      const trackIndex = state.queue.findIndex(item => item.track.id === track.id)
      const newIndex = trackIndex >= 0 ? trackIndex : state.currentQueueIndex
      
      const newState = {
        ...state,
        currentTrack: track,
        currentQueueIndex: newIndex,
        currentTime: 0,
        duration: 0
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'SET_QUEUE': {
      const tracks = action.payload
      const queueItems = tracks.map(createQueueItem)
      
      const newState = {
        ...state,
        queue: queueItems,
        originalQueue: queueItems,
        currentQueueIndex: tracks.length > 0 ? 0 : -1,
        currentTrack: tracks.length > 0 ? tracks[0] : null,
        isShuffled: false
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'ADD_TO_QUEUE': {
      const newItem = createQueueItem(action.payload)
      const newQueue = [...state.queue, newItem]
      
      const newState = {
        ...state,
        queue: newQueue,
        originalQueue: state.isShuffled ? state.originalQueue : newQueue,
        currentQueueIndex: state.currentQueueIndex >= 0 ? state.currentQueueIndex : 0,
        currentTrack: state.currentTrack || action.payload
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'REMOVE_FROM_QUEUE': {
      const trackId = action.payload
      const newQueue = state.queue.filter(item => item.track.id !== trackId)
      const newOriginalQueue = state.originalQueue.filter(item => item.track.id !== trackId)
      
      let newIndex = state.currentQueueIndex
      const removedIndex = state.queue.findIndex(item => item.track.id === trackId)
      
      if (removedIndex >= 0 && removedIndex <= state.currentQueueIndex) {
        newIndex = Math.max(0, state.currentQueueIndex - 1)
      }
      
      if (newQueue.length === 0) {
        return {
          ...state,
          queue: [],
          originalQueue: [],
          currentTrack: null,
          currentQueueIndex: -1,
          hasNext: false,
          hasPrevious: false
        }
      }
      
      const newState = {
        ...state,
        queue: newQueue,
        originalQueue: newOriginalQueue,
        currentQueueIndex: newIndex,
        currentTrack: newQueue[newIndex]?.track || null
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'CLEAR_QUEUE':
      return {
        ...state,
        queue: [],
        originalQueue: [],
        currentTrack: null,
        currentQueueIndex: -1,
        hasNext: false,
        hasPrevious: false,
        isShuffled: false
      }
    
    case 'SHUFFLE_QUEUE': {
      if (state.queue.length <= 1) return state
      
      const currentItem = state.queue[state.currentQueueIndex]
      const otherItems = state.queue.filter((_, index) => index !== state.currentQueueIndex)
      const shuffledOthers = shuffleArray(otherItems)
      const newQueue = currentItem ? [currentItem, ...shuffledOthers] : shuffledOthers
      
      const newState = {
        ...state,
        queue: newQueue,
        originalQueue: state.isShuffled ? state.originalQueue : state.queue,
        currentQueueIndex: 0,
        isShuffled: true
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'UNSHUFFLE_QUEUE': {
      if (!state.isShuffled) return state
      
      const currentTrack = state.currentTrack
      const restoredIndex = currentTrack 
        ? state.originalQueue.findIndex(item => item.track.id === currentTrack.id)
        : 0
      
      const newState = {
        ...state,
        queue: state.originalQueue,
        currentQueueIndex: Math.max(0, restoredIndex),
        isShuffled: false
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'SET_REPEAT_MODE': {
      const newState = { ...state, repeatMode: action.payload }
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'TOGGLE_QUEUE_VISIBILITY':
      return { ...state, isQueueVisible: !state.isQueueVisible }
    
    case 'PLAY_TRACK_AT_INDEX': {
      const index = action.payload
      if (index < 0 || index >= state.queue.length) return state
      
      const newState = {
        ...state,
        currentQueueIndex: index,
        currentTrack: state.queue[index].track,
        currentTime: 0,
        duration: 0
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'NEXT_TRACK': {
      const { currentQueueIndex, queue, repeatMode } = state
      
      if (queue.length === 0) return state
      
      let nextIndex: number
      
      if (repeatMode === 'one') {
        nextIndex = currentQueueIndex // Stay on same track
      } else if (repeatMode === 'all' && currentQueueIndex >= queue.length - 1) {
        nextIndex = 0 // Loop to beginning
      } else if (currentQueueIndex < queue.length - 1) {
        nextIndex = currentQueueIndex + 1
      } else {
        return state // No next track available
      }
      
      const newState = {
        ...state,
        currentQueueIndex: nextIndex,
        currentTrack: queue[nextIndex].track,
        currentTime: 0,
        duration: 0
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'PREVIOUS_TRACK': {
      const { currentQueueIndex, queue, repeatMode } = state
      
      if (queue.length === 0) return state
      
      let prevIndex: number
      
      if (repeatMode === 'all' && currentQueueIndex <= 0) {
        prevIndex = queue.length - 1 // Loop to end
      } else if (currentQueueIndex > 0) {
        prevIndex = currentQueueIndex - 1
      } else {
        return state // No previous track available
      }
      
      const newState = {
        ...state,
        currentQueueIndex: prevIndex,
        currentTrack: queue[prevIndex].track,
        currentTime: 0,
        duration: 0
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }
    
    case 'JUMP_TO_TRACK': {
      const index = action.payload
      if (index < 0 || index >= state.queue.length) return state
      
      const newState = {
        ...state,
        currentQueueIndex: index,
        currentTrack: state.queue[index].track,
        currentTime: 0,
        duration: 0
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }

    case 'PLAY_PLAYLIST': {
      const { playlist, startIndex = 0 } = action.payload
      
      // Convert playlist tracks to queue items
      const playlistTracks = playlist.tracks
        .sort((a, b) => a.position - b.position)
        .map(playlistTrack => playlistTrack.track)
        .filter(track => track) // Filter out any null tracks
      
      if (playlistTracks.length === 0) return state
      
      const queueItems = playlistTracks.map(track => createQueueItem(mapTrackToAudioTrack(track!)))
      const validStartIndex = Math.max(0, Math.min(startIndex, queueItems.length - 1))
      
      const newState = {
        ...state,
        queue: queueItems,
        originalQueue: queueItems,
        currentQueueIndex: validStartIndex,
        currentTrack: queueItems[validStartIndex].track,
        currentPlaylist: playlist,
        playlistMode: true,
        isShuffled: false,
        currentTime: 0,
        duration: 0
      }
      
      return { ...newState, ...updateNavigationState(newState) }
    }

    case 'SET_CURRENT_PLAYLIST':
      return { ...state, currentPlaylist: action.payload }

    case 'SET_PLAYLIST_MODE':
      return { ...state, playlistMode: action.payload }
    
    default:
      return state
  }
}

// Create Context
const AudioContext = createContext<AudioContextValue | null>(null)

// Provider Component
export const AudioProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(audioReducer, initialState)
  const [isClient, setIsClient] = useState(false)

  // Set client-side flag
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Persist state to localStorage
  useEffect(() => {
    if (isClient) {
      localStorage.setItem('tunami-audio-state', JSON.stringify({
        queue: state.queue,
        currentQueueIndex: state.currentQueueIndex,
        volume: state.volume,
        isMuted: state.isMuted,
        repeatMode: state.repeatMode,
        isShuffled: state.isShuffled
      }))
    }
  }, [state, isClient])

  const contextValue: AudioContextValue = {
    // Current playback state
    currentTrack: state.currentTrack,
    currentQueueIndex: state.currentQueueIndex,
    isPlaying: state.isPlaying,
    isLoading: state.isLoading,
    currentTime: state.currentTime,
    duration: state.duration,
    volume: state.volume,
    isMuted: state.isMuted,

    // Queue management
    queue: state.queue,
    originalQueue: state.originalQueue,
    queuePosition: state.currentQueueIndex,
    repeatMode: state.repeatMode,
    isShuffled: state.isShuffled,
    
    // Player state
    hasNext: state.hasNext,
    hasPrevious: state.hasPrevious,
    isQueueVisible: state.isQueueVisible,

    // Player controls
    togglePlayPause: useCallback(() => {
      dispatch({ type: 'TOGGLE_PLAY_PAUSE' })
    }, []),
    
    nextTrack: useCallback(() => {
      dispatch({ type: 'NEXT_TRACK' })
    }, []),
    
    previousTrack: useCallback(() => {
      dispatch({ type: 'PREVIOUS_TRACK' })
    }, []),
    
    seek: useCallback((time: number) => {
      dispatch({ type: 'SEEK_TO', payload: time })
    }, []),
    
    setVolume: useCallback((volume: number) => {
      dispatch({ type: 'SET_VOLUME', payload: volume })
    }, []),
    
    toggleMute: useCallback(() => {
      dispatch({ type: 'TOGGLE_MUTE' })
    }, []),

    // Internal functions for GlobalAudioPlayer
    setCurrentTime: useCallback((time: number) => {
      dispatch({ type: 'SET_CURRENT_TIME', payload: time })
    }, []),
    
    setDuration: useCallback((duration: number) => {
      dispatch({ type: 'SET_DURATION', payload: duration })
    }, []),
    
    setLoading: useCallback((loading: boolean) => {
      dispatch({ type: 'SET_LOADING', payload: loading })
    }, []),

    // Queue controls
    setQueue: useCallback((tracks: AudioTrack[]) => {
      dispatch({ type: 'SET_QUEUE', payload: tracks })
    }, []),
    
    addToQueue: useCallback((track: AudioTrack) => {
      dispatch({ type: 'ADD_TO_QUEUE', payload: track })
    }, []),
    
    removeFromQueue: useCallback((id: string) => {
      dispatch({ type: 'REMOVE_FROM_QUEUE', payload: id })
    }, []),
    
    clearQueue: useCallback(() => {
      dispatch({ type: 'CLEAR_QUEUE' })
    }, []),
    
    shuffleQueue: useCallback(() => {
      dispatch({ type: 'SHUFFLE_QUEUE' })
    }, []),
    
    setRepeatMode: useCallback((mode: RepeatMode) => {
      dispatch({ type: 'SET_REPEAT_MODE', payload: mode })
    }, []),
    
    jumpToTrack: useCallback((index: number) => {
      dispatch({ type: 'JUMP_TO_TRACK', payload: index })
    }, []),

    // Playlist controls
    playPlaylist: useCallback((playlist: PlaylistWithTracks, startIndex?: number) => {
      dispatch({ type: 'PLAY_PLAYLIST', payload: { playlist, startIndex } })
    }, []),
    
    currentPlaylist: state.currentPlaylist,
    playlistMode: state.playlistMode,

    // State
    isClient
  }

  return (
    <AudioContext.Provider value={contextValue}>
      {children}
    </AudioContext.Provider>
  )
}

// Custom hook with better error handling
export const useAudio = (): AudioContextValue => {
  const context = useContext(AudioContext)
  if (!context) {
    throw new Error('useAudio must be used within an AudioProvider')
  }
  return context
} 