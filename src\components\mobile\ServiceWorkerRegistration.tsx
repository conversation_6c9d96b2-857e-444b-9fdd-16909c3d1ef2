'use client'

import { useEffect, useState } from 'react'
import { toast } from 'react-hot-toast'

interface PWAInstallPrompt {
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
  prompt: () => Promise<void>
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: Event & {
      preventDefault: () => void
      prompt: () => Promise<void>
      userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
    }
  }
}

export function ServiceWorkerRegistration() {
  const [isLoading, setIsLoading] = useState(true)
  const [updateAvailable, setUpdateAvailable] = useState(false)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<PWAInstallPrompt | null>(null)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)

  useEffect(() => {
    // Check if app is already installed
    const checkInstallStatus = () => {
      // Check if running in standalone mode (PWA)
      const standalone = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone ||
                        document.referrer.includes('android-app://')
      
      setIsStandalone(standalone)
      setIsInstalled(standalone)
    }

    checkInstallStatus()
    registerServiceWorker()
    setupInstallPrompt()
    setupUpdateListener()

    return () => {
      // Cleanup listeners
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const registerServiceWorker = async () => {
    if (!('serviceWorker' in navigator)) {
      console.log('Service Workers not supported')
      setIsLoading(false)
      return
    }

    try {
      console.log('🎵 Registering service worker...')
      
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      })

      console.log('🎵 Service Worker registered:', registration.scope)

      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('🎵 New service worker available')
              setUpdateAvailable(true)
              toast.success('A new version of Tunami is ready to install', {
                action: {
                  label: 'Update',
                  onClick: handleUpdate
                }
              })
            }
          })
        }
      })

      // Handle controller changes
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('🎵 Service worker controller changed')
        window.location.reload()
      })

      // Check for waiting service worker
      if (registration.waiting) {
        console.log('🎵 Service worker waiting')
        setUpdateAvailable(true)
      }

      // Check for active service worker
      if (registration.active) {
        console.log('🎵 Service worker active')
        
        // Send message to service worker
        registration.active.postMessage({
          type: 'GET_VERSION'
        })
      }

      setIsLoading(false)

    } catch (error) {
      console.error('🎵 Service Worker registration failed:', error)
      setIsLoading(false)
    }
  }

  const setupInstallPrompt = () => {
    const handleBeforeInstallPrompt = (e: WindowEventMap['beforeinstallprompt']) => {
      console.log('🎵 Before install prompt fired')
      
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault()
      
      // Stash the event so it can be triggered later
      setDeferredPrompt(e as any)
      
      // Show install prompt after a delay (don't be too aggressive)
      setTimeout(() => {
        if (!isInstalled && !isStandalone) {
          setShowInstallPrompt(true)
        }
      }, 30000) // Show after 30 seconds
    }

    const handleAppInstalled = () => {
      console.log('🎵 PWA was installed')
      setIsInstalled(true)
      setShowInstallPrompt(false)
      setDeferredPrompt(null)
      
      toast.success('Tunami has been added to your home screen')
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)
  }

  const setupUpdateListener = () => {
    // Listen for messages from service worker
    navigator.serviceWorker?.addEventListener('message', (event) => {
      if (event.data?.type === 'VERSION_INFO') {
        console.log('🎵 Service worker version:', event.data.version)
      }
    })

    // Handle page visibility changes to check for updates
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        checkForUpdates()
      }
    })
  }

  const checkForUpdates = async () => {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        await registration.update()
      }
    }
  }

  const handleUpdate = async () => {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.getRegistration()
      
      if (registration?.waiting) {
        // Tell the waiting service worker to skip waiting
        registration.waiting.postMessage({ type: 'SKIP_WAITING' })
        setUpdateAvailable(false)
      }
    }
  }

  const handleInstall = async () => {
    if (!deferredPrompt) {
      // Fallback for browsers that don't support beforeinstallprompt
      toast.info('Add Tunami to your home screen using your browser\'s menu')
      return
    }

    try {
      // Show the install prompt
      await deferredPrompt.prompt()
      
      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice
      
      console.log('🎵 User choice:', outcome)
      
      if (outcome === 'accepted') {
        toast.info('Tunami is being added to your device')
      }
      
      setDeferredPrompt(null)
      setShowInstallPrompt(false)
      
    } catch (error) {
      console.error('🎵 Install prompt error:', error)
      toast.error('Unable to install the app. Please try again.')
    }
  }

  const dismissInstallPrompt = () => {
    setShowInstallPrompt(false)
    setDeferredPrompt(null)
    
    // Don't show again for a while
    localStorage.setItem('tunami-install-dismissed', Date.now().toString())
  }

  // Check if we should show install prompt based on previous dismissals
  const shouldShowInstallPrompt = () => {
    const dismissed = localStorage.getItem('tunami-install-dismissed')
    if (dismissed) {
      const dismissedTime = parseInt(dismissed)
      const dayInMs = 24 * 60 * 60 * 1000
      return Date.now() - dismissedTime > dayInMs * 7 // Show again after 7 days
    }
    return true
  }

  // Enhanced installation detection
  const detectInstallation = () => {
    // iOS Safari
    if ((window.navigator as any).standalone) {
      return true
    }
    
    // Android Chrome
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return true
    }
    
    // Desktop PWA
    if (document.referrer.startsWith('android-app://')) {
      return true
    }
    
    return false
  }

  // Preload critical resources
  const preloadCriticalResources = () => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      // Request service worker to cache audio files that are likely to be played
      navigator.serviceWorker.controller.postMessage({
        type: 'PRELOAD_CRITICAL',
        urls: [
          '/api/tracks/popular',
          '/api/user/recent'
        ]
      })
    }
  }

  // Register for background sync
  const registerBackgroundSync = async () => {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      const registration = await navigator.serviceWorker.ready
      
      try {
        await registration.sync.register('tunami-background-sync')
        console.log('🎵 Background sync registered')
      } catch (error) {
        console.log('🎵 Background sync failed:', error)
      }
    }
  }

  // Setup push notifications
  const setupPushNotifications = async () => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      try {
        const registration = await navigator.serviceWorker.ready
        
        // Check if already subscribed
        const existingSubscription = await registration.pushManager.getSubscription()
        
        if (!existingSubscription) {
          console.log('🎵 Push notifications available but not subscribed')
          // Don't auto-subscribe, let user opt-in
        } else {
          console.log('🎵 Push notifications already subscribed')
        }
      } catch (error) {
        console.log('🎵 Push notifications setup failed:', error)
      }
    }
  }

  // Initialize additional PWA features
  useEffect(() => {
    if (!isLoading) {
      preloadCriticalResources()
      registerBackgroundSync()
      setupPushNotifications()
    }
  }, [isLoading])

  // Don't render anything if service workers aren't supported
  if (!('serviceWorker' in navigator)) {
    return null
  }

  return (
    <>
      {/* Install Prompt */}
      {showInstallPrompt && shouldShowInstallPrompt() && !isStandalone && (
        <div className="fixed bottom-4 left-4 right-4 z-50 md:bottom-6 md:left-6 md:right-auto md:max-w-sm">
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-4 shadow-lg">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-2xl">🎵</span>
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="text-white font-semibold text-sm">
                  Install Tunami
                </h3>
                <p className="text-white/80 text-xs mt-1">
                  Add to your home screen for the best experience
                </p>
                
                <div className="flex space-x-2 mt-3">
                  <button
                    onClick={handleInstall}
                    className="px-3 py-1.5 bg-white text-purple-600 text-xs font-medium rounded hover:bg-gray-100 transition-colors"
                  >
                    Install
                  </button>
                  <button
                    onClick={dismissInstallPrompt}
                    className="px-3 py-1.5 text-white/80 text-xs hover:text-white transition-colors"
                  >
                    Not now
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Update Available Banner */}
      {updateAvailable && (
        <div className="fixed top-4 left-4 right-4 z-50 md:top-6 md:left-6 md:right-auto md:max-w-sm">
          <div className="bg-blue-600 rounded-lg p-4 shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-semibold text-sm">
                  Update Available
                </h3>
                <p className="text-white/80 text-xs mt-1">
                  A new version is ready to install
                </p>
              </div>
              
              <button
                onClick={handleUpdate}
                className="px-3 py-1.5 bg-white text-blue-600 text-xs font-medium rounded hover:bg-gray-100 transition-colors"
              >
                Update
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading indicator (hidden, just for development) */}
      {isLoading && process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className="bg-gray-800 text-white px-3 py-2 rounded text-xs">
            Registering SW...
          </div>
        </div>
      )}
    </>
  )
}

// Hook for using PWA features
export function usePWA() {
  const [isInstalled, setIsInstalled] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)
  const [canInstall, setCanInstall] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<PWAInstallPrompt | null>(null)

  useEffect(() => {
    // Check installation status
    const checkStatus = () => {
      const standalone = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone ||
                        document.referrer.includes('android-app://')
      
      setIsStandalone(standalone)
      setIsInstalled(standalone)
    }

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: WindowEventMap['beforeinstallprompt']) => {
      e.preventDefault()
      setDeferredPrompt(e as any)
      setCanInstall(true)
    }

    checkStatus()
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    }
  }, [])

  const installApp = async () => {
    if (deferredPrompt) {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        setCanInstall(false)
        setDeferredPrompt(null)
      }
      
      return outcome === 'accepted'
    }
    return false
  }

  const shareApp = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Tunami - AI Music Platform',
          text: 'Discover amazing AI-generated music on Tunami',
          url: window.location.origin
        })
        return true
      } catch (error) {
        console.log('Share failed:', error)
        return false
      }
    }
    return false
  }

  return {
    isInstalled,
    isStandalone,
    canInstall,
    installApp,
    shareApp
  }
} 