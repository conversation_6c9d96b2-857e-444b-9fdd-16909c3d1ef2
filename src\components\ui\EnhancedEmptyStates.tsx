'use client'

import { 
  Music, 
  Upload, 
  Search, 
  Heart, 
  PlayCircle, 
  Headphones, 
  Sparkles, 
  Plus,
  ArrowRight,
  Mic,
  Radio,
  Clock,
  Users,
  TrendingUp
} from 'lucide-react'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'

interface EmptyStateProps {
  icon: React.ComponentType<{ className?: string }>
  title: string
  description: string
  primaryAction?: {
    label: string
    onClick: () => void
    icon?: React.ComponentType<{ className?: string }>
  }
  secondaryAction?: {
    label: string
    onClick: () => void
    icon?: React.ComponentType<{ className?: string }>
  }
  illustration?: React.ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'welcome' | 'error' | 'success'
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  primaryAction,
  secondaryAction,
  illustration,
  className = '',
  size = 'md',
  variant = 'default'
}: EmptyStateProps) {
  const sizeClasses = {
    sm: {
      container: 'py-8',
      icon: 'w-12 h-12',
      title: 'text-lg',
      description: 'text-sm',
      button: 'px-4 py-2 text-sm'
    },
    md: {
      container: 'py-12',
      icon: 'w-16 h-16',
      title: 'text-xl',
      description: 'text-base',
      button: 'px-6 py-3 text-base'
    },
    lg: {
      container: 'py-16',
      icon: 'w-20 h-20',
      title: 'text-2xl',
      description: 'text-lg',
      button: 'px-8 py-4 text-lg'
    }
  }

  const variantClasses = {
    default: {
      icon: 'text-gray-400',
      title: 'text-gray-300',
      description: 'text-gray-500'
    },
    welcome: {
      icon: 'text-purple-400',
      title: 'text-white',
      description: 'text-gray-300'
    },
    error: {
      icon: 'text-red-400',
      title: 'text-red-300',
      description: 'text-red-400'
    },
    success: {
      icon: 'text-green-400',
      title: 'text-green-300',
      description: 'text-green-400'
    }
  }

  const classes = sizeClasses[size]
  const colors = variantClasses[variant]

  return (
    <ComponentErrorBoundary>
      <div className={`text-center ${classes.container} ${className}`}>
        {illustration || (
          <div className={`${classes.icon} ${colors.icon} mx-auto mb-4`}>
            <Icon className="w-full h-full" />
          </div>
        )}
        
        <h3 className={`${classes.title} ${colors.title} font-semibold mb-2`}>
          {title}
        </h3>
        
        <p className={`${classes.description} ${colors.description} mb-6 max-w-md mx-auto`}>
          {description}
        </p>
        
        {(primaryAction || secondaryAction) && (
          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
            {primaryAction && (
              <button
                onClick={primaryAction.onClick}
                className={`${classes.button} bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2`}
              >
                {primaryAction.icon && <primaryAction.icon className="w-4 h-4" />}
                {primaryAction.label}
              </button>
            )}
            
            {secondaryAction && (
              <button
                onClick={secondaryAction.onClick}
                className={`${classes.button} bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg font-medium transition-all duration-200 flex items-center gap-2`}
              >
                {secondaryAction.icon && <secondaryAction.icon className="w-4 h-4" />}
                {secondaryAction.label}
              </button>
            )}
          </div>
        )}
      </div>
    </ComponentErrorBoundary>
  )
}

// Animated illustration component
function AnimatedMusicIllustration() {
  return (
    <div className="relative w-24 h-24 mx-auto mb-6">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full opacity-20 animate-pulse"></div>
      <div className="absolute inset-2 bg-gradient-to-br from-purple-400 to-blue-400 rounded-full opacity-40 animate-ping"></div>
      <div className="absolute inset-4 bg-gradient-to-br from-purple-300 to-blue-300 rounded-full flex items-center justify-center">
        <Music className="w-8 h-8 text-white animate-bounce" />
      </div>
    </div>
  )
}

// Predefined empty states for common scenarios

export function WelcomeToTunami({ onGetStarted, onExplore }: { 
  onGetStarted?: () => void
  onExplore?: () => void 
}) {
  return (
    <EmptyState
      icon={Sparkles}
      title="Welcome to Tunami!"
      description="Your AI-powered music streaming platform. Discover, create, and share amazing music generated by artificial intelligence."
      illustration={<AnimatedMusicIllustration />}
      primaryAction={onGetStarted ? {
        label: "Get Started",
        onClick: onGetStarted,
        icon: ArrowRight
      } : undefined}
      secondaryAction={onExplore ? {
        label: "Explore Music",
        onClick: onExplore,
        icon: Search
      } : undefined}
      variant="welcome"
      size="lg"
    />
  )
}

export function NoTracksFound({ 
  onUpload, 
  onExplore, 
  searchQuery 
}: { 
  onUpload?: () => void
  onExplore?: () => void
  searchQuery?: string 
}) {
  const title = searchQuery ? "No tracks found" : "No music yet"
  const description = searchQuery 
    ? `We couldn't find any tracks matching "${searchQuery}". Try a different search term or explore our featured content.`
    : "Start building your music library by uploading your AI-generated tracks or exploring what others have created."

  return (
    <EmptyState
      icon={Music}
      title={title}
      description={description}
      primaryAction={onUpload ? {
        label: "Upload Track",
        onClick: onUpload,
        icon: Upload
      } : undefined}
      secondaryAction={onExplore ? {
        label: "Explore Music",
        onClick: onExplore,
        icon: Search
      } : undefined}
    />
  )
}

export function NoPlaylistsYet({ onCreate, onDiscover }: { 
  onCreate?: () => void
  onDiscover?: () => void 
}) {
  return (
    <EmptyState
      icon={PlayCircle}
      title="No playlists yet"
      description="Create your first playlist to organize your favorite AI-generated tracks and discover new music."
      primaryAction={onCreate ? {
        label: "Create Playlist",
        onClick: onCreate,
        icon: Plus
      } : undefined}
      secondaryAction={onDiscover ? {
        label: "Discover Music",
        onClick: onDiscover,
        icon: TrendingUp
      } : undefined}
    />
  )
}

export function NoLikedTracks({ onExplore }: { onExplore?: () => void }) {
  return (
    <EmptyState
      icon={Heart}
      title="No liked tracks"
      description="Start liking tracks to build your personal collection of favorite AI-generated music."
      primaryAction={onExplore ? {
        label: "Explore Music",
        onClick: onExplore,
        icon: Search
      } : undefined}
    />
  )
}

export function NoRecentlyPlayed({ onExplore }: { onExplore?: () => void }) {
  return (
    <EmptyState
      icon={Clock}
      title="No recently played tracks"
      description="Your listening history will appear here once you start playing music. Discover amazing AI-generated tracks to get started."
      primaryAction={onExplore ? {
        label: "Start Listening",
        onClick: onExplore,
        icon: Headphones
      } : undefined}
    />
  )
}

export function NoUploadHistory({ onUpload }: { onUpload?: () => void }) {
  return (
    <EmptyState
      icon={Upload}
      title="No uploads yet"
      description="Share your AI-generated music with the world. Upload your first track to get started."
      primaryAction={onUpload ? {
        label: "Upload Your First Track",
        onClick: onUpload,
        icon: Upload
      } : undefined}
      variant="welcome"
    />
  )
}

export function NoSearchResults({ 
  query, 
  onClearSearch, 
  onExplore 
}: { 
  query: string
  onClearSearch?: () => void
  onExplore?: () => void 
}) {
  return (
    <EmptyState
      icon={Search}
      title="No results found"
      description={`We couldn't find anything matching "${query}". Try different keywords or explore trending music.`}
      primaryAction={onClearSearch ? {
        label: "Clear Search",
        onClick: onClearSearch
      } : undefined}
      secondaryAction={onExplore ? {
        label: "Explore Trending",
        onClick: onExplore,
        icon: TrendingUp
      } : undefined}
    />
  )
}

export function NoFollowing({ onDiscover }: { onDiscover?: () => void }) {
  return (
    <EmptyState
      icon={Users}
      title="Not following anyone yet"
      description="Follow other creators to see their latest AI-generated music and stay updated with their releases."
      primaryAction={onDiscover ? {
        label: "Discover Creators",
        onClick: onDiscover,
        icon: Search
      } : undefined}
    />
  )
}

export function NoNotifications() {
  return (
    <EmptyState
      icon={Radio}
      title="No notifications"
      description="You're all caught up! New notifications about your music, followers, and activity will appear here."
      size="sm"
    />
  )
}

export function OfflineMode({ onRetry }: { onRetry?: () => void }) {
  return (
    <EmptyState
      icon={Radio}
      title="You're offline"
      description="Check your internet connection to access your music library and discover new tracks."
      primaryAction={onRetry ? {
        label: "Try Again",
        onClick: onRetry
      } : undefined}
      variant="error"
    />
  )
}

export function ComingSoon({ 
  feature, 
  onNotifyMe 
}: { 
  feature: string
  onNotifyMe?: () => void 
}) {
  return (
    <EmptyState
      icon={Sparkles}
      title={`${feature} Coming Soon`}
      description={`We're working hard to bring you ${feature.toLowerCase()}. Stay tuned for updates!`}
      primaryAction={onNotifyMe ? {
        label: "Notify Me",
        onClick: onNotifyMe
      } : undefined}
      variant="welcome"
    />
  )
}

// Loading state with empty state styling
export function LoadingEmptyState({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 relative">
        <div className="absolute inset-0 border-4 border-purple-200 rounded-full"></div>
        <div className="absolute inset-0 border-4 border-purple-600 rounded-full border-t-transparent animate-spin"></div>
        <Music className="absolute inset-2 w-8 h-8 text-purple-600" />
      </div>
      <h3 className="text-xl text-white font-semibold mb-2">{message}</h3>
      <p className="text-gray-400">Please wait while we load your content...</p>
    </div>
  )
}

// Error state with retry functionality
export function ErrorEmptyState({ 
  title = "Something went wrong",
  description = "We encountered an error while loading your content.",
  onRetry 
}: { 
  title?: string
  description?: string
  onRetry?: () => void 
}) {
  return (
    <EmptyState
      icon={Radio}
      title={title}
      description={description}
      primaryAction={onRetry ? {
        label: "Try Again",
        onClick: onRetry
      } : undefined}
      variant="error"
    />
  )
} 