import React, { useState } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  sizes?: string
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  onLoad?: () => void
  onError?: () => void
  fallbackSrc?: string
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape'
}

const aspectRatioClasses = {
  square: 'aspect-square',
  video: 'aspect-video',
  portrait: 'aspect-[3/4]',
  landscape: 'aspect-[4/3]'
}

// Generate a low-quality placeholder for better UX
const generateBlurDataURL = (width: number = 8, height: number = 8): string => {
  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height
  const ctx = canvas.getContext('2d')
  
  if (ctx) {
    // Create a gradient placeholder
    const gradient = ctx.createLinearGradient(0, 0, width, height)
    gradient.addColorStop(0, '#374151')
    gradient.addColorStop(1, '#1f2937')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)
  }
  
  return canvas.toDataURL()
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  quality = 75,
  placeholder = 'blur',
  blurDataURL,
  onLoad,
  onError,
  fallbackSrc = '/images/placeholder-music.svg',
  aspectRatio
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [currentSrc, setCurrentSrc] = useState(src)

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    setIsLoading(false)
    if (currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc)
    }
    onError?.()
  }

  const containerClasses = cn(
    'relative overflow-hidden',
    aspectRatio && aspectRatioClasses[aspectRatio],
    className
  )

  const imageClasses = cn(
    'transition-opacity duration-300',
    isLoading && 'opacity-0',
    !isLoading && 'opacity-100'
  )

  // Auto-generate blur placeholder if not provided
  const finalBlurDataURL = blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined)

  return (
    <div className={containerClasses}>
      {/* Loading skeleton */}
      {isLoading && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-700 to-gray-800 animate-pulse" />
      )}

      <Image
        src={currentSrc}
        alt={alt}
        width={width}
        height={height}
        className={imageClasses}
        priority={priority}
        sizes={sizes}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={finalBlurDataURL}
        onLoad={handleLoad}
        onError={handleError}
        fill={!width && !height}
        style={{
          objectFit: 'cover',
          objectPosition: 'center'
        }}
      />

      {/* Error state */}
      {hasError && currentSrc === fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
          <div className="text-center text-gray-400">
            <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            <p className="text-xs">Image unavailable</p>
          </div>
        </div>
      )}
    </div>
  )
}

// Specialized components for common use cases
export const AlbumArt: React.FC<Omit<OptimizedImageProps, 'aspectRatio'> & { size?: 'sm' | 'md' | 'lg' | 'xl' }> = ({
  size = 'md',
  ...props
}) => {
  const sizeMap = {
    sm: { width: 64, height: 64 },
    md: { width: 128, height: 128 },
    lg: { width: 256, height: 256 },
    xl: { width: 512, height: 512 }
  }

  return (
    <OptimizedImage
      {...props}
      {...sizeMap[size]}
      aspectRatio="square"
      placeholder="blur"
      quality={85}
      sizes={`${sizeMap[size].width}px`}
    />
  )
}

export const ProfileAvatar: React.FC<Omit<OptimizedImageProps, 'aspectRatio'> & { size?: number }> = ({
  size = 64,
  ...props
}) => {
  return (
    <OptimizedImage
      {...props}
      width={size}
      height={size}
      aspectRatio="square"
      className={cn('rounded-full', props.className)}
      fallbackSrc="/images/default-avatar.svg"
      quality={90}
      sizes={`${size}px`}
    />
  )
}

export const PlaylistCover: React.FC<Omit<OptimizedImageProps, 'aspectRatio'>> = (props) => {
  return (
    <OptimizedImage
      {...props}
      aspectRatio="square"
      placeholder="blur"
      quality={80}
      fallbackSrc="/images/playlist-placeholder.svg"
    />
  )
} 