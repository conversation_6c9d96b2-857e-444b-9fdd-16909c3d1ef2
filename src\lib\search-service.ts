import { supabase } from './supabase'
import { 
  SearchSuggestion, 
  SearchHistory, 
  SearchResults, 
  SearchFilters, 
  SearchArtist,
  SearchAnalytics,
  PopularSearch,
  SearchResultItem
} from '@/types/search'
import { Track, mapDatabaseTrackToFrontend } from '@/types/track'
import { Playlist } from '@/types/playlist'

export class SearchService {
  private static readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
  private static suggestionCache = new Map<string, { data: SearchSuggestion[], timestamp: number }>()

  /**
   * Perform a comprehensive search across tracks, playlists, and artists
   */
  static async search(
    query: string, 
    filters: SearchFilters = {}, 
    page = 1, 
    perPage = 20
  ): Promise<SearchResults> {
    const startTime = Date.now()
    
    try {
      // Use RPC function for optimized search
      const { data, error } = await supabase.rpc('search_content', {
        search_query: query.trim(),
        search_filters: filters,
        page_number: page,
        page_size: perPage
      })

      if (error) {
        throw new Error(`Search failed: ${error.message}`)
      }

      // Transform the results
      const results: SearchResults = {
        tracks: data.tracks?.map((track: any) => mapDatabaseTrackToFrontend(track)) || [],
        playlists: data.playlists || [],
        artists: data.artists || [],
        total_tracks: data.total_tracks || 0,
        total_playlists: data.total_playlists || 0,
        total_artists: data.total_artists || 0,
        query,
        filters,
        page,
        per_page: perPage,
        has_more: data.has_more || false
      }

      // Track search analytics
      const searchTime = Date.now() - startTime
      this.trackSearchAnalytics(query, results, filters, searchTime)

      return results
    } catch (error) {
      console.error('Search error:', error)
      throw error
    }
  }

  /**
   * Get search suggestions with caching
   */
  static async getSuggestions(query: string, category: string = 'all'): Promise<SearchSuggestion[]> {
    try {
      // Mock implementation - replace with actual API call
      const suggestions: SearchSuggestion[] = []
      
      if (query.length < 2) return suggestions

      // Add query suggestion
      suggestions.push({
        id: `query-${query}`,
        text: query,
        type: 'query'
      })

      // Mock track suggestions
      if (category === 'all' || category === 'tracks') {
        suggestions.push(
          {
            id: 'track-1',
            text: `${query} - Electronic Dreams`,
            type: 'track',
            metadata: { artist: 'AI Composer', genre: 'Electronic' }
          },
          {
            id: 'track-2', 
            text: `${query} Beat`,
            type: 'track',
            metadata: { artist: 'Suno AI', aiTool: 'suno' }
          }
        )
      }

      // Mock artist suggestions
      if (category === 'all' || category === 'artists') {
        suggestions.push({
          id: 'artist-1',
          text: `${query} Artist`,
          type: 'artist',
          metadata: { genre: 'Various' }
        })
      }

      return suggestions.slice(0, 8) // Limit to 8 suggestions
    } catch (error) {
      console.error('Error fetching suggestions:', error)
      return []
    }
  }

  /**
   * Search tracks with advanced filtering
   */
  static async searchTracks(
    query: string, 
    filters: SearchFilters = {},
    page = 1,
    perPage = 20
  ): Promise<{ tracks: Track[], total: number, hasMore: boolean }> {
    let queryBuilder = supabase
      .from('tracks')
      .select('*', { count: 'exact' })

    // Apply text search
    if (query.trim()) {
      queryBuilder = queryBuilder.or(`title.ilike.%${query}%,artist_name.ilike.%${query}%`)
    }

    // Apply filters
    if (filters.ai_tool && filters.ai_tool.length > 0) {
      queryBuilder = queryBuilder.in('ai_tool', filters.ai_tool)
    }

    if (filters.genre && filters.genre.length > 0) {
      queryBuilder = queryBuilder.in('genre', filters.genre)
    }

    if (filters.duration_min !== undefined) {
      queryBuilder = queryBuilder.gte('duration', filters.duration_min)
    }

    if (filters.duration_max !== undefined) {
      queryBuilder = queryBuilder.lte('duration', filters.duration_max)
    }

    if (filters.date_from) {
      queryBuilder = queryBuilder.gte('created_at', filters.date_from)
    }

    if (filters.date_to) {
      queryBuilder = queryBuilder.lte('created_at', filters.date_to)
    }

    if (filters.is_public !== undefined) {
      queryBuilder = queryBuilder.eq('is_public', filters.is_public)
    }

    // Apply sorting
    const sortBy = filters.sort_by || 'created_at'
    const sortOrder = filters.sort_order === 'asc' ? { ascending: true } : { ascending: false }
    
    if (sortBy === 'relevance' && query.trim()) {
      // Use text search ranking for relevance
      queryBuilder = queryBuilder.order('title', sortOrder)
    } else {
      queryBuilder = queryBuilder.order(sortBy, sortOrder)
    }

    // Apply pagination
    const from = (page - 1) * perPage
    const to = from + perPage - 1
    queryBuilder = queryBuilder.range(from, to)

    const { data, error, count } = await queryBuilder

    if (error) {
      throw new Error(`Track search failed: ${error.message}`)
    }

    const tracks = (data || []).map(mapDatabaseTrackToFrontend)
    const total = count || 0
    const hasMore = from + perPage < total

    return { tracks, total, hasMore }
  }

  /**
   * Search playlists
   */
  static async searchPlaylists(
    query: string,
    filters: SearchFilters = {},
    page = 1,
    perPage = 20
  ): Promise<{ playlists: Playlist[], total: number, hasMore: boolean }> {
    let queryBuilder = supabase
      .from('playlists')
      .select(`
        *,
        playlist_tracks(count)
      `, { count: 'exact' })

    // Apply text search
    if (query.trim()) {
      queryBuilder = queryBuilder.or(`name.ilike.%${query}%,description.ilike.%${query}%`)
    }

    // Apply filters
    if (filters.is_public !== undefined) {
      queryBuilder = queryBuilder.eq('is_public', filters.is_public)
    }

    if (filters.date_from) {
      queryBuilder = queryBuilder.gte('created_at', filters.date_from)
    }

    if (filters.date_to) {
      queryBuilder = queryBuilder.lte('created_at', filters.date_to)
    }

    // Apply sorting
    const sortBy = filters.sort_by || 'updated_at'
    const sortOrder = filters.sort_order === 'asc' ? { ascending: true } : { ascending: false }
    queryBuilder = queryBuilder.order(sortBy, sortOrder)

    // Apply pagination
    const from = (page - 1) * perPage
    const to = from + perPage - 1
    queryBuilder = queryBuilder.range(from, to)

    const { data, error, count } = await queryBuilder

    if (error) {
      throw new Error(`Playlist search failed: ${error.message}`)
    }

    const playlists = (data || []).map(playlist => ({
      ...playlist,
      track_count: playlist.playlist_tracks?.[0]?.count || 0
    }))

    const total = count || 0
    const hasMore = from + perPage < total

    return { playlists, total, hasMore }
  }

  /**
   * Search artists (aggregated from tracks)
   */
  static async searchArtists(
    query: string,
    page = 1,
    perPage = 20
  ): Promise<{ artists: SearchArtist[], total: number, hasMore: boolean }> {
    const { data, error } = await supabase.rpc('search_artists', {
      search_query: query.trim(),
      page_number: page,
      page_size: perPage
    })

    if (error) {
      throw new Error(`Artist search failed: ${error.message}`)
    }

    const artists: SearchArtist[] = data?.artists || []
    const total = data?.total || 0
    const hasMore = (page * perPage) < total

    return { artists, total, hasMore }
  }

  /**
   * Get search history for a user
   */
  static async getSearchHistory(userId: string): Promise<SearchHistory[]> {
    try {
      const { data, error } = await supabase
        .from('search_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(20)

      if (error) throw error

      return data?.map(item => ({
        id: item.id,
        query: item.query,
        timestamp: new Date(item.created_at),
        results_count: item.results_count,
        category: item.category
      })) || []
    } catch (error) {
      console.error('Error fetching search history:', error)
      return []
    }
  }

  /**
   * Save search to history
   */
  static async saveSearchHistory(userId: string, query: string, category: string = 'all'): Promise<void> {
    try {
      // Check if this exact query already exists for this user
      const { data: existing } = await supabase
        .from('search_history')
        .select('id')
        .eq('user_id', userId)
        .eq('query', query)
        .single()

      if (existing) {
        // Update timestamp of existing entry
        await supabase
          .from('search_history')
          .update({ created_at: new Date().toISOString() })
          .eq('id', existing.id)
      } else {
        // Insert new entry
        await supabase
          .from('search_history')
          .insert({
            user_id: userId,
            query,
            category,
            created_at: new Date().toISOString()
          })
      }
    } catch (error) {
      console.error('Error saving search history:', error)
    }
  }

  /**
   * Clear search history for a user
   */
  static async clearSearchHistory(userId: string): Promise<void> {
    try {
      await supabase
        .from('search_history')
        .delete()
        .eq('user_id', userId)
    } catch (error) {
      console.error('Error clearing search history:', error)
    }
  }

  /**
   * Get popular searches
   */
  static async getPopularSearches(limit = 10): Promise<PopularSearch[]> {
    const { data, error } = await supabase.rpc('get_popular_searches', {
      search_limit: limit
    })

    if (error) {
      throw new Error(`Failed to get popular searches: ${error.message}`)
    }

    return data || []
  }

  /**
   * Track search analytics
   */
  private static async trackSearchAnalytics(
    query: string,
    results: SearchResults,
    filters: SearchFilters,
    searchTime: number
  ): Promise<void> {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      const analytics: Omit<SearchAnalytics, 'created_at'> = {
        query: query.trim(),
        result_count: results.total_tracks + results.total_playlists + results.total_artists,
        filters_used: filters,
        search_time: searchTime,
        user_id: user.user?.id
      }

      await supabase
        .from('search_analytics')
        .insert({
          ...analytics,
          created_at: new Date().toISOString()
        })
    } catch (error) {
      // Don't throw on analytics errors
      console.error('Failed to track search analytics:', error)
    }
  }

  /**
   * Track search result click
   */
  static async trackResultClick(
    query: string,
    resultType: 'track' | 'playlist' | 'artist',
    resultId: string,
    position: number
  ): Promise<void> {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      await supabase
        .from('search_analytics')
        .insert({
          query: query.trim(),
          result_count: 0, // Will be updated by trigger
          clicked_result: {
            type: resultType,
            id: resultId,
            position
          },
          filters_used: {},
          search_time: 0,
          user_id: user.user?.id,
          created_at: new Date().toISOString()
        })
    } catch (error) {
      console.error('Failed to track result click:', error)
    }
  }

  /**
   * Get search suggestions based on user's search history and popular searches
   */
  static async getPersonalizedSuggestions(userId: string): Promise<SearchSuggestion[]> {
    const { data, error } = await supabase.rpc('get_personalized_suggestions', {
      user_id: userId,
      max_suggestions: 8
    })

    if (error) {
      console.error('Failed to get personalized suggestions:', error)
      return []
    }

    return data || []
  }

  /**
   * Clear suggestion cache
   */
  static clearSuggestionCache(): void {
    this.suggestionCache.clear()
  }

  /**
   * Get unified search results for display
   */
  static async getUnifiedResults(
    query: string,
    filters: SearchFilters = {},
    page = 1,
    perPage = 20
  ): Promise<SearchResultItem[]> {
    const results = await this.search(query, filters, page, perPage)
    const unified: SearchResultItem[] = []

    // Add tracks
    results.tracks.forEach((track, index) => {
      unified.push({
        id: track.id,
        type: 'track',
        title: track.title,
        subtitle: track.artist,
        image_url: track.cover_image_url,
        metadata: {
          duration: track.duration,
          ai_tool: track.ai_tool,
          genre: track.genre,
          created_at: track.created_at,
          is_public: track.is_public
        },
        relevance_score: 1.0 - (index * 0.1) // Simple relevance scoring
      })
    })

    // Add playlists
    results.playlists.forEach((playlist, index) => {
      unified.push({
        id: playlist.id,
        type: 'playlist',
        title: playlist.name,
        subtitle: playlist.description || `${playlist.track_count || 0} tracks`,
        image_url: playlist.cover_image_url,
        metadata: {
          track_count: playlist.track_count,
          created_at: playlist.created_at,
          is_public: playlist.is_public
        },
        relevance_score: 0.8 - (index * 0.1)
      })
    })

    // Add artists
    results.artists.forEach((artist, index) => {
      unified.push({
        id: artist.id,
        type: 'artist',
        title: artist.name,
        subtitle: `${artist.track_count} tracks`,
        image_url: artist.avatar_url,
        metadata: {
          track_count: artist.track_count
        },
        relevance_score: 0.6 - (index * 0.1)
      })
    })

    // Sort by relevance score
    return unified.sort((a, b) => b.relevance_score - a.relevance_score)
  }

  static async getTrendingSearches(): Promise<string[]> {
    try {
      // Mock trending searches - replace with actual analytics
      return [
        'AI Electronic',
        'Suno Beats',
        'Chill Lo-fi',
        'Synthwave',
        'AI Jazz',
        'Ambient Dreams',
        'Future Bass',
        'Neural Music'
      ]
    } catch (error) {
      console.error('Error fetching trending searches:', error)
      return []
    }
  }

  static async search(
    query: string, 
    category: string = 'all', 
    limit: number = 20,
    offset: number = 0
  ): Promise<SearchResults> {
    try {
      const results: SearchResults = {
        tracks: [],
        artists: [],
        playlists: [],
        users: [],
        totalCount: 0,
        hasMore: false
      }

      // Search tracks
      if (category === 'all' || category === 'tracks') {
        const { data: tracks, error } = await supabase
          .from('tracks')
          .select(`
            id,
            title,
            artist,
            album,
            duration,
            genre,
            ai_tool,
            cover_image_url,
            is_public,
            play_count,
            created_at
          `)
          .or(`title.ilike.%${query}%, artist.ilike.%${query}%`)
          .eq('is_public', true)
          .order('play_count', { ascending: false })
          .range(offset, offset + limit - 1)

        if (!error && tracks) {
          results.tracks = tracks.map(track => ({
            id: track.id,
            title: track.title,
            artist: track.artist,
            album: track.album,
            duration: track.duration || 0,
            genre: track.genre,
            aiTool: track.ai_tool,
            coverArt: track.cover_image_url,
            isPublic: track.is_public,
            playCount: track.play_count || 0,
            likeCount: 0, // TODO: Add likes count
            createdAt: new Date(track.created_at)
          }))
        }
      }

      // Search artists (simplified)
      if (category === 'all' || category === 'artists') {
        const { data: artists } = await supabase
          .from('tracks')
          .select('artist')
          .ilike('artist', `%${query}%`)
          .eq('is_public', true)

        if (artists) {
          const uniqueArtists = [...new Set(artists.map(a => a.artist))]
          results.artists = uniqueArtists.slice(0, 10).map((name, index) => ({
            id: `artist-${index}`,
            name,
            trackCount: 0,
            followerCount: 0,
            isVerified: false,
            genres: []
          }))
        }
      }

      results.totalCount = results.tracks.length + results.artists.length + results.playlists.length + results.users.length
      results.hasMore = results.totalCount >= limit

      return results
    } catch (error) {
      console.error('Error performing search:', error)
      return {
        tracks: [],
        artists: [],
        playlists: [],
        users: [],
        totalCount: 0,
        hasMore: false
      }
    }
  }
} 