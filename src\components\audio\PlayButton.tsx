'use client'

import { Play, Pause, Loader2 } from 'lucide-react'
import { useAudio } from '@/contexts/AudioContext'
import { AudioTrack } from '@/types/audio'

export interface PlayButtonProps {
  track: AudioTrack
  size?: 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'secondary' | 'minimal'
  showQueue?: boolean // Kept for backward compatibility but not used
  className?: string
  onPlay?: () => void
  onPause?: () => void
}

export default function PlayButton({
  track,
  size = 'md',
  variant = 'primary',
  showQueue = true, // Kept for backward compatibility but not used
  className = '',
  onPlay,
  onPause
}: PlayButtonProps) {
  const {
    currentTrack,
    isPlaying,
    isLoading,
    isClient,
    togglePlayPause,
    setQueue
  } = useAudio()

  // Don't render on server side
  if (!isClient) {
    return <div className={`${getSizeClasses(size)} ${className}`} />
  }

  const isCurrentTrack = currentTrack?.id === track.id
  const isTrackPlaying = isCurrentTrack && isPlaying
  const isTrackLoading = isCurrentTrack && isLoading

  const handleMainClick = () => {
    if (isCurrentTrack) {
      // If this track is currently loaded, toggle play/pause
      togglePlayPause()
      if (isPlaying) {
        onPause?.()
      } else {
        onPlay?.()
      }
    } else {
      // If different track, replace queue with this track and play
      setQueue([track])
      onPlay?.()
    }
  }



  function getSizeClasses(size: 'sm' | 'md' | 'lg') {
    switch (size) {
      case 'sm':
        return 'w-8 h-8'
      case 'lg':
        return 'w-16 h-16' // Made larger for better visibility
      default:
        return 'w-10 h-10'
    }
  }

  function getIconSize(size: 'sm' | 'md' | 'lg') {
    switch (size) {
      case 'sm':
        return 'w-4 h-4'
      case 'lg':
        return 'w-8 h-8' // Made larger to match button size
      default:
        return 'w-5 h-5'
    }
  }

  function getVariantClasses(variant: 'primary' | 'secondary' | 'minimal') {
    switch (variant) {
      case 'secondary':
        return 'bg-gray-700 hover:bg-gray-600 text-white'
      case 'minimal':
        return 'bg-transparent hover:bg-gray-800 text-gray-300 hover:text-white border border-gray-600 hover:border-gray-500'
      default:
        return 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl'
    }
  }

  return (
    <div className="relative inline-block">
      {/* Main Play Button */}
      <button
        onClick={handleMainClick}
        disabled={isTrackLoading}
        className={`
          ${getSizeClasses(size)}
          ${getVariantClasses(variant)}
          rounded-full flex items-center justify-center transition-all duration-200 transform hover:scale-105 disabled:opacity-75 disabled:cursor-not-allowed disabled:transform-none
          ${className}
        `}
        title={isCurrentTrack ? (isPlaying ? 'Pause' : 'Play') : 'Play Track'}
      >
        {isTrackLoading ? (
          <Loader2 className={`${getIconSize(size)} animate-spin`} />
        ) : isTrackPlaying ? (
          <Pause className={getIconSize(size)} />
        ) : (
          <Play className={`${getIconSize(size)} ml-0.5`} />
        )}
      </button>
    </div>
  )
} 