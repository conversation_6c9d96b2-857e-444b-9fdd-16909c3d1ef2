'use client'

import { useState, useCallback, useMemo } from 'react'
import { Tag, User, Sparkles, Calendar, Hash, Music, AlertCircle, Info } from 'lucide-react'
import { ErrorMessage, ValidationError } from '@/components/ui/ErrorMessage'
import { useFormValidation, CommonRules } from '@/lib/form-validation'

export interface UploadMetadata {
  title: string
  artist: string
  aiTool: string
  genre: string
  description: string
  tags: string[]
  isPublic: boolean
}

interface UploadFormProps {
  metadata: UploadMetadata
  onMetadataChange: (metadata: UploadMetadata) => void
  isSubmitting?: boolean
  className?: string
  errors?: Record<string, string>
  onValidationChange?: (isValid: boolean) => void
}

interface FormErrors {
  title?: string
  artist?: string
  aiTool?: string
  genre?: string
  description?: string
  tags?: string
  general?: string
}

const AI_TOOLS = [
  { id: 'suno', name: 'Suno AI', description: 'AI music generation platform', category: 'Popular' },
  { id: 'udio', name: 'U<PERSON>', description: 'Advanced AI music creation', category: 'Popular' },
  { id: 'musicgen', name: 'MusicGen', description: 'Meta\'s music generation model', category: 'Open Source' },
  { id: 'stable-audio', name: 'Stable Audio', description: 'Stability AI music model', category: 'Open Source' },
  { id: 'soundraw', name: 'Soundraw', description: 'AI music composition tool', category: 'Commercial' },
  { id: 'mubert', name: 'Mubert', description: 'AI music streaming and generation', category: 'Commercial' },
  { id: 'aiva', name: 'AIVA', description: 'AI composer for emotional music', category: 'Commercial' },
  { id: 'beatoven', name: 'Beatoven.ai', description: 'AI music for content creators', category: 'Commercial' },
  { id: 'boomy', name: 'Boomy', description: 'Create songs with AI instantly', category: 'Popular' },
  { id: 'wavtool', name: 'WavTool', description: 'Browser-based AI music production', category: 'Online' },
  { id: 'custom', name: 'Custom Model', description: 'Your own AI model or tool', category: 'Other' },
  { id: 'other', name: 'Other', description: 'Other AI music generation tool', category: 'Other' }
]

const GENRES = [
  { name: 'Electronic', popular: true },
  { name: 'Pop', popular: true },
  { name: 'Hip Hop', popular: true },
  { name: 'Rock', popular: true },
  { name: 'Jazz', popular: false },
  { name: 'Classical', popular: false },
  { name: 'Folk', popular: false },
  { name: 'Country', popular: false },
  { name: 'R&B/Soul', popular: true },
  { name: 'Reggae', popular: false },
  { name: 'Blues', popular: false },
  { name: 'Punk', popular: false },
  { name: 'Metal', popular: false },
  { name: 'Ambient', popular: true },
  { name: 'House', popular: true },
  { name: 'Techno', popular: true },
  { name: 'Trance', popular: false },
  { name: 'Dubstep', popular: false },
  { name: 'Indie', popular: true },
  { name: 'Alternative', popular: false },
  { name: 'World', popular: false },
  { name: 'Instrumental', popular: true },
  { name: 'Soundtrack', popular: false },
  { name: 'Lo-fi', popular: true },
  { name: 'Experimental', popular: false },
  { name: 'Chillout', popular: true },
  { name: 'Trap', popular: true },
  { name: 'Other', popular: false }
]

export default function UploadForm({
  metadata,
  onMetadataChange,
  isSubmitting = false,
  className = '',
  errors: externalErrors = {},
  onValidationChange
}: UploadFormProps) {
  const [tagInput, setTagInput] = useState('')
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  // Enhanced validation rules
  const validationRules = useMemo(() => ({
    title: [
      CommonRules.required('Track title is required'),
      CommonRules.minLength(2, 'Title must be at least 2 characters'),
      CommonRules.maxLength(100, 'Title must be less than 100 characters'),
      CommonRules.custom(
        (value: string) => !/^\s+$/.test(value), 
        'Title cannot be only whitespace'
      )
    ],
    artist: [
      CommonRules.maxLength(50, 'Artist name must be less than 50 characters'),
      CommonRules.custom(
        (value: string) => !value || !/^\s+$/.test(value),
        'Artist name cannot be only whitespace'
      )
    ],
    aiTool: [
      CommonRules.required('Please select the AI tool used')
    ],
    genre: [
      CommonRules.required('Please select a genre')
    ],
    description: [
      CommonRules.maxLength(500, 'Description must be less than 500 characters')
    ]
  }), [])

  // Real-time validation
  const validateField = useCallback((field: keyof UploadMetadata, value: any): string | undefined => {
    const rules = validationRules[field as keyof typeof validationRules]
    if (!rules) return undefined

    for (const rule of rules) {
      if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
        return rule.message || 'This field is required'
      }

      if (!value || (typeof value === 'string' && !value.trim())) continue

      if (rule.minLength && value.length < rule.minLength) {
        return rule.message || `Must be at least ${rule.minLength} characters`
      }

      if (rule.maxLength && value.length > rule.maxLength) {
        return rule.message || `Must be less than ${rule.maxLength} characters`
      }

      if (rule.pattern && !rule.pattern.test(value)) {
        return rule.message || 'Invalid format'
      }

      if (rule.custom && !rule.custom(value)) {
        return rule.message || 'Invalid value'
      }
    }

    return undefined
  }, [validationRules])

  // Form validation status
  const isFormValid = useMemo(() => {
    const hasRequiredFields = metadata.title.trim() && metadata.aiTool && metadata.genre
    const hasNoErrors = !validateField('title', metadata.title) &&
                       !validateField('artist', metadata.artist) &&
                       !validateField('aiTool', metadata.aiTool) &&
                       !validateField('genre', metadata.genre) &&
                       !validateField('description', metadata.description)
    
    return hasRequiredFields && hasNoErrors
  }, [metadata, validateField])

  // Notify parent of validation status
  React.useEffect(() => {
    onValidationChange?.(isFormValid)
  }, [isFormValid, onValidationChange])

  const updateMetadata = useCallback((field: keyof UploadMetadata, value: any) => {
    // Update metadata
    const newMetadata = { ...metadata, [field]: value }
    onMetadataChange(newMetadata)

    // Clear field error if value is valid
    if (touched[field]) {
      const error = validateField(field, value)
      setFormErrors(prev => ({
        ...prev,
        [field]: error
      }))
    }
  }, [metadata, onMetadataChange, touched, validateField])

  const handleFieldBlur = useCallback((field: keyof UploadMetadata) => {
    setTouched(prev => ({ ...prev, [field]: true }))
    const error = validateField(field, metadata[field])
    setFormErrors(prev => ({ ...prev, [field]: error }))
  }, [metadata, validateField])

  const handleAddTag = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      const tag = tagInput.trim().toLowerCase()
      
      if (!tag) return

      if (tag.length < 2) {
        setFormErrors(prev => ({ ...prev, tags: 'Tags must be at least 2 characters' }))
        return
      }

      if (tag.length > 20) {
        setFormErrors(prev => ({ ...prev, tags: 'Tags must be less than 20 characters' }))
        return
      }

      if (metadata.tags.includes(tag)) {
        setFormErrors(prev => ({ ...prev, tags: 'Tag already exists' }))
        return
      }

      if (metadata.tags.length >= 10) {
        setFormErrors(prev => ({ ...prev, tags: 'Maximum 10 tags allowed' }))
        return
      }

      updateMetadata('tags', [...metadata.tags, tag])
      setTagInput('')
      setFormErrors(prev => ({ ...prev, tags: undefined }))
    }
  }, [tagInput, metadata.tags, updateMetadata])

  const handleRemoveTag = useCallback((tagToRemove: string) => {
    updateMetadata('tags', metadata.tags.filter(tag => tag !== tagToRemove))
  }, [metadata.tags, updateMetadata])

  const getFieldError = useCallback((field: keyof UploadMetadata): string | undefined => {
    return formErrors[field] || externalErrors[field]
  }, [formErrors, externalErrors])

  // Group AI tools by category
  const groupedTools = useMemo(() => {
    const groups: Record<string, typeof AI_TOOLS> = {}
    AI_TOOLS.forEach(tool => {
      if (!groups[tool.category]) groups[tool.category] = []
      groups[tool.category].push(tool)
    })
    return groups
  }, [])

  // Group genres
  const { popularGenres, otherGenres } = useMemo(() => {
    const popular = GENRES.filter(g => g.popular)
    const other = GENRES.filter(g => !g.popular)
    return { popularGenres: popular, otherGenres: other }
  }, [])

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-700 pb-6">
        <h2 className="text-2xl font-bold text-white flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
            <Music className="w-5 h-5 text-white" />
          </div>
          Track Details
        </h2>
        <p className="text-gray-400 mt-2">
          Add information about your AI-generated music. Fields marked with * are required.
        </p>
      </div>

      {/* General Error Display */}
      {formErrors.general && (
        <ErrorMessage 
          message={formErrors.general}
          className="mb-6"
        />
      )}

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Title */}
        <div className="md:col-span-2">
          <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-2">
            Track Title *
          </label>
          <input
            id="title"
            type="text"
            value={metadata.title}
            onChange={(e) => updateMetadata('title', e.target.value)}
            onBlur={() => handleFieldBlur('title')}
            placeholder="Enter track title..."
            disabled={isSubmitting}
            className={`w-full px-4 py-3 bg-gray-800 border rounded-lg 
                     focus:ring-2 focus:ring-purple-500 focus:border-transparent
                     text-white placeholder-gray-400 disabled:opacity-50 disabled:cursor-not-allowed
                     transition-colors ${
                       getFieldError('title') ? 'border-red-500' : 'border-gray-700'
                     }`}
            maxLength={100}
            aria-invalid={!!getFieldError('title')}
            aria-describedby={getFieldError('title') ? 'title-error' : undefined}
            required
          />
          {getFieldError('title') && (
            <p id="title-error" className="mt-1 text-sm text-red-400" role="alert">
              {getFieldError('title')}
            </p>
          )}
          <div className="mt-1 text-xs text-gray-500">
            {metadata.title.length}/100 characters
          </div>
        </div>

        {/* Artist */}
        <div>
          <label htmlFor="artist" className="block text-sm font-medium text-gray-300 mb-2">
            Artist Name
          </label>
          <div className="relative">
            <User className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              id="artist"
              type="text"
              value={metadata.artist}
              onChange={(e) => updateMetadata('artist', e.target.value)}
              onBlur={() => handleFieldBlur('artist')}
              placeholder="Artist or creator name..."
              disabled={isSubmitting}
              className={`w-full pl-10 pr-4 py-3 bg-gray-800 border rounded-lg 
                       focus:ring-2 focus:ring-purple-500 focus:border-transparent
                       text-white placeholder-gray-400 disabled:opacity-50 disabled:cursor-not-allowed
                       transition-colors ${
                         getFieldError('artist') ? 'border-red-500' : 'border-gray-700'
                       }`}
              maxLength={50}
              aria-invalid={!!getFieldError('artist')}
              aria-describedby={getFieldError('artist') ? 'artist-error' : undefined}
            />
          </div>
          {getFieldError('artist') && (
            <p id="artist-error" className="mt-1 text-sm text-red-400" role="alert">
              {getFieldError('artist')}
            </p>
          )}
        </div>

        {/* AI Tool */}
        <div>
          <label htmlFor="aiTool" className="block text-sm font-medium text-gray-300 mb-2">
            AI Tool Used *
          </label>
          <div className="relative">
            <Sparkles className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2 z-10" />
            <select
              id="aiTool"
              value={metadata.aiTool}
              onChange={(e) => updateMetadata('aiTool', e.target.value)}
              onBlur={() => handleFieldBlur('aiTool')}
              disabled={isSubmitting}
              className={`w-full pl-10 pr-4 py-3 bg-gray-800 border rounded-lg 
                       focus:ring-2 focus:ring-purple-500 focus:border-transparent
                       text-white disabled:opacity-50 disabled:cursor-not-allowed
                       transition-colors appearance-none cursor-pointer ${
                         getFieldError('aiTool') ? 'border-red-500' : 'border-gray-700'
                       }`}
              aria-invalid={!!getFieldError('aiTool')}
              aria-describedby={getFieldError('aiTool') ? 'ai-tool-error' : undefined}
              required
            >
              <option value="">Select AI tool...</option>
              {Object.entries(groupedTools).map(([category, tools]) => (
                <optgroup key={category} label={category}>
                  {tools.map(tool => (
                    <option key={tool.id} value={tool.id}>
                      {tool.name}
                    </option>
                  ))}
                </optgroup>
              ))}
            </select>
          </div>
          {getFieldError('aiTool') && (
            <p id="ai-tool-error" className="mt-1 text-sm text-red-400" role="alert">
              {getFieldError('aiTool')}
            </p>
          )}
          {metadata.aiTool && (
            <div className="mt-2 p-2 bg-blue-500/10 border border-blue-500/20 rounded text-xs text-blue-400 flex items-start gap-2">
              <Info className="w-3 h-3 mt-0.5 flex-shrink-0" />
              <span>{AI_TOOLS.find(tool => tool.id === metadata.aiTool)?.description}</span>
            </div>
          )}
        </div>

        {/* Genre */}
        <div>
          <label htmlFor="genre" className="block text-sm font-medium text-gray-300 mb-2">
            Genre *
          </label>
          <div className="relative">
            <Hash className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2 z-10" />
            <select
              id="genre"
              value={metadata.genre}
              onChange={(e) => updateMetadata('genre', e.target.value)}
              onBlur={() => handleFieldBlur('genre')}
              disabled={isSubmitting}
              className={`w-full pl-10 pr-4 py-3 bg-gray-800 border rounded-lg 
                       focus:ring-2 focus:ring-purple-500 focus:border-transparent
                       text-white disabled:opacity-50 disabled:cursor-not-allowed
                       transition-colors appearance-none cursor-pointer ${
                         getFieldError('genre') ? 'border-red-500' : 'border-gray-700'
                       }`}
              aria-invalid={!!getFieldError('genre')}
              aria-describedby={getFieldError('genre') ? 'genre-error' : undefined}
              required
            >
              <option value="">Select genre...</option>
              <optgroup label="Popular Genres">
                {popularGenres.map(genre => (
                  <option key={genre.name} value={genre.name.toLowerCase()}>
                    {genre.name}
                  </option>
                ))}
              </optgroup>
              <optgroup label="Other Genres">
                {otherGenres.map(genre => (
                  <option key={genre.name} value={genre.name.toLowerCase()}>
                    {genre.name}
                  </option>
                ))}
              </optgroup>
            </select>
          </div>
          {getFieldError('genre') && (
            <p id="genre-error" className="mt-1 text-sm text-red-400" role="alert">
              {getFieldError('genre')}
            </p>
          )}
        </div>

        {/* Privacy Toggle */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-3">
            Visibility
          </label>
          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={() => updateMetadata('isPublic', !metadata.isPublic)}
              disabled={isSubmitting}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 ${
                metadata.isPublic ? 'bg-purple-600' : 'bg-gray-600'
              }`}
              role="switch"
              aria-checked={metadata.isPublic}
              aria-label="Toggle track visibility"
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  metadata.isPublic ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className="text-sm text-gray-300">
              {metadata.isPublic ? 'Public' : 'Private'}
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {metadata.isPublic 
              ? 'Anyone can discover and listen to this track'
              : 'Only you can access this track'
            }
          </p>
        </div>
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
          Description
        </label>
        <textarea
          id="description"
          value={metadata.description}
          onChange={(e) => updateMetadata('description', e.target.value)}
          onBlur={() => handleFieldBlur('description')}
          placeholder="Describe your track, the prompt used, or creative process..."
          disabled={isSubmitting}
          rows={4}
          className={`w-full px-4 py-3 bg-gray-800 border rounded-lg 
                   focus:ring-2 focus:ring-purple-500 focus:border-transparent
                   text-white placeholder-gray-400 disabled:opacity-50 disabled:cursor-not-allowed
                   transition-colors resize-none ${
                     getFieldError('description') ? 'border-red-500' : 'border-gray-700'
                   }`}
          maxLength={500}
          aria-invalid={!!getFieldError('description')}
          aria-describedby={getFieldError('description') ? 'description-error' : undefined}
        />
        {getFieldError('description') && (
          <p id="description-error" className="mt-1 text-sm text-red-400" role="alert">
            {getFieldError('description')}
          </p>
        )}
        <div className="mt-1 text-xs text-gray-500">
          {metadata.description.length}/500 characters
        </div>
      </div>

      {/* Tags */}
      <div>
        <label htmlFor="tags" className="block text-sm font-medium text-gray-300 mb-2">
          Tags
        </label>
        <div className="space-y-3">
          {/* Tag Input */}
          <div className="relative">
            <Tag className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              id="tags"
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={handleAddTag}
              placeholder="Add tags (press Enter or comma to add)..."
              disabled={isSubmitting || metadata.tags.length >= 10}
              className={`w-full pl-10 pr-4 py-3 bg-gray-800 border rounded-lg 
                       focus:ring-2 focus:ring-purple-500 focus:border-transparent
                       text-white placeholder-gray-400 disabled:opacity-50 disabled:cursor-not-allowed
                       transition-colors ${
                         formErrors.tags ? 'border-red-500' : 'border-gray-700'
                       }`}
              maxLength={20}
              aria-invalid={!!formErrors.tags}
              aria-describedby={formErrors.tags ? 'tags-error' : 'tags-help'}
            />
          </div>

          {/* Tag Error */}
          {formErrors.tags && (
            <p id="tags-error" className="text-sm text-red-400" role="alert">
              {formErrors.tags}
            </p>
          )}

          {/* Tag Help */}
          <p id="tags-help" className="text-xs text-gray-500">
            Add up to 10 tags to help others discover your music. Max 20 characters per tag.
          </p>

          {/* Tag Display */}
          {metadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {metadata.tags.map((tag, index) => (
                <span
                  key={`${tag}-${index}`}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-purple-600/20 border border-purple-600/30 rounded-full text-sm text-purple-300"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    disabled={isSubmitting}
                    className="hover:text-purple-100 transition-colors focus:outline-none focus:ring-1 focus:ring-purple-500 rounded-full"
                    aria-label={`Remove tag ${tag}`}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Form Validation Summary */}
      {touched.title && !isFormValid && (
        <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-yellow-400 font-medium text-sm">Complete Required Fields</h4>
              <p className="text-yellow-400/80 text-xs mt-1">
                Please fill in all required fields (marked with *) to continue.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 