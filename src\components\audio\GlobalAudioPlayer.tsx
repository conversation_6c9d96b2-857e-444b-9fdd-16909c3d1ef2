'use client'

import { useRef, useEffect } from 'react'
import { useAudio } from '@/contexts/AudioContext'

export default function GlobalAudioPlayer() {
  const audioRef = useRef<HTMLAudioElement>(null)
  const {
    currentTrack,
    isPlaying,
    volume,
    isMuted,
    currentTime,
    togglePlayPause,
    nextTrack,
    seek,
    setVolume,
    setCurrentTime,
    setDuration,
    setLoading
  } = useAudio()

  // Handle play/pause changes
  useEffect(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.play().catch(console.error)
    } else {
      audioRef.current.pause()
    }
  }, [isPlaying])

  // Handle track changes
  useEffect(() => {
    if (!audioRef.current || !currentTrack) return

    const audio = audioRef.current
    
    // Load new track if different
    if (audio.src !== currentTrack.src) {
      setLoading(true)
      audio.pause()
      audio.src = currentTrack.src
      audio.load()
      
      if (isPlaying) {
        audio.play().catch(console.error)
      }
    }
  }, [currentTrack, isPlaying, setLoading])

  // Handle volume changes
  useEffect(() => {
    if (!audioRef.current) return
    audioRef.current.volume = isMuted ? 0 : volume
  }, [volume, isMuted])

  // Handle seek changes - sync with audio element when seek is called from context
  useEffect(() => {
    if (!audioRef.current) return
    const audio = audioRef.current
    
    // Only update if there's a significant difference to avoid loops
    if (Math.abs(audio.currentTime - currentTime) > 1) {
      audio.currentTime = currentTime
    }
  }, [currentTime])

  // Audio event handlers
  const handleTimeUpdate = () => {
    if (!audioRef.current) return
    const audio = audioRef.current
    setCurrentTime(audio.currentTime)
  }

  const handleEnded = () => {
    nextTrack()
  }

  const handleLoadedMetadata = () => {
    if (!audioRef.current) return
    const audio = audioRef.current
    setDuration(audio.duration)
    setLoading(false)
  }

  const handleCanPlay = () => {
    setLoading(false)
  }

  const handleLoadStart = () => {
    setLoading(true)
  }

  const handleError = (e: any) => {
    console.error('Audio playback error:', e)
    setLoading(false)
  }

  return (
    <audio
      ref={audioRef}
      onTimeUpdate={handleTimeUpdate}
      onEnded={handleEnded}
      onLoadedMetadata={handleLoadedMetadata}
      onCanPlay={handleCanPlay}
      onLoadStart={handleLoadStart}
      onError={handleError}
      style={{ display: 'none' }}
    />
  )
} 