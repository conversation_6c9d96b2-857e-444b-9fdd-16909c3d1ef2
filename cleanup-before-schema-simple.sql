-- Simplified Cleanup Script: Run this BEFORE the main schema
-- Removes existing elements without referencing non-existent tables

-- =============================================
-- 1. DROP STORAGE POLICIES (if they exist)
-- =============================================

-- Drop storage policies without failing if they don't exist
DO $$
BEGIN
    -- Drop storage policies for audio-tracks bucket
    DROP POLICY IF EXISTS "Audio files are publicly viewable" ON storage.objects;
    DROP POLICY IF EXISTS "Users can upload their own audio files" ON storage.objects;
    DROP POLICY IF EXISTS "Users can update their own audio files" ON storage.objects;
    DROP POLICY IF EXISTS "Users can delete their own audio files" ON storage.objects;
    
    -- Drop storage policies for playlist-covers bucket
    DROP POLICY IF EXISTS "Playlist covers are publicly viewable" ON storage.objects;
    DROP POLICY IF EXISTS "Users can upload playlist covers" ON storage.objects;
    
    -- Drop storage policies for profile-avatars bucket
    DROP POLICY IF EXISTS "Profile avatars are publicly viewable" ON storage.objects;
    DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
    
    RAISE NOTICE 'Storage policies cleaned up';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Some storage policies did not exist - continuing...';
END
$$;

-- =============================================
-- 2. DROP EXISTING TABLES (CASCADE removes triggers/functions automatically)
-- =============================================

-- Drop tables in dependency order - CASCADE will remove associated triggers and functions
DROP TABLE IF EXISTS listening_history CASCADE;
DROP TABLE IF EXISTS ai_generation_jobs CASCADE;
DROP TABLE IF EXISTS track_likes CASCADE;
DROP TABLE IF EXISTS user_agreements CASCADE;
DROP TABLE IF EXISTS playlist_tracks CASCADE;
DROP TABLE IF EXISTS playlists CASCADE;
DROP TABLE IF EXISTS tracks CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;

-- =============================================
-- 3. DROP EXISTING VIEWS
-- =============================================

DROP VIEW IF EXISTS popular_tracks CASCADE;
DROP VIEW IF EXISTS user_playlists_with_stats CASCADE;

-- =============================================
-- 4. DROP EXISTING FUNCTIONS (manually clean remaining ones)
-- =============================================

DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS update_track_like_count() CASCADE;
DROP FUNCTION IF EXISTS update_playlist_stats() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- =============================================
-- 5. DROP EXISTING TYPES
-- =============================================

DROP TYPE IF EXISTS agreement_version_type CASCADE;
DROP TYPE IF EXISTS mood_type CASCADE;
DROP TYPE IF EXISTS genre_type CASCADE;
DROP TYPE IF EXISTS ai_tool_type CASCADE;

-- =============================================
-- 6. CLEAN STORAGE BUCKETS
-- =============================================

-- Clean storage buckets safely
DO $$
BEGIN
    -- Delete all objects from buckets first
    DELETE FROM storage.objects WHERE bucket_id IN ('audio-tracks', 'playlist-covers', 'profile-avatars');
    
    -- Then delete the buckets themselves
    DELETE FROM storage.buckets WHERE id IN ('audio-tracks', 'playlist-covers', 'profile-avatars');
    
    RAISE NOTICE 'Storage buckets cleaned up';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Some storage buckets did not exist - continuing...';
END
$$;

-- =============================================
-- CLEANUP COMPLETE
-- =============================================

-- Success message
DO $$
BEGIN
    RAISE NOTICE '=================================';
    RAISE NOTICE 'SIMPLIFIED CLEANUP COMPLETED!';
    RAISE NOTICE '=================================';
    RAISE NOTICE 'Removed: tables, views, types, functions, storage buckets, and policies';
    RAISE NOTICE 'Used CASCADE to automatically remove triggers and dependencies';
    RAISE NOTICE 'Ready to run the main schema without conflicts!';
    RAISE NOTICE '=================================';
END
$$; 