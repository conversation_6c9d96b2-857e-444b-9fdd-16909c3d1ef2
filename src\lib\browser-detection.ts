// Browser detection and feature support utilities
export interface BrowserInfo {
  name: string
  version: string
  engine: string
  platform: string
  isMobile: boolean
  isDesktop: boolean
  isTablet: boolean
  supportsAudio: boolean
  audioFormats: {
    mp3: boolean
    wav: boolean
    flac: boolean
    ogg: boolean
    aac: boolean
    opus: boolean
  }
  features: {
    serviceWorker: boolean
    webAudio: boolean
    mediaSession: boolean
    pictureInPicture: boolean
    fullscreen: boolean
    notifications: boolean
    geolocation: boolean
    localStorage: boolean
    indexedDB: boolean
    webGL: boolean
    touch: boolean
    pointerEvents: boolean
    intersectionObserver: boolean
    mutationObserver: boolean
    resizeObserver: boolean
    css: {
      grid: boolean
      flexbox: boolean
      customProperties: boolean
      backdropFilter: boolean
      animations: boolean
      transforms3d: boolean
    }
  }
}

class BrowserDetector {
  private browserInfo: BrowserInfo | null = null

  detect(): BrowserInfo {
    if (this.browserInfo) return this.browserInfo

    if (typeof window === 'undefined') {
      // Server-side fallback
      return this.getServerSideFallback()
    }

    const userAgent = navigator.userAgent
    const platform = navigator.platform
    
    this.browserInfo = {
      name: this.detectBrowserName(userAgent),
      version: this.detectBrowserVersion(userAgent),
      engine: this.detectEngine(userAgent),
      platform: this.detectPlatform(platform, userAgent),
      isMobile: this.isMobileDevice(userAgent),
      isDesktop: this.isDesktopDevice(userAgent),
      isTablet: this.isTabletDevice(userAgent),
      supportsAudio: this.detectAudioSupport(),
      audioFormats: this.detectAudioFormats(),
      features: this.detectFeatures()
    }

    return this.browserInfo
  }

  private detectBrowserName(userAgent: string): string {
    if (userAgent.includes('Firefox')) return 'Firefox'
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari'
    if (userAgent.includes('Chrome')) return 'Chrome'
    if (userAgent.includes('Edge')) return 'Edge'
    if (userAgent.includes('Opera')) return 'Opera'
    return 'Unknown'
  }

  private detectBrowserVersion(userAgent: string): string {
    const patterns = {
      Firefox: /Firefox\/(\d+\.\d+)/,
      Safari: /Version\/(\d+\.\d+)/,
      Chrome: /Chrome\/(\d+\.\d+)/,
      Edge: /Edg\/(\d+\.\d+)/,
      Opera: /Opera\/(\d+\.\d+)/
    }

    for (const [browser, pattern] of Object.entries(patterns)) {
      if (userAgent.includes(browser) || (browser === 'Safari' && userAgent.includes('Safari'))) {
        const match = userAgent.match(pattern)
        if (match) return match[1]
      }
    }

    return 'Unknown'
  }

  private detectEngine(userAgent: string): string {
    if (userAgent.includes('Gecko') && userAgent.includes('Firefox')) return 'Gecko'
    if (userAgent.includes('WebKit') && userAgent.includes('Safari')) return 'WebKit'
    if (userAgent.includes('Blink') || userAgent.includes('Chrome')) return 'Blink'
    if (userAgent.includes('EdgeHTML')) return 'EdgeHTML'
    return 'Unknown'
  }

  private detectPlatform(platform: string, userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows'
    if (userAgent.includes('Macintosh')) return 'macOS'
    if (userAgent.includes('Linux')) return 'Linux'
    if (userAgent.includes('Android')) return 'Android'
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) return 'iOS'
    return platform || 'Unknown'
  }

  private isMobileDevice(userAgent: string): boolean {
    return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
  }

  private isDesktopDevice(userAgent: string): boolean {
    return !this.isMobileDevice(userAgent) && !this.isTabletDevice(userAgent)
  }

  private isTabletDevice(userAgent: string): boolean {
    return /iPad|Android.*Tablet|Windows.*Touch/i.test(userAgent)
  }

  private detectAudioSupport(): boolean {
    try {
      return !!(document.createElement('audio').canPlayType)
    } catch {
      return false
    }
  }

  private detectAudioFormats() {
    const audio = document.createElement('audio')
    return {
      mp3: audio.canPlayType('audio/mpeg') !== '',
      wav: audio.canPlayType('audio/wav') !== '',
      flac: audio.canPlayType('audio/flac') !== '',
      ogg: audio.canPlayType('audio/ogg') !== '',
      aac: audio.canPlayType('audio/aac') !== '',
      opus: audio.canPlayType('audio/ogg; codecs="opus"') !== ''
    }
  }

  private detectFeatures() {
    return {
      serviceWorker: 'serviceWorker' in navigator,
      webAudio: !!(window.AudioContext || window.webkitAudioContext),
      mediaSession: 'mediaSession' in navigator,
      pictureInPicture: 'pictureInPictureEnabled' in document,
      fullscreen: !!(
        document.documentElement.requestFullscreen ||
        document.documentElement.webkitRequestFullscreen ||
        document.documentElement.mozRequestFullScreen
      ),
      notifications: 'Notification' in window,
      geolocation: 'geolocation' in navigator,
      localStorage: this.testLocalStorage(),
      indexedDB: 'indexedDB' in window,
      webGL: this.testWebGL(),
      touch: 'ontouchstart' in window,
      pointerEvents: 'onpointerdown' in window,
      intersectionObserver: 'IntersectionObserver' in window,
      mutationObserver: 'MutationObserver' in window,
      resizeObserver: 'ResizeObserver' in window,
      css: this.detectCSSFeatures()
    }
  }

  private testLocalStorage(): boolean {
    try {
      localStorage.setItem('test', 'test')
      localStorage.removeItem('test')
      return true
    } catch {
      return false
    }
  }

  private testWebGL(): boolean {
    try {
      const canvas = document.createElement('canvas')
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    } catch {
      return false
    }
  }

  private detectCSSFeatures() {
    const testCSS = (property: string, value: string) => {
      try {
        return CSS.supports(property, value)
      } catch {
        return false
      }
    }

    return {
      grid: testCSS('display', 'grid'),
      flexbox: testCSS('display', 'flex'),
      customProperties: testCSS('color', 'var(--test)'),
      backdropFilter: testCSS('backdrop-filter', 'blur(10px)'),
      animations: testCSS('animation', 'test 1s'),
      transforms3d: testCSS('transform', 'translateZ(0)')
    }
  }

  private getServerSideFallback(): BrowserInfo {
    return {
      name: 'Unknown',
      version: 'Unknown',
      engine: 'Unknown',
      platform: 'Unknown',
      isMobile: false,
      isDesktop: true,
      isTablet: false,
      supportsAudio: true, // Assume support on server-side
      audioFormats: {
        mp3: true,
        wav: true,
        flac: false,
        ogg: false,
        aac: false,
        opus: false
      },
      features: {
        serviceWorker: false,
        webAudio: false,
        mediaSession: false,
        pictureInPicture: false,
        fullscreen: false,
        notifications: false,
        geolocation: false,
        localStorage: false,
        indexedDB: false,
        webGL: false,
        touch: false,
        pointerEvents: false,
        intersectionObserver: false,
        mutationObserver: false,
        resizeObserver: false,
        css: {
          grid: true,
          flexbox: true,
          customProperties: true,
          backdropFilter: false,
          animations: true,
          transforms3d: true
        }
      }
    }
  }
}

// Browser-specific fixes and workarounds
export class BrowserFixes {
  private browserInfo: BrowserInfo

  constructor(browserInfo: BrowserInfo) {
    this.browserInfo = browserInfo
  }

  applyAudioFixes(): void {
    // Safari iOS audio fixes
    if (this.browserInfo.name === 'Safari' && this.browserInfo.platform === 'iOS') {
      this.applySafariIOSAudioFixes()
    }

    // Chrome autoplay fixes
    if (this.browserInfo.name === 'Chrome') {
      this.applyChromeAutoplayFixes()
    }

    // Firefox audio context fixes
    if (this.browserInfo.name === 'Firefox') {
      this.applyFirefoxAudioFixes()
    }
  }

  private applySafariIOSAudioFixes(): void {
    // Enable inline audio playback for iOS Safari
    document.addEventListener('DOMContentLoaded', () => {
      const audioElements = document.querySelectorAll('audio')
      audioElements.forEach(audio => {
        audio.setAttribute('playsinline', '')
        audio.setAttribute('webkit-playsinline', '')
      })
    })

    // Handle iOS Safari audio unlock
    let unlocked = false
    const unlock = () => {
      if (unlocked) return
      
      const audio = new Audio()
      audio.play().then(() => {
        unlocked = true
        document.removeEventListener('touchstart', unlock)
        document.removeEventListener('touchend', unlock)
      }).catch(() => {
        // Ignore unlock failures
      })
    }

    document.addEventListener('touchstart', unlock, { once: true })
    document.addEventListener('touchend', unlock, { once: true })
  }

  private applyChromeAutoplayFixes(): void {
    // Handle Chrome's autoplay policy
    document.addEventListener('DOMContentLoaded', () => {
      const handleUserGesture = () => {
        // Try to resume any suspended audio contexts
        if (window.AudioContext || window.webkitAudioContext) {
          const AudioContext = window.AudioContext || window.webkitAudioContext
          const contexts = (window as any).audioContexts || []
          
          contexts.forEach((context: AudioContext) => {
            if (context.state === 'suspended') {
              context.resume()
            }
          })
        }

        document.removeEventListener('click', handleUserGesture)
        document.removeEventListener('keydown', handleUserGesture)
      }

      document.addEventListener('click', handleUserGesture, { once: true })
      document.addEventListener('keydown', handleUserGesture, { once: true })
    })
  }

  private applyFirefoxAudioFixes(): void {
    // Firefox-specific audio optimizations
    if (this.browserInfo.features.webAudio) {
      // Optimize audio context for Firefox
      document.addEventListener('DOMContentLoaded', () => {
        const AudioContext = window.AudioContext || window.webkitAudioContext
        if (AudioContext) {
          const originalCreateContext = AudioContext
          ;(window as any).AudioContext = function(...args: any[]) {
            const context = new originalCreateContext(...args)
            
            // Firefox optimization: set lower latency hint
            if (context.audioWorklet) {
              context.audioWorklet.addModule('/audio-worklet-processor.js').catch(() => {
                // Ignore if worklet module doesn't exist
              })
            }
            
            return context
          }
        }
      })
    }
  }

  applyCSSFixes(): void {
    // Apply CSS fixes based on browser capabilities
    const style = document.createElement('style')
    let css = ''

    // Grid fallbacks
    if (!this.browserInfo.features.css.grid) {
      css += `
        .grid-fallback {
          display: flex;
          flex-wrap: wrap;
        }
      `
    }

    // Custom properties fallbacks
    if (!this.browserInfo.features.css.customProperties) {
      css += `
        :root {
          /* Fallback colors for older browsers */
        }
        .primary-color { color: #8b5cf6; }
        .background-color { background-color: #0f172a; }
      `
    }

    // Backdrop filter fallbacks
    if (!this.browserInfo.features.css.backdropFilter) {
      css += `
        .backdrop-blur-fallback {
          background-color: rgba(15, 23, 42, 0.8);
        }
      `
    }

    if (css) {
      style.textContent = css
      document.head.appendChild(style)
    }
  }
}

// Singleton instance
export const browserDetector = new BrowserDetector()

// Convenience functions
export const getBrowserInfo = () => browserDetector.detect()
export const browserFixes = new BrowserFixes(getBrowserInfo())

// Browser check functions
export const isChrome = () => getBrowserInfo().name === 'Chrome'
export const isFirefox = () => getBrowserInfo().name === 'Firefox'
export const isSafari = () => getBrowserInfo().name === 'Safari'
export const isEdge = () => getBrowserInfo().name === 'Edge'
export const isMobile = () => getBrowserInfo().isMobile
export const isIOS = () => getBrowserInfo().platform === 'iOS'
export const isAndroid = () => getBrowserInfo().platform === 'Android'

// Feature check functions
export const supportsWebAudio = () => getBrowserInfo().features.webAudio
export const supportsServiceWorker = () => getBrowserInfo().features.serviceWorker
export const supportsMediaSession = () => getBrowserInfo().features.mediaSession
export const supportsTouchEvents = () => getBrowserInfo().features.touch

// Audio format check functions
export const supportsMP3 = () => getBrowserInfo().audioFormats.mp3
export const supportsOGG = () => getBrowserInfo().audioFormats.ogg
export const supportsFLAC = () => getBrowserInfo().audioFormats.flac
export const supportsAAC = () => getBrowserInfo().audioFormats.aac

export default browserDetector 