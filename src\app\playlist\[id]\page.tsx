'use client'

import React, { useEffect, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import Image from 'next/image'
import { 
  Play, 
  Pause, 
  Shuffle, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Share2,
  Clock,
  Music,
  Lock,
  Globe,
  ArrowLeft
} from 'lucide-react'
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd'
import { usePlaylist } from '@/contexts/PlaylistContext'
import { useAuth } from '@/contexts/AuthContext'
import { useAudio } from '@/contexts/AudioContext'
import PlaylistModal from '@/components/playlists/PlaylistModal'
import DeletePlaylistModal from '@/components/playlists/DeletePlaylistModal'
import PlaylistTrackItem from '@/components/playlists/PlaylistTrackItem'
import { formatDuration, formatDate } from '@/utils/format'
import { ReorderTrackData } from '@/types/playlist'

export default function PlaylistDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const { 
    currentPlaylist, 
    getPlaylist, 
    reorderPlaylistTracks,
    loading, 
    error 
  } = usePlaylist()
  const { 
    playPlaylist, 
    currentPlaylist: audioCurrentPlaylist, 
    isPlaying: audioIsPlaying,
    currentTrack 
  } = useAudio()

  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showMenu, setShowMenu] = useState(false)
  const [imageError, setImageError] = useState(false)

  const playlistId = params.id as string

  useEffect(() => {
    if (playlistId) {
      getPlaylist(playlistId)
    }
  }, [playlistId, getPlaylist])

  const handleDragEnd = async (result: any) => {
    if (!result.destination || !currentPlaylist) return

    const { source, destination } = result
    if (source.index === destination.index) return

    // Create a copy of tracks array
    const tracks = Array.from(currentPlaylist.tracks)
    const [reorderedTrack] = tracks.splice(source.index, 1)
    tracks.splice(destination.index, 0, reorderedTrack)

    // Update positions
    const reorderData: ReorderTrackData[] = tracks.map((track, index) => ({
      playlist_id: currentPlaylist.id,
      track_id: track.track_id,
      new_position: index + 1
    }))

    try {
      await reorderPlaylistTracks(currentPlaylist.id, reorderData)
    } catch (error) {
      console.error('Failed to reorder tracks:', error)
    }
  }

  const handlePlay = () => {
    if (!currentPlaylist) return
    
    // Check if this playlist is currently playing
    const isCurrentPlaylistPlaying = audioCurrentPlaylist?.id === currentPlaylist.id && audioIsPlaying
    
    if (isCurrentPlaylistPlaying) {
      // If already playing this playlist, we would pause (handled by play button)
      return
    }
    
    // Start playing the playlist from the beginning
    playPlaylist(currentPlaylist, 0)
  }

  const handleShuffle = () => {
    if (!currentPlaylist) return
    
    // Play playlist and then shuffle (shuffle will be handled by the audio context)
    playPlaylist(currentPlaylist, 0)
    // Note: We could add a shuffle action to the audio context if needed
  }

  const handleShare = async () => {
    if (!currentPlaylist) return

    if (navigator.share && currentPlaylist.is_public) {
      try {
        await navigator.share({
          title: currentPlaylist.name,
          text: currentPlaylist.description || `Check out this playlist: ${currentPlaylist.name}`,
          url: window.location.href
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      await navigator.clipboard.writeText(window.location.href)
      // TODO: Show toast notification
    }
  }

  const isOwner = !!(user && currentPlaylist && user.id === currentPlaylist.user_id)
  
  // Check if this playlist is currently playing
  const isCurrentPlaylistPlaying = audioCurrentPlaylist?.id === currentPlaylist?.id && audioIsPlaying

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !currentPlaylist) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Playlist not found
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {error || 'The playlist you\'re looking for doesn\'t exist or you don\'t have permission to view it.'}
          </p>
          <button
            onClick={() => router.back()}
            className="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Go Back</span>
          </button>
        </div>
      </div>
    )
  }

  const totalDuration = currentPlaylist.tracks.reduce((sum, track) => 
    sum + (track.track?.duration || 0), 0
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <button
          onClick={() => router.back()}
          className="inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white mb-6 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back</span>
        </button>

        {/* Playlist Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Cover Image */}
            <div className="w-48 h-48 mx-auto md:mx-0 flex-shrink-0">
              <div className="relative w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg overflow-hidden">
                {currentPlaylist.cover_image_url && !imageError ? (
                  <Image
                    src={currentPlaylist.cover_image_url}
                    alt={currentPlaylist.name}
                    fill
                    className="object-cover"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Music className="w-16 h-16 text-white opacity-80" />
                  </div>
                )}
              </div>
            </div>

            {/* Playlist Info */}
            <div className="flex-1 text-center md:text-left">
              <div className="flex items-center justify-center md:justify-start space-x-2 mb-2">
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">PLAYLIST</span>
                {currentPlaylist.is_public ? (
                  <Globe className="w-4 h-4 text-green-500" />
                ) : (
                  <Lock className="w-4 h-4 text-gray-500" />
                )}
              </div>

              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {currentPlaylist.name}
              </h1>

              {currentPlaylist.description && (
                <p className="text-gray-600 dark:text-gray-400 mb-4 max-w-2xl">
                  {currentPlaylist.description}
                </p>
              )}

              <div className="flex flex-wrap items-center justify-center md:justify-start gap-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
                <span>{currentPlaylist.tracks.length} tracks</span>
                {totalDuration > 0 && (
                  <>
                    <span>•</span>
                    <span>{formatDuration(totalDuration)}</span>
                  </>
                )}
                <span>•</span>
                <span>Updated {formatDate(currentPlaylist.updated_at)}</span>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-center md:justify-start space-x-3">
                <button
                  onClick={handlePlay}
                  className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors"
                >
                  {isCurrentPlaylistPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5 ml-1" />}
                  <span>{isCurrentPlaylistPlaying ? 'Playing' : 'Play'}</span>
                </button>

                <button
                  onClick={handleShuffle}
                  className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white px-4 py-3 rounded-lg transition-colors"
                >
                  <Shuffle className="w-5 h-5" />
                  <span>Shuffle</span>
                </button>

                {/* Menu */}
                <div className="relative">
                  <button
                    onClick={() => setShowMenu(!showMenu)}
                    className="p-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg transition-colors"
                  >
                    <MoreVertical className="w-5 h-5" />
                  </button>

                  {showMenu && (
                    <div className="absolute top-12 right-0 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 py-1 z-10 min-w-[160px]">
                      {isOwner && (
                        <button
                          onClick={() => {
                            setShowMenu(false)
                            setShowEditModal(true)
                          }}
                          className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-2"
                        >
                          <Edit className="w-4 h-4" />
                          <span>Edit</span>
                        </button>
                      )}
                      
                      <button
                        onClick={() => {
                          setShowMenu(false)
                          handleShare()
                        }}
                        className="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-2"
                      >
                        <Share2 className="w-4 h-4" />
                        <span>Share</span>
                      </button>
                      
                      {isOwner && (
                        <>
                          <hr className="my-1 border-gray-200 dark:border-gray-600" />
                          <button
                            onClick={() => {
                              setShowMenu(false)
                              setShowDeleteModal(true)
                            }}
                            className="w-full px-3 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2"
                          >
                            <Trash2 className="w-4 h-4" />
                            <span>Delete</span>
                          </button>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tracks List */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          {currentPlaylist.tracks.length === 0 ? (
            <div className="text-center py-12">
              <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No tracks in this playlist
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Add some tracks to get started
              </p>
            </div>
          ) : (
            <>
              {/* Header */}
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                  <div className="col-span-1">#</div>
                  <div className="col-span-6">Title</div>
                  <div className="col-span-3">Album</div>
                  <div className="col-span-2 text-right">
                    <Clock className="w-4 h-4 inline" />
                  </div>
                </div>
              </div>

              {/* Tracks */}
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="playlist-tracks" isDropDisabled={!isOwner}>
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      {currentPlaylist.tracks.map((playlistTrack, index) => (
                        <Draggable
                          key={playlistTrack.id}
                          draggableId={playlistTrack.id}
                          index={index}
                          isDragDisabled={!isOwner}
                        >
                          {(provided, snapshot) => (
                            <PlaylistTrackItem
                              playlistTrack={playlistTrack}
                              index={index}
                              isOwner={isOwner}
                              isDragging={snapshot.isDragging}
                              dragHandleProps={provided.dragHandleProps}
                              draggableProps={provided.draggableProps}
                              innerRef={provided.innerRef}
                            />
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      <PlaylistModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        playlist={currentPlaylist}
        mode="edit"
      />

      <DeletePlaylistModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        playlist={currentPlaylist}
      />

      {/* Click outside to close menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  )
} 