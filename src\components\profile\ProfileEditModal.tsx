// Profile Edit Modal Component
'use client'

import { useState, useRef } from 'react'
import Image from 'next/image'
import { ExtendedProfile, ProfileFormData } from '@/types/profile'
import { socialPlatforms, validateSocialUrl } from '@/lib/profile'
import { useProfileImagePreview } from '@/hooks/useProfile'

interface ProfileEditModalProps {
  profile: ExtendedProfile
  isOpen: boolean
  onClose: () => void
  onSave: (data: Partial<ExtendedProfile>, avatarFile?: File) => Promise<void>
  loading?: boolean
}

export default function ProfileEditModal({
  profile,
  isOpen,
  onClose,
  onSave,
  loading = false
}: ProfileEditModalProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { preview, file, handleFileSelect, clearPreview } = useProfileImagePreview()
  
  const [formData, setFormData] = useState<ProfileFormData>({
    full_name: profile.full_name || '',
    username: profile.username || '',
    bio: profile.bio || '',
    location: profile.location || '',
    website_url: profile.website_url || '',
    twitter_url: profile.twitter_url || '',
    instagram_url: profile.instagram_url || '',
    youtube_url: profile.youtube_url || '',
    spotify_url: profile.spotify_url || '',
    soundcloud_url: profile.soundcloud_url || '',
    preferred_genres: profile.preferred_genres || [],
    preferred_moods: profile.preferred_moods || []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const genres = [
    'electronic', 'ambient', 'classical', 'jazz', 'rock', 'pop', 'hip-hop', 'r&b',
    'country', 'folk', 'reggae', 'blues', 'metal', 'punk', 'indie', 'experimental'
  ]

  const moods = [
    'energetic', 'chill', 'focus', 'creative', 'melancholic', 'uplifting',
    'dark', 'nostalgic', 'dreamy', 'intense', 'playful', 'serious'
  ]

  const handleInputChange = (field: keyof ProfileFormData, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // Validate file
      if (!selectedFile.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, avatar: 'Please select an image file' }))
        return
      }
      
      if (selectedFile.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, avatar: 'Image must be less than 5MB' }))
        return
      }
      
      handleFileSelect(selectedFile)
      setErrors(prev => ({ ...prev, avatar: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Validate required fields
    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required'
    }

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'Username can only contain letters, numbers, and underscores'
    }

    // Validate social URLs
    Object.entries(formData).forEach(([key, value]) => {
      if (key.endsWith('_url') && value) {
        const platform = key.replace('_url', '')
        if (!validateSocialUrl(platform, value as string)) {
          newErrors[key] = `Invalid ${platform} URL`
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    try {
      await onSave(formData, file || undefined)
      onClose()
      clearPreview()
    } catch (error) {
      console.error('Save profile error:', error)
    }
  }

  const handleGenreToggle = (genre: string) => {
    const current = formData.preferred_genres
    const updated = current.includes(genre)
      ? current.filter(g => g !== genre)
      : [...current, genre]
    handleInputChange('preferred_genres', updated)
  }

  const handleMoodToggle = (mood: string) => {
    const current = formData.preferred_moods
    const updated = current.includes(mood)
      ? current.filter(m => m !== mood)
      : [...current, mood]
    handleInputChange('preferred_moods', updated)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Edit Profile
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Avatar Upload */}
            <div className="text-center">
              <div className="relative w-24 h-24 mx-auto mb-4">
                {preview || profile.avatar_url ? (
                  <Image
                    src={preview || profile.avatar_url!}
                    alt="Profile"
                    fill
                    className="rounded-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                    <span className="text-white text-xl font-bold">
                      {formData.full_name ? formData.full_name.charAt(0).toUpperCase() : '?'}
                    </span>
                  </div>
                )}
              </div>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
              />
              
              <div className="flex gap-2 justify-center">
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm"
                >
                  Change Photo
                </button>
                
                {(preview || profile.avatar_url) && (
                  <button
                    type="button"
                    onClick={clearPreview}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm"
                  >
                    Remove
                  </button>
                )}
              </div>
              
              {errors.avatar && (
                <p className="text-red-500 text-sm mt-2">{errors.avatar}</p>
              )}
            </div>

            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  value={formData.full_name}
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Your full name"
                />
                {errors.full_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.full_name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Username *
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="your_username"
                />
                {errors.username && (
                  <p className="text-red-500 text-sm mt-1">{errors.username}</p>
                )}
              </div>
            </div>

            {/* Bio */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Bio
              </label>
              <textarea
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Tell us about yourself..."
                maxLength={500}
              />
              <p className="text-sm text-gray-500 mt-1">
                {formData.bio.length}/500 characters
              </p>
            </div>

            {/* Location */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Location
              </label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="City, Country"
              />
            </div>

            {/* Social Links */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Social Links
              </h3>
              <div className="space-y-3">
                {socialPlatforms.map((platform) => (
                  <div key={platform.platform}>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <span className="mr-2">{platform.icon}</span>
                      {platform.platform.charAt(0).toUpperCase() + platform.platform.slice(1)}
                    </label>
                    <input
                      type="url"
                      value={formData[`${platform.platform}_url` as keyof ProfileFormData] as string}
                      onChange={(e) => handleInputChange(`${platform.platform}_url` as keyof ProfileFormData, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      placeholder={`https://${platform.platform}.com/yourprofile`}
                    />
                    {errors[`${platform.platform}_url`] && (
                      <p className="text-red-500 text-sm mt-1">{errors[`${platform.platform}_url`]}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Preferred Genres */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Preferred Genres
              </h3>
              <div className="flex flex-wrap gap-2">
                {genres.map((genre) => (
                  <button
                    key={genre}
                    type="button"
                    onClick={() => handleGenreToggle(genre)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      formData.preferred_genres.includes(genre)
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    {genre}
                  </button>
                ))}
              </div>
            </div>

            {/* Preferred Moods */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Preferred Moods
              </h3>
              <div className="flex flex-wrap gap-2">
                {moods.map((mood) => (
                  <button
                    key={mood}
                    type="button"
                    onClick={() => handleMoodToggle(mood)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      formData.preferred_moods.includes(mood)
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    {mood}
                  </button>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-6">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white py-2 px-4 rounded-lg font-medium transition-colors"
              >
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
} 