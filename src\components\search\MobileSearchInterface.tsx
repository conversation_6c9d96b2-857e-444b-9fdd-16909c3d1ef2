'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Search, 
  X, 
  Mic, 
  Clock, 
  TrendingUp, 
  Filter,
  Music,
  User,
  ListMusic,
  Sparkles,
  ArrowLeft,
  Volume2,
  Heart,
  Share2
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useMobileDetection } from '@/hooks/useMobileDetection'
import { useSwipeGestures } from '@/hooks/useSwipeGestures'
import { SearchService } from '@/lib/search-service'
import { SearchSuggestion, SearchHistory } from '@/types/search'
import { debounce } from '@/utils/debounce'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'

interface MobileSearchInterfaceProps {
  isOpen: boolean
  onClose: () => void
  autoFocus?: boolean
  showFilters?: boolean
  defaultQuery?: string
  placeholder?: string
}

interface SearchFilter {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  active: boolean
}

const searchFilters: SearchFilter[] = [
  { id: 'all', label: 'All', icon: Search, active: true },
  { id: 'tracks', label: 'Tracks', icon: Music, active: false },
  { id: 'artists', label: 'Artists', icon: User, active: false },
  { id: 'playlists', label: 'Playlists', icon: ListMusic, active: false },
  { id: 'ai', label: 'AI Generated', icon: Sparkles, active: false }
]

export default function MobileSearchInterface({
  isOpen,
  onClose,
  autoFocus = true,
  showFilters = true,
  defaultQuery = '',
  placeholder = 'Search music, artists, playlists...'
}: MobileSearchInterfaceProps) {
  const [query, setQuery] = useState(defaultQuery)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])
  const [trendingSearches, setTrendingSearches] = useState<string[]>([])
  const [filters, setFilters] = useState<SearchFilter[]>(searchFilters)
  const [isVoiceActive, setIsVoiceActive] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [showClearHistory, setShowClearHistory] = useState(false)

  const router = useRouter()
  const { user } = useAuth()
  const mobileDetection = useMobileDetection()
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const recognition = useRef<SpeechRecognition | null>(null)

  // Swipe to close
  const swipeHandlers = useSwipeGestures({
    onSwipeDown: onClose,
    threshold: 100,
    enabled: isOpen
  })

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      recognition.current = new SpeechRecognition()
      
      if (recognition.current) {
        recognition.current.continuous = false
        recognition.current.interimResults = false
        recognition.current.lang = 'en-US'

        recognition.current.onstart = () => {
          setIsVoiceActive(true)
          // Add haptic feedback
          if ('vibrate' in navigator) {
            navigator.vibrate(20)
          }
        }

        recognition.current.onresult = (event) => {
          const transcript = event.results[0][0].transcript
          setQuery(transcript)
          handleSearch(transcript)
        }

        recognition.current.onend = () => {
          setIsVoiceActive(false)
        }

        recognition.current.onerror = (event) => {
          console.error('Speech recognition error:', event.error)
          setIsVoiceActive(false)
        }
      }
    }

    return () => {
      if (recognition.current) {
        recognition.current.abort()
      }
    }
  }, [])

  // Load initial data
  useEffect(() => {
    if (isOpen && user) {
      loadSearchHistory()
      loadTrendingSearches()
    }
  }, [isOpen, user])

  // Auto focus
  useEffect(() => {
    if (isOpen && autoFocus && inputRef.current) {
      const timer = setTimeout(() => {
        inputRef.current?.focus()
      }, 300) // Delay to allow animation
      return () => clearTimeout(timer)
    }
  }, [isOpen, autoFocus])

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.trim().length < 2) {
        setSuggestions([])
        return
      }

      setLoading(true)
      try {
        const activeFilter = filters.find(f => f.active)?.id || 'all'
        const results = await SearchService.getSuggestions(searchQuery, activeFilter)
        setSuggestions(results)
      } catch (error) {
        console.error('Error fetching suggestions:', error)
        setSuggestions([])
      } finally {
        setLoading(false)
      }
    }, 300),
    [filters]
  )

  // Handle input changes
  useEffect(() => {
    if (query.trim()) {
      debouncedSearch(query)
    } else {
      setSuggestions([])
    }
  }, [query, debouncedSearch])

  const loadSearchHistory = async () => {
    if (!user) return
    
    try {
      const history = await SearchService.getSearchHistory(user.id)
      setSearchHistory(history.slice(0, 10)) // Limit to 10 recent searches
    } catch (error) {
      console.error('Error loading search history:', error)
    }
  }

  const loadTrendingSearches = async () => {
    try {
      const trending = await SearchService.getTrendingSearches()
      setTrendingSearches(trending.slice(0, 8)) // Limit to 8 trending
    } catch (error) {
      console.error('Error loading trending searches:', error)
    }
  }

  const handleSearch = async (searchQuery?: string) => {
    const finalQuery = searchQuery || query
    if (!finalQuery.trim()) return

    // Save to search history
    if (user) {
      try {
        await SearchService.saveSearchHistory(user.id, finalQuery.trim())
        await loadSearchHistory() // Refresh history
      } catch (error) {
        console.error('Error saving search history:', error)
      }
    }

    // Navigate to search results
    const activeFilter = filters.find(f => f.active)?.id || 'all'
    const searchParams = new URLSearchParams({ 
      q: finalQuery.trim(),
      ...(activeFilter !== 'all' && { type: activeFilter })
    })
    
    router.push(`/search?${searchParams.toString()}`)
    onClose()
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text)
    handleSearch(suggestion.text)
  }

  const handleHistoryClick = (historyItem: SearchHistory) => {
    setQuery(historyItem.query)
    handleSearch(historyItem.query)
  }

  const handleTrendingClick = (trending: string) => {
    setQuery(trending)
    handleSearch(trending)
  }

  const handleFilterChange = (filterId: string) => {
    setFilters(prev => prev.map(filter => ({
      ...filter,
      active: filter.id === filterId
    })))
    
    // Re-trigger search with new filter if query exists
    if (query.trim()) {
      debouncedSearch(query)
    }
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(10)
    }
  }

  const startVoiceSearch = () => {
    if (recognition.current) {
      recognition.current.start()
    }
  }

  const clearInput = () => {
    setQuery('')
    setSuggestions([])
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }

  const clearHistory = async () => {
    if (!user) return
    
    try {
      await SearchService.clearSearchHistory(user.id)
      setSearchHistory([])
      setShowClearHistory(false)
    } catch (error) {
      console.error('Error clearing search history:', error)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    const totalItems = suggestions.length
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev < totalItems - 1 ? prev + 1 : -1))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => (prev > -1 ? prev - 1 : totalItems - 1))
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && suggestions[selectedIndex]) {
          handleSuggestionClick(suggestions[selectedIndex])
        } else {
          handleSearch()
        }
        break
      case 'Escape':
        onClose()
        break
    }
  }

  if (!isOpen) return null

  return (
    <ComponentErrorBoundary>
      <div className="fixed inset-0 z-50 bg-gray-900 md:hidden">
        {/* Header */}
        <div className="sticky top-0 bg-gray-900 border-b border-gray-800 px-4 py-3 safe-area-top">
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="p-2 -ml-2 rounded-full hover:bg-gray-800 transition-colors"
              aria-label="Close search"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>

            <div className="flex-1 relative">
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 pr-24 text-white placeholder-gray-400 text-base focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                style={{ fontSize: '16px' }} // Prevent zoom on iOS
              />
              
              {/* Input actions */}
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                {query && (
                  <button
                    onClick={clearInput}
                    className="p-1.5 rounded-full hover:bg-gray-700 transition-colors"
                    aria-label="Clear search"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
                
                {recognition.current && (
                  <button
                    onClick={startVoiceSearch}
                    disabled={isVoiceActive}
                    className={`p-1.5 rounded-full transition-colors ${
                      isVoiceActive 
                        ? 'bg-red-500 text-white animate-pulse' 
                        : 'hover:bg-gray-700 text-gray-400'
                    }`}
                    aria-label="Voice search"
                  >
                    <Mic className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="flex items-center space-x-2 mt-3 overflow-x-auto pb-2">
              {filters.map((filter) => {
                const Icon = filter.icon
                return (
                  <button
                    key={filter.id}
                    onClick={() => handleFilterChange(filter.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                      filter.active
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{filter.label}</span>
                  </button>
                )
              })}
            </div>
          )}
        </div>

        {/* Content */}
        <div 
          ref={containerRef}
          className="flex-1 overflow-y-auto p-4 safe-area-bottom"
          {...swipeHandlers}
        >
          {/* Loading */}
          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500 border-t-transparent" />
            </div>
          )}

          {/* Suggestions */}
          {suggestions.length > 0 && !loading && (
            <div className="space-y-2 mb-6">
              <h3 className="text-sm font-semibold text-gray-400 px-2">Suggestions</h3>
              <div className="space-y-1">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={suggestion.id}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className={`w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-colors ${
                      selectedIndex === index
                        ? 'bg-purple-600 text-white'
                        : 'hover:bg-gray-800 text-white'
                    }`}
                  >
                    <Search className="w-4 h-4 text-gray-400 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{suggestion.text}</p>
                      {suggestion.type && (
                        <p className="text-xs text-gray-400 capitalize">{suggestion.type}</p>
                      )}
                    </div>
                    {suggestion.trending && (
                      <TrendingUp className="w-4 h-4 text-orange-400 flex-shrink-0" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* No query state */}
          {!query.trim() && !loading && (
            <div className="space-y-6">
              {/* Recent searches */}
              {searchHistory.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-gray-400 px-2">Recent Searches</h3>
                    <button
                      onClick={() => setShowClearHistory(true)}
                      className="text-xs text-purple-400 hover:text-purple-300 px-2"
                    >
                      Clear All
                    </button>
                  </div>
                  <div className="space-y-1">
                    {searchHistory.map((item) => (
                      <button
                        key={item.id}
                        onClick={() => handleHistoryClick(item)}
                        className="w-full flex items-center space-x-3 p-3 rounded-lg text-left hover:bg-gray-800 transition-colors"
                      >
                        <Clock className="w-4 h-4 text-gray-400 flex-shrink-0" />
                        <span className="font-medium text-white truncate">{item.query}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Trending searches */}
              {trendingSearches.length > 0 && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 px-2 mb-3">Trending</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {trendingSearches.map((trending, index) => (
                      <button
                        key={index}
                        onClick={() => handleTrendingClick(trending)}
                        className="flex items-center space-x-2 p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <TrendingUp className="w-4 h-4 text-orange-400 flex-shrink-0" />
                        <span className="text-sm font-medium text-white truncate">{trending}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Quick actions */}
              <div>
                <h3 className="text-sm font-semibold text-gray-400 px-2 mb-3">Quick Actions</h3>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={() => router.push('/browse/genres')}
                    className="flex flex-col items-center p-4 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <Music className="w-6 h-6 text-purple-400 mb-2" />
                    <span className="text-sm font-medium text-white">Browse Genres</span>
                  </button>
                  
                  <button
                    onClick={() => router.push('/ai-tracks')}
                    className="flex flex-col items-center p-4 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <Sparkles className="w-6 h-6 text-blue-400 mb-2" />
                    <span className="text-sm font-medium text-white">AI Tracks</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Clear history confirmation */}
        {showClearHistory && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-sm">
              <h3 className="text-lg font-semibold text-white mb-2">Clear Search History</h3>
              <p className="text-gray-400 mb-6">This will remove all your recent searches. This action cannot be undone.</p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowClearHistory(false)}
                  className="flex-1 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={clearHistory}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Clear
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </ComponentErrorBoundary>
  )
}

// Hook for using mobile search
export function useMobileSearch() {
  const [isOpen, setIsOpen] = useState(false)
  
  const openSearch = () => setIsOpen(true)
  const closeSearch = () => setIsOpen(false)
  const toggleSearch = () => setIsOpen(prev => !prev)
  
  return {
    isOpen,
    openSearch,
    closeSearch,
    toggleSearch
  }
} 