# Session 4: Playlist Database Operations - Implementation Guide

## 🎯 **Overview**

This session focuses on implementing comprehensive playlist database operations with enhanced performance, security, and functionality. The implementation includes advanced Supabase RPC functions, bulk operations, and robust permission management.

## 📋 **Implementation Checklist**

### **Phase 1: Database Functions & Migration**

#### ✅ **Completed:**

- [x] Created `005_playlist_functions.sql` migration
- [x] Implemented RPC functions for efficient operations
- [x] Added bulk track operations support
- [x] Enhanced security with ownership validation
- [x] Optimized queries with proper indexing

#### **RPC Functions Implemented:**

1. `reorder_playlist_tracks()` - Efficient track reordering
2. `add_tracks_to_playlist()` - Bulk track addition
3. `remove_tracks_from_playlist()` - Bulk track removal with gap filling
4. `duplicate_playlist()` - Complete playlist duplication
5. `get_playlist_with_tracks()` - Optimized playlist fetching
6. `get_user_playlists_with_stats()` - User playlists with statistics
7. `search_public_playlists()` - Public playlist search with pagination
8. `can_modify_playlist()` - Permission validation
9. `get_playlists_with_track()` - Find playlists containing specific track

### **Phase 2: Enhanced Service Layer**

#### ✅ **Completed:**

- [x] Created `EnhancedPlaylistService` class
- [x] Implemented all CRUD operations with RPC integration
- [x] Added bulk operations support
- [x] Enhanced error handling and validation
- [x] Implemented permission management
- [x] Added playlist sharing functionality

#### **Key Features:**

- **Bulk Operations**: Add/remove multiple tracks efficiently
- **Permission System**: Granular access control
- **Data Validation**: Client-side validation before API calls
- **Share URLs**: Generate shareable playlist links
- **Batch Updates**: Multiple operations in single transaction

### **Phase 3: Enhanced Context Provider**

#### ✅ **Completed:**

- [x] Created `EnhancedPlaylistContext`
- [x] Implemented state management with useReducer
- [x] Added bulk operation progress tracking
- [x] Enhanced error handling and loading states
- [x] Integrated permission management

#### **Context Features:**

- **Progress Tracking**: Real-time bulk operation progress
- **Permission Caching**: Efficient permission state management
- **Auto-refresh**: Automatic playlist updates after operations
- **Error Recovery**: Graceful error handling with user feedback

## 🚀 **Integration Steps**

### **Step 1: Apply Database Migration**

```bash
# Run the new migration
supabase db push

# Or apply manually in Supabase dashboard
# Copy content from supabase/migrations/005_playlist_functions.sql
```

### **Step 2: Update Layout to Use Enhanced Context**

```typescript
// src/app/layout.tsx
import { EnhancedPlaylistProvider } from "@/contexts/EnhancedPlaylistContext";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} bg-gray-950 text-white`}>
        <CriticalErrorBoundary>
          <ClientErrorHandler />
          <AuthProvider>
            <AudioProvider>
              <EnhancedPlaylistProvider>
                {" "}
                {/* Replace PlaylistProvider */}
                <div className="flex flex-col min-h-screen">
                  {children}
                  <GlobalAudioPlayer />
                  <MobileNavigation />
                </div>
                <Toaster />
              </EnhancedPlaylistProvider>
            </AudioProvider>
          </AuthProvider>
        </CriticalErrorBoundary>
      </body>
    </html>
  );
}
```

### **Step 3: Update Components to Use Enhanced Context**

```typescript
// Update existing components
import { useEnhancedPlaylist } from "@/contexts/EnhancedPlaylistContext";

// Example: Update PlaylistList component
const {
  playlists,
  loading,
  error,
  bulkOperationProgress,
  getUserPlaylists,
  duplicatePlaylist,
} = useEnhancedPlaylist();
```

### **Step 4: Implement Bulk Operations UI**

Create components for:

- Bulk track selection
- Progress indicators
- Batch operation controls
- Permission-based UI rendering

## 🔧 **Advanced Features Implementation**

### **1. Bulk Track Operations**

```typescript
// Add multiple tracks to playlist
const handleBulkAdd = async (trackIds: string[]) => {
  try {
    await addTracksToPlaylist(playlistId, trackIds);
    showSuccessToast(`Added ${trackIds.length} tracks to playlist`);
  } catch (error) {
    showErrorToast("Failed to add tracks");
  }
};

// Remove multiple tracks
const handleBulkRemove = async (trackIds: string[]) => {
  try {
    const removedCount = await removeTracksFromPlaylist(playlistId, trackIds);
    showSuccessToast(`Removed ${removedCount} tracks from playlist`);
  } catch (error) {
    showErrorToast("Failed to remove tracks");
  }
};
```

### **2. Permission-Based UI**

```typescript
const PlaylistActions = ({ playlistId }: { playlistId: string }) => {
  const { currentPermissions } = useEnhancedPlaylist();

  return (
    <div className="flex space-x-2">
      {currentPermissions?.canEdit && (
        <button onClick={handleEdit}>Edit</button>
      )}
      {currentPermissions?.canDelete && (
        <button onClick={handleDelete}>Delete</button>
      )}
      {currentPermissions?.canAddTracks && (
        <button onClick={handleAddTracks}>Add Tracks</button>
      )}
    </div>
  );
};
```

### **3. Progress Tracking**

```typescript
const BulkOperationProgress = () => {
  const { bulkOperationProgress } = useEnhancedPlaylist();

  if (!bulkOperationProgress?.isRunning) return null;

  const progress =
    (bulkOperationProgress.completed / bulkOperationProgress.total) * 100;

  return (
    <div className="fixed bottom-4 right-4 bg-white p-4 rounded-lg shadow-lg">
      <p className="text-sm font-medium">{bulkOperationProgress.operation}</p>
      <div className="w-64 bg-gray-200 rounded-full h-2 mt-2">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      </div>
      <p className="text-xs text-gray-500 mt-1">
        {bulkOperationProgress.completed} of {bulkOperationProgress.total}
      </p>
    </div>
  );
};
```

### **4. Playlist Sharing**

```typescript
const SharePlaylist = ({ playlistId }: { playlistId: string }) => {
  const { generateShareUrl } = useEnhancedPlaylist();

  const handleShare = async () => {
    try {
      const shareData = await generateShareUrl(playlistId);

      if (navigator.share) {
        await navigator.share({
          title: "Check out this playlist",
          url: shareData.share_url,
        });
      } else {
        await navigator.clipboard.writeText(shareData.share_url);
        showSuccessToast("Share link copied to clipboard");
      }
    } catch (error) {
      showErrorToast("Failed to generate share link");
    }
  };

  return <button onClick={handleShare}>Share Playlist</button>;
};
```

## 🔒 **Security Features**

### **1. Row Level Security (RLS)**

- All operations validate user ownership
- Public playlists accessible to all users
- Private playlists restricted to owners

### **2. Permission Validation**

- Server-side permission checks in RPC functions
- Client-side permission caching for UI
- Granular permissions (view, edit, delete, add tracks, etc.)

### **3. Data Validation**

- Input sanitization and validation
- SQL injection prevention through parameterized queries
- Type safety with TypeScript interfaces

## 📊 **Performance Optimizations**

### **1. Efficient Queries**

- Single RPC call for playlist with tracks
- Bulk operations reduce database round trips
- Optimized indexing for fast lookups

### **2. Caching Strategy**

- Permission caching in context
- Playlist data caching
- Automatic cache invalidation

### **3. Batch Operations**

- Multiple track operations in single transaction
- Reduced network overhead
- Progress tracking for user feedback

## 🧪 **Testing Strategy**

### **1. Database Functions**

```sql
-- Test RPC functions
SELECT * FROM get_user_playlists_with_stats();
SELECT * FROM get_playlist_with_tracks('playlist-id');
SELECT reorder_playlist_tracks('playlist-id', '[{"track_id": "track-1", "position": 1}]'::jsonb);
```

### **2. Service Layer**

```typescript
// Unit tests for EnhancedPlaylistService
describe("EnhancedPlaylistService", () => {
  test("should create playlist with validation", async () => {
    const playlist = await EnhancedPlaylistService.createPlaylist({
      name: "Test Playlist",
      description: "Test Description",
    });
    expect(playlist.name).toBe("Test Playlist");
  });
});
```

### **3. Context Integration**

```typescript
// Integration tests for context
describe("EnhancedPlaylistContext", () => {
  test("should handle bulk operations", async () => {
    const { addTracksToPlaylist } = useEnhancedPlaylist();
    const result = await addTracksToPlaylist("playlist-id", [
      "track-1",
      "track-2",
    ]);
    expect(result).toHaveLength(2);
  });
});
```

## 🎯 **Success Criteria**

- [ ] All RPC functions working correctly
- [ ] Enhanced service layer integrated
- [ ] Context provider updated and functional
- [ ] Bulk operations working with progress tracking
- [ ] Permission system enforced
- [ ] Playlist sharing functional
- [ ] Performance optimizations verified
- [ ] Security measures tested
- [ ] Error handling robust
- [ ] UI components updated

## 🔄 **Migration from Existing System**

### **1. Gradual Migration**

- Keep existing `PlaylistContext` for compatibility
- Gradually migrate components to `EnhancedPlaylistContext`
- Test each component migration thoroughly

### **2. Data Migration**

- No data migration needed (schema compatible)
- New RPC functions work with existing data
- Enhanced features available immediately

### **3. Rollback Plan**

- Keep original service and context files
- Database migration is additive (no breaking changes)
- Easy rollback if issues arise

This implementation provides a robust, scalable, and secure playlist management system with advanced features like bulk operations, permission management, and optimized performance.
