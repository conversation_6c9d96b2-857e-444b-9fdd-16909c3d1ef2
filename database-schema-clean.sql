-- Tunami AI Music Platform Database Schema (Clean Version)
-- Complete schema with AI-specific features, RLS policies, and storage
-- No sample data - will be populated when users sign up

-- =============================================
-- 1. ENABLE EXTENSIONS
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- 2. CUSTOM TYPES
-- =============================================

-- AI Tool types for music generation
CREATE TYPE ai_tool_type AS ENUM (
  'suno',
  'udio',
  'mubert',
  'soundraw',
  'aiva',
  'amper',
  'jukedeck',
  'endlesss',
  'custom',
  'other'
);

-- Music genres
CREATE TYPE genre_type AS ENUM (
  'pop',
  'rock',
  'hip_hop',
  'electronic',
  'jazz',
  'classical',
  'country',
  'folk',
  'r_and_b',
  'reggae',
  'blues',
  'metal',
  'punk',
  'indie',
  'ambient',
  'experimental',
  'world',
  'other'
);

-- Mood types for AI generation
CREATE TYPE mood_type AS ENUM (
  'happy',
  'sad',
  'energetic',
  'calm',
  'aggressive',
  'romantic',
  'mysterious',
  'epic',
  'chill',
  'uplifting',
  'dark',
  'nostalgic',
  'dreamy',
  'intense',
  'playful',
  'serious'
);

-- Agreement versions
CREATE TYPE agreement_version_type AS ENUM (
  'v1.0',
  'v1.1',
  'v2.0',
  'v2.1'
);

-- =============================================
-- 3. PROFILES TABLE (extends Supabase auth.users)
-- =============================================

CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  username TEXT UNIQUE,
  avatar_url TEXT,
  bio TEXT,
  is_premium BOOLEAN DEFAULT FALSE,
  subscription_tier TEXT DEFAULT 'free',
  ai_generation_credits INTEGER DEFAULT 10,
  total_tracks_generated INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 4. TRACKS TABLE (AI-generated music)
-- =============================================

CREATE TABLE tracks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  artist_name TEXT NOT NULL,
  file_url TEXT, -- URL to audio file in storage
  file_path TEXT, -- Storage path
  file_size BIGINT, -- File size in bytes
  ai_tool ai_tool_type NOT NULL,
  duration INTEGER, -- Duration in seconds
  genre genre_type,
  mood mood_type,
  tempo INTEGER, -- BPM
  key_signature TEXT, -- Musical key (C, D#, etc.)
  ai_prompt TEXT, -- Original prompt used for generation
  ai_parameters JSONB, -- AI generation parameters
  is_public BOOLEAN DEFAULT TRUE,
  is_featured BOOLEAN DEFAULT FALSE,
  play_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  upload_status TEXT DEFAULT 'pending', -- pending, processing, complete, failed
  uploaded_by UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 5. PLAYLISTS TABLE
-- =============================================

CREATE TABLE playlists (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  cover_image_url TEXT,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  is_public BOOLEAN DEFAULT FALSE,
  is_collaborative BOOLEAN DEFAULT FALSE,
  track_count INTEGER DEFAULT 0,
  total_duration INTEGER DEFAULT 0, -- Total duration in seconds
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 6. PLAYLIST_TRACKS TABLE (Junction table)
-- =============================================

CREATE TABLE playlist_tracks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  playlist_id UUID REFERENCES playlists(id) ON DELETE CASCADE NOT NULL,
  track_id UUID REFERENCES tracks(id) ON DELETE CASCADE NOT NULL,
  position INTEGER NOT NULL,
  added_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  added_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique position per playlist
  UNIQUE(playlist_id, position),
  UNIQUE(playlist_id, track_id)
);

-- =============================================
-- 7. USER_AGREEMENTS TABLE
-- =============================================

CREATE TABLE user_agreements (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  agreement_type TEXT NOT NULL, -- 'terms_of_service', 'privacy_policy', 'ai_usage_terms'
  agreement_version agreement_version_type NOT NULL,
  accepted_at TIMESTAMPTZ DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT,
  
  -- One agreement per user per type per version
  UNIQUE(user_id, agreement_type, agreement_version)
);

-- =============================================
-- 8. TRACK_LIKES TABLE
-- =============================================

CREATE TABLE track_likes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  track_id UUID REFERENCES tracks(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, track_id)
);

-- =============================================
-- 9. LISTENING_HISTORY TABLE
-- =============================================

CREATE TABLE listening_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  track_id UUID REFERENCES tracks(id) ON DELETE CASCADE NOT NULL,
  listened_at TIMESTAMPTZ DEFAULT NOW(),
  duration_listened INTEGER, -- Seconds listened
  completed BOOLEAN DEFAULT FALSE, -- If user listened to full track
  source TEXT, -- 'playlist', 'search', 'recommendations', etc.
  playlist_id UUID REFERENCES playlists(id) ON DELETE SET NULL
);

-- =============================================
-- 10. AI_GENERATION_JOBS TABLE
-- =============================================

CREATE TABLE ai_generation_jobs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  ai_tool ai_tool_type NOT NULL,
  prompt TEXT NOT NULL,
  parameters JSONB,
  status TEXT DEFAULT 'pending', -- pending, processing, completed, failed
  progress INTEGER DEFAULT 0, -- 0-100
  result_track_id UUID REFERENCES tracks(id) ON DELETE SET NULL,
  error_message TEXT,
  credits_used INTEGER DEFAULT 1,
  processing_time INTEGER, -- Seconds
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ
);

-- =============================================
-- 11. INDEXES FOR PERFORMANCE
-- =============================================

-- Profiles indexes
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_profiles_email ON profiles(email);

-- Tracks indexes
CREATE INDEX idx_tracks_uploaded_by ON tracks(uploaded_by);
CREATE INDEX idx_tracks_ai_tool ON tracks(ai_tool);
CREATE INDEX idx_tracks_genre ON tracks(genre);
CREATE INDEX idx_tracks_mood ON tracks(mood);
CREATE INDEX idx_tracks_created_at ON tracks(created_at DESC);
CREATE INDEX idx_tracks_is_public ON tracks(is_public);
CREATE INDEX idx_tracks_is_featured ON tracks(is_featured);
CREATE INDEX idx_tracks_play_count ON tracks(play_count DESC);

-- Playlists indexes
CREATE INDEX idx_playlists_user_id ON playlists(user_id);
CREATE INDEX idx_playlists_is_public ON playlists(is_public);
CREATE INDEX idx_playlists_created_at ON playlists(created_at DESC);

-- Playlist tracks indexes
CREATE INDEX idx_playlist_tracks_playlist_id ON playlist_tracks(playlist_id);
CREATE INDEX idx_playlist_tracks_track_id ON playlist_tracks(track_id);
CREATE INDEX idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);

-- User agreements indexes
CREATE INDEX idx_user_agreements_user_id ON user_agreements(user_id);
CREATE INDEX idx_user_agreements_type_version ON user_agreements(agreement_type, agreement_version);

-- Track likes indexes
CREATE INDEX idx_track_likes_user_id ON track_likes(user_id);
CREATE INDEX idx_track_likes_track_id ON track_likes(track_id);

-- Listening history indexes
CREATE INDEX idx_listening_history_user_id ON listening_history(user_id);
CREATE INDEX idx_listening_history_track_id ON listening_history(track_id);
CREATE INDEX idx_listening_history_listened_at ON listening_history(listened_at DESC);

-- AI generation jobs indexes
CREATE INDEX idx_ai_jobs_user_id ON ai_generation_jobs(user_id);
CREATE INDEX idx_ai_jobs_status ON ai_generation_jobs(status);
CREATE INDEX idx_ai_jobs_created_at ON ai_generation_jobs(created_at DESC);

-- =============================================
-- 12. ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tracks ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlist_tracks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_agreements ENABLE ROW LEVEL SECURITY;
ALTER TABLE track_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE listening_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_generation_jobs ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Tracks policies
CREATE POLICY "Public tracks are viewable by everyone"
  ON tracks FOR SELECT
  USING (is_public = true OR uploaded_by = auth.uid());

CREATE POLICY "Users can insert their own tracks"
  ON tracks FOR INSERT
  WITH CHECK (uploaded_by = auth.uid());

CREATE POLICY "Users can update their own tracks"
  ON tracks FOR UPDATE
  USING (uploaded_by = auth.uid());

CREATE POLICY "Users can delete their own tracks"
  ON tracks FOR DELETE
  USING (uploaded_by = auth.uid());

-- Playlists policies
CREATE POLICY "Public playlists are viewable by everyone"
  ON playlists FOR SELECT
  USING (is_public = true OR user_id = auth.uid());

CREATE POLICY "Users can manage their own playlists"
  ON playlists FOR ALL
  USING (user_id = auth.uid());

-- Playlist tracks policies
CREATE POLICY "Playlist tracks viewable based on playlist access"
  ON playlist_tracks FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM playlists 
      WHERE playlists.id = playlist_tracks.playlist_id 
      AND (playlists.is_public = true OR playlists.user_id = auth.uid())
    )
  );

CREATE POLICY "Users can manage tracks in their playlists"
  ON playlist_tracks FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM playlists 
      WHERE playlists.id = playlist_tracks.playlist_id 
      AND playlists.user_id = auth.uid()
    )
  );

-- User agreements policies
CREATE POLICY "Users can view their own agreements"
  ON user_agreements FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own agreements"
  ON user_agreements FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Track likes policies
CREATE POLICY "Users can view their own likes"
  ON track_likes FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own likes"
  ON track_likes FOR ALL
  USING (user_id = auth.uid());

-- Listening history policies
CREATE POLICY "Users can view their own listening history"
  ON listening_history FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own listening history"
  ON listening_history FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- AI generation jobs policies
CREATE POLICY "Users can view their own AI jobs"
  ON ai_generation_jobs FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own AI jobs"
  ON ai_generation_jobs FOR INSERT
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own AI jobs"
  ON ai_generation_jobs FOR UPDATE
  USING (user_id = auth.uid());

-- =============================================
-- 13. STORAGE BUCKETS
-- =============================================

-- Create storage bucket for audio files
INSERT INTO storage.buckets (id, name, public) VALUES ('audio-tracks', 'audio-tracks', true);
INSERT INTO storage.buckets (id, name, public) VALUES ('playlist-covers', 'playlist-covers', true);
INSERT INTO storage.buckets (id, name, public) VALUES ('profile-avatars', 'profile-avatars', true);

-- Storage policies for audio tracks
CREATE POLICY "Audio files are publicly viewable"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'audio-tracks');

CREATE POLICY "Users can upload their own audio files"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'audio-tracks' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own audio files"
  ON storage.objects FOR UPDATE
  USING (
    bucket_id = 'audio-tracks' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own audio files"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'audio-tracks' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for playlist covers
CREATE POLICY "Playlist covers are publicly viewable"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'playlist-covers');

CREATE POLICY "Users can upload playlist covers"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'playlist-covers' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for profile avatars
CREATE POLICY "Profile avatars are publicly viewable"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'profile-avatars');

CREATE POLICY "Users can upload their own avatar"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'profile-avatars' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- =============================================
-- 14. FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tracks_updated_at
    BEFORE UPDATE ON tracks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_playlists_updated_at
    BEFORE UPDATE ON playlists
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update playlist stats
CREATE OR REPLACE FUNCTION update_playlist_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE playlists 
        SET 
            track_count = track_count + 1,
            total_duration = total_duration + COALESCE((SELECT duration FROM tracks WHERE id = NEW.track_id), 0)
        WHERE id = NEW.playlist_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE playlists 
        SET 
            track_count = track_count - 1,
            total_duration = total_duration - COALESCE((SELECT duration FROM tracks WHERE id = OLD.track_id), 0)
        WHERE id = OLD.playlist_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE 'plpgsql';

-- Apply playlist stats triggers
CREATE TRIGGER update_playlist_stats_on_insert
    AFTER INSERT ON playlist_tracks
    FOR EACH ROW EXECUTE FUNCTION update_playlist_stats();

CREATE TRIGGER update_playlist_stats_on_delete
    AFTER DELETE ON playlist_tracks
    FOR EACH ROW EXECUTE FUNCTION update_playlist_stats();

-- Function to update track like count
CREATE OR REPLACE FUNCTION update_track_like_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE tracks SET like_count = like_count + 1 WHERE id = NEW.track_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE tracks SET like_count = like_count - 1 WHERE id = OLD.track_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE 'plpgsql';

-- Apply like count triggers
CREATE TRIGGER update_track_like_count_on_insert
    AFTER INSERT ON track_likes
    FOR EACH ROW EXECUTE FUNCTION update_track_like_count();

CREATE TRIGGER update_track_like_count_on_delete
    AFTER DELETE ON track_likes
    FOR EACH ROW EXECUTE FUNCTION update_track_like_count();

-- Function to handle user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name'
    );
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql' SECURITY DEFINER;

-- Trigger for automatic profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- =============================================
-- 15. UTILITY VIEWS
-- =============================================

-- View for popular tracks
CREATE VIEW popular_tracks AS
SELECT 
    t.*,
    p.username as uploader_username,
    p.full_name as uploader_name
FROM tracks t
JOIN profiles p ON t.uploaded_by = p.id
WHERE t.is_public = true
ORDER BY t.play_count DESC, t.like_count DESC;

-- View for user's complete playlists
CREATE VIEW user_playlists_with_stats AS
SELECT 
    pl.*,
    p.username,
    p.full_name,
    COUNT(pt.track_id) as actual_track_count
FROM playlists pl
JOIN profiles p ON pl.user_id = p.id
LEFT JOIN playlist_tracks pt ON pl.id = pt.playlist_id
GROUP BY pl.id, p.username, p.full_name;

-- =============================================
-- 16. GRANT PERMISSIONS
-- =============================================

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, authenticated, service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO postgres, authenticated, service_role;

-- =============================================
-- SCHEMA COMPLETE - NO SAMPLE DATA
-- =============================================

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Tunami AI Music Platform database schema created successfully!';
    RAISE NOTICE 'Tables created: profiles, tracks, playlists, playlist_tracks, user_agreements, track_likes, listening_history, ai_generation_jobs';
    RAISE NOTICE 'Storage buckets: audio-tracks, playlist-covers, profile-avatars';
    RAISE NOTICE 'RLS policies enabled for all tables';
    RAISE NOTICE 'Ready for users to sign up and start creating content!';
END
$$; 