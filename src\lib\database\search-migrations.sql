-- Search History Table
CREATE TABLE IF NOT EXISTS search_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  query TEXT NOT NULL,
  result_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  CONSTRAINT search_history_query_length CHECK (length(query) >= 1 AND length(query) <= 500)
);

-- Search Analytics Table
CREATE TABLE IF NOT EXISTS search_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  query TEXT NOT NULL,
  result_count INTEGER DEFAULT 0,
  clicked_result JSONB,
  filters_used JSONB DEFAULT '{}',
  search_time INTEGER DEFAULT 0, -- milliseconds
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  CONSTRAINT search_analytics_query_length CHECK (length(query) >= 1 AND length(query) <= 500),
  CONSTRAINT search_analytics_search_time_positive CHECK (search_time >= 0)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_search_history_user_id ON search_history(user_id);
CREATE INDEX IF NOT EXISTS idx_search_history_created_at ON search_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_search_history_query ON search_history(query);

CREATE INDEX IF NOT EXISTS idx_search_analytics_query ON search_analytics(query);
CREATE INDEX IF NOT EXISTS idx_search_analytics_created_at ON search_analytics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_search_analytics_user_id ON search_analytics(user_id);

-- Full-text search indexes on tracks table
CREATE INDEX IF NOT EXISTS idx_tracks_search ON tracks USING gin(to_tsvector('english', title || ' ' || artist_name || ' ' || COALESCE(genre, '') || ' ' || COALESCE(ai_tool, '')));

-- Full-text search indexes on playlists table  
CREATE INDEX IF NOT EXISTS idx_playlists_search ON playlists USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Function to get search suggestions
CREATE OR REPLACE FUNCTION get_search_suggestions(
  search_query TEXT,
  max_suggestions INTEGER DEFAULT 10
)
RETURNS TABLE (
  id TEXT,
  text TEXT,
  type TEXT,
  subtitle TEXT,
  metadata JSONB
) AS $$
BEGIN
  RETURN QUERY
  WITH track_suggestions AS (
    SELECT 
      tracks.id::TEXT,
      tracks.title as text,
      'track'::TEXT as type,
      tracks.artist_name as subtitle,
      jsonb_build_object(
        'ai_tool', tracks.ai_tool,
        'genre', tracks.genre
      ) as metadata
    FROM tracks
    WHERE 
      tracks.is_public = true
      AND (
        tracks.title ILIKE '%' || search_query || '%'
        OR tracks.artist_name ILIKE '%' || search_query || '%'
      )
    ORDER BY 
      CASE 
        WHEN tracks.title ILIKE search_query || '%' THEN 1
        WHEN tracks.artist_name ILIKE search_query || '%' THEN 2
        ELSE 3
      END,
      tracks.created_at DESC
    LIMIT max_suggestions / 3
  ),
  artist_suggestions AS (
    SELECT DISTINCT
      tracks.artist_name::TEXT as id,
      tracks.artist_name as text,
      'artist'::TEXT as type,
      COUNT(*)::TEXT || ' tracks' as subtitle,
      jsonb_build_object(
        'track_count', COUNT(*)
      ) as metadata
    FROM tracks
    WHERE 
      tracks.is_public = true
      AND tracks.artist_name ILIKE '%' || search_query || '%'
    GROUP BY tracks.artist_name
    ORDER BY 
      CASE 
        WHEN tracks.artist_name ILIKE search_query || '%' THEN 1
        ELSE 2
      END,
      COUNT(*) DESC
    LIMIT max_suggestions / 3
  ),
  playlist_suggestions AS (
    SELECT 
      playlists.id::TEXT,
      playlists.name as text,
      'playlist'::TEXT as type,
      COALESCE(playlists.description, 'Playlist') as subtitle,
      jsonb_build_object(
        'track_count', COALESCE(playlist_stats.track_count, 0)
      ) as metadata
    FROM playlists
    LEFT JOIN (
      SELECT 
        playlist_id,
        COUNT(*) as track_count
      FROM playlist_tracks
      GROUP BY playlist_id
    ) playlist_stats ON playlists.id = playlist_stats.playlist_id
    WHERE 
      playlists.is_public = true
      AND playlists.name ILIKE '%' || search_query || '%'
    ORDER BY 
      CASE 
        WHEN playlists.name ILIKE search_query || '%' THEN 1
        ELSE 2
      END,
      playlists.updated_at DESC
    LIMIT max_suggestions / 3
  ),
  ai_tool_suggestions AS (
    SELECT DISTINCT
      tracks.ai_tool::TEXT as id,
      tracks.ai_tool as text,
      'ai_tool'::TEXT as type,
      COUNT(*)::TEXT || ' tracks' as subtitle,
      jsonb_build_object(
        'track_count', COUNT(*)
      ) as metadata
    FROM tracks
    WHERE 
      tracks.is_public = true
      AND tracks.ai_tool IS NOT NULL
      AND tracks.ai_tool ILIKE '%' || search_query || '%'
    GROUP BY tracks.ai_tool
    ORDER BY COUNT(*) DESC
    LIMIT 2
  )
  SELECT * FROM track_suggestions
  UNION ALL
  SELECT * FROM artist_suggestions
  UNION ALL
  SELECT * FROM playlist_suggestions
  UNION ALL
  SELECT * FROM ai_tool_suggestions
  ORDER BY 
    CASE type
      WHEN 'track' THEN 1
      WHEN 'artist' THEN 2
      WHEN 'playlist' THEN 3
      WHEN 'ai_tool' THEN 4
    END
  LIMIT max_suggestions;
END;
$$ LANGUAGE plpgsql;

-- Function for comprehensive search
CREATE OR REPLACE FUNCTION search_content(
  search_query TEXT,
  search_filters JSONB DEFAULT '{}',
  page_number INTEGER DEFAULT 1,
  page_size INTEGER DEFAULT 20
)
RETURNS TABLE (
  tracks JSONB,
  playlists JSONB,
  artists JSONB,
  total_tracks INTEGER,
  total_playlists INTEGER,
  total_artists INTEGER,
  has_more BOOLEAN
) AS $$
DECLARE
  offset_val INTEGER;
  ai_tools TEXT[];
  genres TEXT[];
  duration_min INTEGER;
  duration_max INTEGER;
  date_from TIMESTAMP;
  date_to TIMESTAMP;
  is_public_filter BOOLEAN;
  sort_by TEXT;
  sort_order TEXT;
BEGIN
  -- Calculate offset
  offset_val := (page_number - 1) * page_size;
  
  -- Extract filters
  ai_tools := CASE 
    WHEN search_filters ? 'ai_tool' THEN 
      ARRAY(SELECT jsonb_array_elements_text(search_filters->'ai_tool'))
    ELSE NULL 
  END;
  
  genres := CASE 
    WHEN search_filters ? 'genre' THEN 
      ARRAY(SELECT jsonb_array_elements_text(search_filters->'genre'))
    ELSE NULL 
  END;
  
  duration_min := (search_filters->>'duration_min')::INTEGER;
  duration_max := (search_filters->>'duration_max')::INTEGER;
  date_from := (search_filters->>'date_from')::TIMESTAMP;
  date_to := (search_filters->>'date_to')::TIMESTAMP;
  is_public_filter := (search_filters->>'is_public')::BOOLEAN;
  sort_by := COALESCE(search_filters->>'sort_by', 'created_at');
  sort_order := COALESCE(search_filters->>'sort_order', 'desc');

  -- Search tracks
  WITH filtered_tracks AS (
    SELECT t.*
    FROM tracks t
    WHERE 
      (search_query = '' OR (
        t.title ILIKE '%' || search_query || '%'
        OR t.artist_name ILIKE '%' || search_query || '%'
        OR t.genre ILIKE '%' || search_query || '%'
        OR t.ai_tool ILIKE '%' || search_query || '%'
      ))
      AND (ai_tools IS NULL OR t.ai_tool = ANY(ai_tools))
      AND (genres IS NULL OR t.genre = ANY(genres))
      AND (duration_min IS NULL OR t.duration >= duration_min)
      AND (duration_max IS NULL OR t.duration <= duration_max)
      AND (date_from IS NULL OR t.created_at >= date_from)
      AND (date_to IS NULL OR t.created_at <= date_to)
      AND (is_public_filter IS NULL OR t.is_public = is_public_filter)
  ),
  track_results AS (
    SELECT 
      jsonb_agg(
        jsonb_build_object(
          'id', ft.id,
          'title', ft.title,
          'artist_name', ft.artist_name,
          'genre', ft.genre,
          'ai_tool', ft.ai_tool,
          'duration', ft.duration,
          'file_url', ft.file_url,
          'cover_image_url', ft.cover_image_url,
          'is_public', ft.is_public,
          'created_at', ft.created_at,
          'updated_at', ft.updated_at
        ) ORDER BY 
          CASE 
            WHEN sort_by = 'title' AND sort_order = 'asc' THEN ft.title
            WHEN sort_by = 'artist_name' AND sort_order = 'asc' THEN ft.artist_name
          END ASC,
          CASE 
            WHEN sort_by = 'title' AND sort_order = 'desc' THEN ft.title
            WHEN sort_by = 'artist_name' AND sort_order = 'desc' THEN ft.artist_name
          END DESC,
          CASE 
            WHEN sort_by = 'duration' AND sort_order = 'asc' THEN ft.duration
          END ASC,
          CASE 
            WHEN sort_by = 'duration' AND sort_order = 'desc' THEN ft.duration
          END DESC,
          CASE 
            WHEN sort_by = 'created_at' AND sort_order = 'asc' THEN ft.created_at
          END ASC,
          CASE 
            WHEN sort_by = 'created_at' AND sort_order = 'desc' THEN ft.created_at
          END DESC
      ) as tracks_json,
      COUNT(*) as total_tracks_count
    FROM (
      SELECT * FROM filtered_tracks
      LIMIT page_size OFFSET offset_val
    ) ft
  ),
  -- Search playlists
  filtered_playlists AS (
    SELECT p.*
    FROM playlists p
    WHERE 
      (search_query = '' OR (
        p.name ILIKE '%' || search_query || '%'
        OR p.description ILIKE '%' || search_query || '%'
      ))
      AND (is_public_filter IS NULL OR p.is_public = is_public_filter)
      AND (date_from IS NULL OR p.created_at >= date_from)
      AND (date_to IS NULL OR p.created_at <= date_to)
  ),
  playlist_results AS (
    SELECT 
      jsonb_agg(
        jsonb_build_object(
          'id', fp.id,
          'name', fp.name,
          'description', fp.description,
          'cover_image_url', fp.cover_image_url,
          'is_public', fp.is_public,
          'user_id', fp.user_id,
          'created_at', fp.created_at,
          'updated_at', fp.updated_at,
          'track_count', COALESCE(pt.track_count, 0)
        ) ORDER BY fp.updated_at DESC
      ) as playlists_json,
      COUNT(*) as total_playlists_count
    FROM (
      SELECT * FROM filtered_playlists
      LIMIT page_size OFFSET offset_val
    ) fp
    LEFT JOIN (
      SELECT playlist_id, COUNT(*) as track_count
      FROM playlist_tracks
      GROUP BY playlist_id
    ) pt ON fp.id = pt.playlist_id
  ),
  -- Search artists (aggregated from tracks)
  artist_results AS (
    SELECT 
      jsonb_agg(
        jsonb_build_object(
          'id', artist_name,
          'name', artist_name,
          'track_count', track_count,
          'genres', genres_array,
          'ai_tools', ai_tools_array
        ) ORDER BY track_count DESC
      ) as artists_json,
      COUNT(*) as total_artists_count
    FROM (
      SELECT 
        t.artist_name,
        COUNT(*) as track_count,
        array_agg(DISTINCT t.genre) FILTER (WHERE t.genre IS NOT NULL) as genres_array,
        array_agg(DISTINCT t.ai_tool) FILTER (WHERE t.ai_tool IS NOT NULL) as ai_tools_array
      FROM tracks t
      WHERE 
        t.is_public = true
        AND (search_query = '' OR t.artist_name ILIKE '%' || search_query || '%')
      GROUP BY t.artist_name
      HAVING COUNT(*) > 0
      LIMIT page_size OFFSET offset_val
    ) artist_data
  )
  SELECT 
    COALESCE(tr.tracks_json, '[]'::jsonb) as tracks,
    COALESCE(pr.playlists_json, '[]'::jsonb) as playlists,
    COALESCE(ar.artists_json, '[]'::jsonb) as artists,
    (SELECT COUNT(*) FROM filtered_tracks)::INTEGER as total_tracks,
    (SELECT COUNT(*) FROM filtered_playlists)::INTEGER as total_playlists,
    (SELECT COUNT(DISTINCT artist_name) FROM tracks WHERE is_public = true AND (search_query = '' OR artist_name ILIKE '%' || search_query || '%'))::INTEGER as total_artists,
    ((SELECT COUNT(*) FROM filtered_tracks) + (SELECT COUNT(*) FROM filtered_playlists) > (offset_val + page_size)) as has_more
  FROM track_results tr
  CROSS JOIN playlist_results pr
  CROSS JOIN artist_results ar;
END;
$$ LANGUAGE plpgsql;

-- Function to get popular searches
CREATE OR REPLACE FUNCTION get_popular_searches(search_limit INTEGER DEFAULT 10)
RETURNS TABLE (
  query TEXT,
  search_count BIGINT,
  trend_direction TEXT,
  categories TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  WITH recent_searches AS (
    SELECT 
      sa.query,
      COUNT(*) as search_count,
      array_agg(DISTINCT 
        CASE 
          WHEN sa.clicked_result->>'type' IS NOT NULL 
          THEN sa.clicked_result->>'type'
        END
      ) FILTER (WHERE sa.clicked_result->>'type' IS NOT NULL) as categories
    FROM search_analytics sa
    WHERE sa.created_at >= NOW() - INTERVAL '7 days'
    GROUP BY sa.query
    HAVING COUNT(*) >= 2
  ),
  previous_searches AS (
    SELECT 
      sa.query,
      COUNT(*) as prev_count
    FROM search_analytics sa
    WHERE sa.created_at >= NOW() - INTERVAL '14 days'
      AND sa.created_at < NOW() - INTERVAL '7 days'
    GROUP BY sa.query
  )
  SELECT 
    rs.query,
    rs.search_count,
    CASE 
      WHEN ps.prev_count IS NULL THEN 'up'
      WHEN rs.search_count > ps.prev_count THEN 'up'
      WHEN rs.search_count < ps.prev_count THEN 'down'
      ELSE 'stable'
    END::TEXT as trend_direction,
    COALESCE(rs.categories, ARRAY[]::TEXT[]) as categories
  FROM recent_searches rs
  LEFT JOIN previous_searches ps ON rs.query = ps.query
  ORDER BY rs.search_count DESC
  LIMIT search_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to get personalized suggestions
CREATE OR REPLACE FUNCTION get_personalized_suggestions(
  user_id UUID,
  max_suggestions INTEGER DEFAULT 8
)
RETURNS TABLE (
  id TEXT,
  text TEXT,
  type TEXT,
  subtitle TEXT,
  metadata JSONB
) AS $$
BEGIN
  RETURN QUERY
  WITH user_history AS (
    SELECT DISTINCT sh.query
    FROM search_history sh
    WHERE sh.user_id = get_personalized_suggestions.user_id
    ORDER BY sh.created_at DESC
    LIMIT 5
  ),
  user_interests AS (
    SELECT 
      UNNEST(string_to_array(uh.query, ' ')) as term
    FROM user_history uh
  ),
  suggested_tracks AS (
    SELECT 
      t.id::TEXT,
      t.title as text,
      'track'::TEXT as type,
      t.artist_name as subtitle,
      jsonb_build_object(
        'ai_tool', t.ai_tool,
        'genre', t.genre
      ) as metadata
    FROM tracks t
    CROSS JOIN user_interests ui
    WHERE 
      t.is_public = true
      AND (
        t.title ILIKE '%' || ui.term || '%'
        OR t.artist_name ILIKE '%' || ui.term || '%'
        OR t.genre ILIKE '%' || ui.term || '%'
      )
    GROUP BY t.id, t.title, t.artist_name, t.ai_tool, t.genre
    ORDER BY COUNT(*) DESC, t.created_at DESC
    LIMIT max_suggestions / 2
  ),
  popular_suggestions AS (
    SELECT 
      ps.query::TEXT as id,
      ps.query as text,
      'popular'::TEXT as type,
      'Trending search' as subtitle,
      jsonb_build_object(
        'search_count', ps.search_count
      ) as metadata
    FROM get_popular_searches(max_suggestions / 2) ps
  )
  SELECT * FROM suggested_tracks
  UNION ALL
  SELECT * FROM popular_suggestions
  LIMIT max_suggestions;
END;
$$ LANGUAGE plpgsql;

-- RLS Policies
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_analytics ENABLE ROW LEVEL SECURITY;

-- Users can only see their own search history
CREATE POLICY "Users can view own search history" ON search_history
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own search history" ON search_history
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own search history" ON search_history
  FOR DELETE USING (auth.uid() = user_id);

-- Search analytics are insert-only for authenticated users
CREATE POLICY "Authenticated users can insert search analytics" ON search_analytics
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Admin users can view all analytics
CREATE POLICY "Admin users can view all search analytics" ON search_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  ); 