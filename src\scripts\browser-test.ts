// Comprehensive browser testing script for cross-browser compatibility

interface BrowserTestResult {
  browser: string
  version: string
  platform: string
  tests: {
    audioFormats: Record<string, boolean>
    audioFeatures: Record<string, boolean>
    webFeatures: Record<string, boolean>
    performance: Record<string, number>
    cssSupport: Record<string, boolean>
  }
  issues: string[]
  recommendations: string[]
  score: number
}

interface AudioTestConfig {
  format: string
  src: string
  mimeType: string
}

class BrowserTester {
  private results: BrowserTestResult = {
    browser: '',
    version: '',
    platform: '',
    tests: {
      audioFormats: {},
      audioFeatures: {},
      webFeatures: {},
      performance: {},
      cssSupport: {}
    },
    issues: [],
    recommendations: [],
    score: 0
  }

  private audioTests: AudioTestConfig[] = [
    { format: 'mp3', src: 'data:audio/mpeg;base64,//tQxAAAAAAAAAAAAAAAAAAA', mimeType: 'audio/mpeg' },
    { format: 'wav', src: 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA', mimeType: 'audio/wav' },
    { format: 'ogg', src: 'data:audio/ogg;base64,T2dnUwACAAAAAAAAAAA=', mimeType: 'audio/ogg' },
    { format: 'webm', src: 'data:audio/webm;base64,GkXfowEAAAAA', mimeType: 'audio/webm' },
    { format: 'aac', src: 'data:audio/aac;base64,//MgxAAAAAAAAAAA', mimeType: 'audio/aac' },
    { format: 'flac', src: 'data:audio/flac;base64,ZkxhQwAAACIAAAAA', mimeType: 'audio/flac' }
  ]

  async runFullTest(): Promise<BrowserTestResult> {
    console.log('🧪 Running comprehensive browser compatibility test...')
    
    this.detectBrowser()
    await this.testAudioFormats()
    await this.testAudioFeatures()
    await this.testWebFeatures()
    await this.testPerformance()
    await this.testCSSSupport()
    this.generateRecommendations()
    this.calculateScore()

    return this.results
  }

  private detectBrowser(): void {
    const userAgent = navigator.userAgent
    const platform = navigator.platform

    // Browser detection
    if (userAgent.includes('Firefox')) {
      this.results.browser = 'Firefox'
      const match = userAgent.match(/Firefox\/(\d+\.\d+)/)
      this.results.version = match ? match[1] : 'Unknown'
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      this.results.browser = 'Safari'
      const match = userAgent.match(/Version\/(\d+\.\d+)/)
      this.results.version = match ? match[1] : 'Unknown'
    } else if (userAgent.includes('Chrome')) {
      this.results.browser = 'Chrome'
      const match = userAgent.match(/Chrome\/(\d+\.\d+)/)
      this.results.version = match ? match[1] : 'Unknown'
    } else if (userAgent.includes('Edge')) {
      this.results.browser = 'Edge'
      const match = userAgent.match(/Edg\/(\d+\.\d+)/)
      this.results.version = match ? match[1] : 'Unknown'
    }

    this.results.platform = platform
  }

  private async testAudioFormats(): Promise<void> {
    console.log('🎵 Testing audio format support...')
    
    const audio = new Audio()
    
    for (const test of this.audioTests) {
      try {
        const canPlay = audio.canPlayType(test.mimeType)
        this.results.tests.audioFormats[test.format] = canPlay !== ''
        
        if (canPlay === '') {
          this.results.issues.push(`No support for ${test.format} format`)
        }
      } catch (error) {
        this.results.tests.audioFormats[test.format] = false
        this.results.issues.push(`Error testing ${test.format}: ${error}`)
      }
    }
  }

  private async testAudioFeatures(): Promise<void> {
    console.log('🔊 Testing audio features...')
    
    // Web Audio API
    this.results.tests.audioFeatures.webAudio = !!(window.AudioContext || window.webkitAudioContext)
    
    // Media Session API
    this.results.tests.audioFeatures.mediaSession = 'mediaSession' in navigator
    
    // Audio Context State
    if (this.results.tests.audioFeatures.webAudio) {
      try {
        const AudioContext = window.AudioContext || window.webkitAudioContext
        const context = new AudioContext()
        this.results.tests.audioFeatures.audioContextRunning = context.state === 'running'
        context.close()
      } catch (error) {
        this.results.tests.audioFeatures.audioContextRunning = false
        this.results.issues.push(`AudioContext error: ${error}`)
      }
    }

    // Audio worklet support
    this.results.tests.audioFeatures.audioWorklet = !!(window.AudioWorkletNode)
    
    // Test autoplay capability
    await this.testAutoplay()
  }

  private async testAutoplay(): Promise<void> {
    try {
      const audio = new Audio()
      audio.muted = true
      audio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA'
      
      const playPromise = audio.play()
      
      if (playPromise !== undefined) {
        await playPromise
        this.results.tests.audioFeatures.autoplayMuted = true
        audio.pause()
      }
    } catch (error) {
      this.results.tests.audioFeatures.autoplayMuted = false
      this.results.issues.push('Autoplay not supported even when muted')
    }
  }

  private async testWebFeatures(): Promise<void> {
    console.log('🌐 Testing web features...')
    
    // Service Worker
    this.results.tests.webFeatures.serviceWorker = 'serviceWorker' in navigator
    
    // Local Storage
    this.results.tests.webFeatures.localStorage = (() => {
      try {
        localStorage.setItem('test', 'test')
        localStorage.removeItem('test')
        return true
      } catch {
        return false
      }
    })()
    
    // IndexedDB
    this.results.tests.webFeatures.indexedDB = 'indexedDB' in window
    
    // Intersection Observer
    this.results.tests.webFeatures.intersectionObserver = 'IntersectionObserver' in window
    
    // Resize Observer
    this.results.tests.webFeatures.resizeObserver = 'ResizeObserver' in window
    
    // Fullscreen API
    this.results.tests.webFeatures.fullscreen = !!(
      document.documentElement.requestFullscreen ||
      document.documentElement.webkitRequestFullscreen ||
      document.documentElement.mozRequestFullScreen
    )
    
    // Picture-in-Picture
    this.results.tests.webFeatures.pictureInPicture = 'pictureInPictureEnabled' in document
    
    // Notifications
    this.results.tests.webFeatures.notifications = 'Notification' in window
    
    // WebGL
    this.results.tests.webFeatures.webGL = (() => {
      try {
        const canvas = document.createElement('canvas')
        return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
      } catch {
        return false
      }
    })()
  }

  private async testPerformance(): Promise<void> {
    console.log('⚡ Testing performance...')
    
    // Memory usage
    const memory = (performance as any).memory
    if (memory) {
      this.results.tests.performance.memoryUsed = memory.usedJSHeapSize
      this.results.tests.performance.memoryLimit = memory.jsHeapSizeLimit
      this.results.tests.performance.memoryUtilization = 
        (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    }

    // Network information
    const connection = (navigator as any).connection
    if (connection) {
      this.results.tests.performance.downlink = connection.downlink || 0
      this.results.tests.performance.rtt = connection.rtt || 0
    }

    // Timing tests
    const start = performance.now()
    
    // DOM manipulation test
    const div = document.createElement('div')
    div.style.cssText = 'position:absolute;top:-9999px;width:100px;height:100px;'
    document.body.appendChild(div)
    document.body.removeChild(div)
    
    this.results.tests.performance.domManipulation = performance.now() - start

    // CSS animation test
    await this.testCSSAnimationPerformance()
    
    // Audio loading test
    await this.testAudioLoadingPerformance()
  }

  private async testCSSAnimationPerformance(): Promise<void> {
    const start = performance.now()
    
    const testElement = document.createElement('div')
    testElement.style.cssText = `
      position: absolute;
      top: -9999px;
      width: 100px;
      height: 100px;
      background: red;
      transition: transform 100ms;
    `
    
    document.body.appendChild(testElement)
    
    // Force reflow
    testElement.offsetHeight
    
    testElement.style.transform = 'translateX(100px)'
    
    setTimeout(() => {
      this.results.tests.performance.cssAnimation = performance.now() - start
      document.body.removeChild(testElement)
    }, 110)
  }

  private async testAudioLoadingPerformance(): Promise<void> {
    const start = performance.now()
    
    try {
      const audio = new Audio()
      audio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA'
      
      await new Promise((resolve, reject) => {
        audio.oncanplay = resolve
        audio.onerror = reject
        audio.load()
      })
      
      this.results.tests.performance.audioLoading = performance.now() - start
    } catch (error) {
      this.results.tests.performance.audioLoading = -1
      this.results.issues.push(`Audio loading test failed: ${error}`)
    }
  }

  private async testCSSSupport(): Promise<void> {
    console.log('🎨 Testing CSS feature support...')
    
    // CSS Custom Properties
    this.results.tests.cssSupport.customProperties = CSS.supports('color', 'var(--test)')
    
    // CSS Grid
    this.results.tests.cssSupport.grid = CSS.supports('display', 'grid')
    
    // Flexbox
    this.results.tests.cssSupport.flexbox = CSS.supports('display', 'flex')
    
    // Backdrop Filter
    this.results.tests.cssSupport.backdropFilter = CSS.supports('backdrop-filter', 'blur(10px)')
    
    // Sticky positioning
    this.results.tests.cssSupport.sticky = CSS.supports('position', 'sticky')
    
    // Transform 3D
    this.results.tests.cssSupport.transform3d = CSS.supports('transform', 'translateZ(0)')
    
    // CSS Animations
    this.results.tests.cssSupport.animations = CSS.supports('animation', 'test 1s')
    
    // CSS Transitions
    this.results.tests.cssSupport.transitions = CSS.supports('transition', 'all 1s')
  }

  private generateRecommendations(): void {
    const { tests, issues } = this.results
    
    // Audio format recommendations
    const supportedFormats = Object.entries(tests.audioFormats)
      .filter(([_, supported]) => supported)
      .map(([format]) => format)
    
    if (supportedFormats.length < 3) {
      this.results.recommendations.push('Consider providing multiple audio format fallbacks')
    }
    
    if (!tests.audioFormats.mp3) {
      this.results.recommendations.push('MP3 support missing - ensure MP3 fallback is available')
    }

    // Feature recommendations
    if (!tests.webFeatures.serviceWorker) {
      this.results.recommendations.push('Service Worker not supported - offline functionality limited')
    }
    
    if (!tests.webFeatures.intersectionObserver) {
      this.results.recommendations.push('Intersection Observer polyfill needed for optimal performance')
    }
    
    if (!tests.audioFeatures.webAudio) {
      this.results.recommendations.push('Web Audio API not supported - audio features will be limited')
    }

    // Performance recommendations
    if (tests.performance.memoryUtilization > 80) {
      this.results.recommendations.push('High memory usage detected - consider optimization')
    }
    
    if (tests.performance.audioLoading > 1000) {
      this.results.recommendations.push('Slow audio loading - consider audio compression')
    }

    // CSS recommendations
    if (!tests.cssSupport.customProperties) {
      this.results.recommendations.push('CSS Custom Properties not supported - use fallback values')
    }
    
    if (!tests.cssSupport.grid) {
      this.results.recommendations.push('CSS Grid not supported - use Flexbox fallbacks')
    }
  }

  private calculateScore(): void {
    let score = 0
    let totalTests = 0

    // Weight different test categories
    const weights = {
      audioFormats: 3,
      audioFeatures: 2,
      webFeatures: 1,
      cssSupport: 1
    }

    Object.entries(this.results.tests).forEach(([category, tests]) => {
      if (category === 'performance') return // Skip performance for scoring
      
      const weight = weights[category as keyof typeof weights] || 1
      const passed = Object.values(tests).filter(Boolean).length
      const total = Object.values(tests).length
      
      score += (passed / total) * weight * 20
      totalTests += weight * 20
    })

    // Deduct points for issues
    score -= this.results.issues.length * 2
    
    this.results.score = Math.max(0, Math.min(100, Math.round(score)))
  }

  // Generate detailed report
  generateReport(): string {
    const { browser, version, platform, tests, issues, recommendations, score } = this.results
    
    let report = `
# Browser Compatibility Report

## Environment
- **Browser**: ${browser} ${version}
- **Platform**: platform
- **Score**: ${score}/100

## Audio Support
`
    
    Object.entries(tests.audioFormats).forEach(([format, supported]) => {
      report += `- ${format.toUpperCase()}: ${supported ? '✅' : '❌'}\n`
    })

    report += `\n## Audio Features\n`
    Object.entries(tests.audioFeatures).forEach(([feature, supported]) => {
      report += `- ${feature}: ${supported ? '✅' : '❌'}\n`
    })

    report += `\n## Web Features\n`
    Object.entries(tests.webFeatures).forEach(([feature, supported]) => {
      report += `- ${feature}: ${supported ? '✅' : '❌'}\n`
    })

    report += `\n## CSS Support\n`
    Object.entries(tests.cssSupport).forEach(([feature, supported]) => {
      report += `- ${feature}: ${supported ? '✅' : '❌'}\n`
    })

    if (issues.length > 0) {
      report += `\n## Issues Found\n`
      issues.forEach(issue => {
        report += `- ⚠️ ${issue}\n`
      })
    }

    if (recommendations.length > 0) {
      report += `\n## Recommendations\n`
      recommendations.forEach(rec => {
        report += `- 💡 ${rec}\n`
      })
    }

    return report
  }
}

// Export for use in development
export { BrowserTester }

// Auto-run in development mode
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Add console command for manual testing
  (window as any).runBrowserTest = async () => {
    const tester = new BrowserTester()
    const results = await tester.runFullTest()
    console.log('Browser Test Results:', results)
    console.log('\n' + tester.generateReport())
    return results
  }
  
  console.log('🧪 Browser testing available. Run `runBrowserTest()` in console.')
} 