// Favorites Section Component
'use client'

import { useState } from 'react'
import { 
  Heart, 
  Play, 
  MoreHorizontal, 
  RefreshCw,
  Music,
  HeartOff,
  Clock,
  Eye,
  Shuffle
} from 'lucide-react'
import { FavoriteTrack } from '@/types/dashboard'
import { useAudio } from '@/contexts/AudioContext'
import { convertTracksToAudioTracks } from '@/lib/audio-utils'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { EmptyState } from '@/components/ui/EmptyState'

interface FavoritesSectionProps {
  favorites: FavoriteTrack[]
  loading: boolean
  error: string | null
  hasMore: boolean
  onLoadMore: () => void
  onRefresh: () => void
  onToggleLike: (trackId: string) => Promise<{ success: boolean; liked: boolean; error: string | null }>
}

export default function FavoritesSection({
  favorites,
  loading,
  error,
  hasMore,
  onLoadMore,
  onRefresh,
  onToggleLike
}: FavoritesSectionProps) {
  const { setQueue } = useAudio()
  const [likingTracks, setLikingTracks] = useState<Set<string>>(new Set())
  const [actionErrors, setActionErrors] = useState<Record<string, string>>({})

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getAIToolIcon = (tool: string) => {
    const icons: Record<string, string> = {
      suno: '🎵',
      udio: '🎶',
      mubert: '🎼',
      aiva: '🎹',
      amper: '🎸',
      custom: '⚡',
      other: '🎧'
    }
    return icons[tool] || '🎧'
  }

  const handlePlayTrack = (track: FavoriteTrack) => {
    try {
      const audioTracks = convertTracksToAudioTracks([track])
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        // Clear any previous errors
        setActionErrors(prev => ({ ...prev, [`play-${track.id}`]: '' }))
      } else {
        setActionErrors(prev => ({ ...prev, [`play-${track.id}`]: 'Unable to play this track' }))
      }
    } catch (error) {
      setActionErrors(prev => ({ ...prev, [`play-${track.id}`]: 'Failed to play track' }))
    }
  }

  const handlePlayAll = () => {
    try {
      const audioTracks = convertTracksToAudioTracks(favorites)
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        setActionErrors(prev => ({ ...prev, 'play-all': '' }))
      } else {
        setActionErrors(prev => ({ ...prev, 'play-all': 'No tracks available to play' }))
      }
    } catch (error) {
      setActionErrors(prev => ({ ...prev, 'play-all': 'Failed to play tracks' }))
    }
  }

  const handleShuffle = () => {
    try {
      const shuffled = [...favorites].sort(() => Math.random() - 0.5)
      const audioTracks = convertTracksToAudioTracks(shuffled)
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        setActionErrors(prev => ({ ...prev, 'shuffle': '' }))
      } else {
        setActionErrors(prev => ({ ...prev, 'shuffle': 'No tracks available to shuffle' }))
      }
    } catch (error) {
      setActionErrors(prev => ({ ...prev, 'shuffle': 'Failed to shuffle tracks' }))
    }
  }

  const handleUnlike = async (trackId: string) => {
    if (likingTracks.has(trackId)) return

    setLikingTracks(prev => new Set(prev).add(trackId))
    setActionErrors(prev => ({ ...prev, [`unlike-${trackId}`]: '' }))
    
    try {
      const result = await onToggleLike(trackId)
      if (!result.success) {
        setActionErrors(prev => ({ 
          ...prev, 
          [`unlike-${trackId}`]: result.error || 'Failed to unlike track' 
        }))
      }
    } catch (error) {
      setActionErrors(prev => ({ 
        ...prev, 
        [`unlike-${trackId}`]: error instanceof Error ? error.message : 'Failed to unlike track' 
      }))
    } finally {
      setLikingTracks(prev => {
        const newSet = new Set(prev)
        newSet.delete(trackId)
        return newSet
      })
    }
  }

  // Loading state
  if (loading && favorites.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Heart className="w-5 h-5 text-red-400" />
            Favorite Tracks
          </h2>
          <LoadingSpinner size="sm" />
        </div>
        
        <div className="space-y-4" role="status" aria-label="Loading favorite tracks">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center gap-4 p-4 bg-gray-700 rounded-lg animate-pulse">
              <div className="w-12 h-12 bg-gray-600 rounded-lg" />
              <div className="flex-1">
                <div className="h-4 bg-gray-600 rounded w-3/4 mb-2" />
                <div className="h-3 bg-gray-600 rounded w-1/2" />
              </div>
              <div className="w-16 h-8 bg-gray-600 rounded" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Heart className="w-5 h-5 text-red-400" />
            Favorite Tracks
          </h2>
        </div>
        
        <ErrorMessage
          message={error}
          onRetry={onRefresh}
          retryLabel="Reload Favorites"
          severity="error"
          className="max-w-2xl"
        />
      </div>
    )
  }

  // Empty state
  if (favorites.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Heart className="w-5 h-5 text-red-400" />
            Favorite Tracks
          </h2>
        </div>
        
        <EmptyState
          icon={Heart}
          title="No Favorite Tracks Yet"
          description="Start liking tracks to build your favorites collection!"
          action={{
            label: "Browse Music",
            href: "/browse"
          }}
        />
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white flex items-center gap-2">
          <Heart className="w-5 h-5 text-red-400" />
          Favorite Tracks
          <span className="text-sm text-gray-400 font-normal">({favorites.length})</span>
        </h2>
        
        <div className="flex items-center gap-3">
          {/* Action Buttons */}
          <button
            onClick={handlePlayAll}
            disabled={loading || favorites.length === 0}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={`Play all ${favorites.length} favorite tracks`}
          >
            <Play className="w-4 h-4" />
            Play All
          </button>

          <button
            onClick={handleShuffle}
            disabled={loading || favorites.length === 0}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={`Shuffle ${favorites.length} favorite tracks`}
          >
            <Shuffle className="w-4 h-4" />
            Shuffle
          </button>

          {/* Refresh Button */}
          <button
            onClick={onRefresh}
            disabled={loading}
            className="text-gray-400 hover:text-white transition-colors disabled:opacity-50"
            aria-label="Refresh favorite tracks"
            title="Refresh favorite tracks"
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Action Errors */}
      {Object.entries(actionErrors).map(([key, error]) => 
        error && (
          <ErrorMessage
            key={key}
            message={error}
            severity="warning"
            onDismiss={() => setActionErrors(prev => ({ ...prev, [key]: '' }))}
            className="mb-4"
          />
        )
      )}

      {/* Favorites List */}
      <div className="space-y-3" role="list" aria-label="Favorite tracks">
        {favorites.map((track) => (
          <div
            key={track.id}
            className="flex items-center gap-4 p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors group"
            role="listitem"
          >
            {/* Track Cover & Play Button */}
            <div className="relative w-12 h-12 flex-shrink-0">
              <div 
                className="w-full h-full bg-gradient-to-br from-red-500 to-pink-500 rounded-lg flex items-center justify-center"
                aria-label={`Album art for ${track.title}`}
              >
                <span className="text-white text-lg" role="img" aria-label={`Generated with ${track.ai_tool}`}>
                  {getAIToolIcon(track.ai_tool)}
                </span>
              </div>
              <button
                onClick={() => handlePlayTrack(track)}
                className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-purple-500"
                aria-label={`Play ${track.title} by ${track.artist_name}`}
                title={`Play ${track.title}`}
              >
                <Play className="w-5 h-5 text-white" />
              </button>
            </div>

            {/* Track Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-white truncate" title={track.title}>{track.title}</h4>
                {track.is_featured && (
                  <span 
                    className="bg-yellow-600 text-white px-2 py-0.5 rounded text-xs font-medium"
                    aria-label="Featured track"
                  >
                    Featured
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <span title={`Artist: ${track.artist_name}`}>{track.artist_name}</span>
                <span aria-hidden="true">•</span>
                <span className="capitalize" title={`Genre: ${track.genre}`}>{track.genre}</span>
                <span aria-hidden="true">•</span>
                <span title={`Duration: ${formatDuration(track.duration || 0)}`}>{formatDuration(track.duration || 0)}</span>
                <span aria-hidden="true">•</span>
                <span title={`Liked ${formatTimeAgo(track.liked_at)}`}>Liked {formatTimeAgo(track.liked_at)}</span>
              </div>
            </div>

            {/* Stats */}
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-1" title={`${track.play_count || 0} plays`}>
                <Eye className="w-4 h-4" aria-hidden="true" />
                <span aria-label={`${track.play_count || 0} plays`}>{track.play_count || 0}</span>
              </div>
              <div className="flex items-center gap-1" title={`${track.like_count || 0} likes`}>
                <Heart className="w-4 h-4" aria-hidden="true" />
                <span aria-label={`${track.like_count || 0} likes`}>{track.like_count || 0}</span>
              </div>
            </div>

            {/* Unlike Button */}
            <button
              onClick={() => handleUnlike(track.id)}
              disabled={likingTracks.has(track.id)}
              className="text-red-400 hover:text-red-300 transition-colors disabled:opacity-50 opacity-0 group-hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-red-500 rounded"
              aria-label={`Remove ${track.title} from favorites`}
              title="Remove from favorites"
            >
              {likingTracks.has(track.id) ? (
                <Clock className="w-5 h-5 animate-spin" aria-label="Removing from favorites..." />
              ) : (
                <HeartOff className="w-5 h-5" />
              )}
            </button>

            {/* More Options */}
            <button 
              className="text-gray-400 hover:text-white transition-colors opacity-0 group-hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded"
              aria-label={`More options for ${track.title}`}
              title="More options"
            >
              <MoreHorizontal className="w-5 h-5" />
            </button>
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center mt-6">
          <button
            onClick={onLoadMore}
            disabled={loading}
            className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
            aria-label={`Load more favorite tracks. Currently showing ${favorites.length} tracks.`}
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="inline mr-2" />
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </button>
        </div>
      )}

      {/* Favorites Summary */}
      <div className="mt-6 pt-6 border-t border-gray-700">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-red-400">
              {favorites.length}
            </div>
            <div className="text-sm text-gray-400">Total Favorites</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">
              {Math.round(favorites.reduce((sum, track) => sum + (track.duration || 0), 0) / 60)}
            </div>
            <div className="text-sm text-gray-400">Minutes</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">
              {new Set(favorites.map(track => track.genre)).size}
            </div>
            <div className="text-sm text-gray-400">Genres</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">
              {new Set(favorites.map(track => track.ai_tool)).size}
            </div>
            <div className="text-sm text-gray-400">AI Tools</div>
          </div>
        </div>
      </div>

      {/* Top Genres */}
      {favorites.length > 0 && (
        <div className="mt-6 pt-6 border-t border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4">Your Favorite Genres</h3>
          <div className="flex flex-wrap gap-2">
            {Object.entries(
              favorites.reduce((acc, track) => {
                const genre = track.genre || 'unknown'
                acc[genre] = (acc[genre] || 0) + 1
                return acc
              }, {} as Record<string, number>)
            )
              .sort(([, a], [, b]) => b - a)
              .slice(0, 6)
              .map(([genre, count]) => (
                <span
                  key={genre}
                  className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium"
                >
                  {genre} ({count})
                </span>
              ))}
          </div>
        </div>
      )}
    </div>
  )
} 