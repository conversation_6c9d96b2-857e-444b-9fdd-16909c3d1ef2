# Context7 MCP Server Setup for Tunami

Context7 is a powerful MCP (Model Context Protocol) server that provides up-to-date documentation and code examples for libraries directly in your AI coding assistant. This eliminates outdated code suggestions and hallucinated APIs.

## 🚀 Quick Setup

### Automatic Setup (Recommended)

```bash
cd mcp-servers
npm run setup:context7
```

This will automatically configure Context7 for Cursor IDE.

### Manual Setup

If you prefer manual configuration or use a different editor:

#### For Cursor IDE

Add to `~/.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "description": "Up-to-date documentation and code examples for libraries"
    }
  }
}
```

#### For VS Code (with MCP extension)

Add to your VS Code `settings.json`:

```json
{
  "mcp.servers": {
    "Context7": {
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

#### For Claude Desktop

Add to `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "Context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

## 🎯 How to Use Context7

Simply add `use context7` to any prompt where you want up-to-date documentation:

### Tunami-Specific Examples

```
Create a Supabase auth component for Next.js 14. use context7

Build a music player component with TypeScript and React hooks. use context7

Show me how to implement real-time subscriptions with Supabase. use context7

Create a playlist management system using Supabase and React. use context7

Write a Next.js API route for file uploads with proper TypeScript types. use context7
```

### General Development Examples

```
Create a React component with useState and useEffect. use context7

Show me how to use TailwindCSS with Next.js 14. use context7

Write a PostgreSQL query with proper TypeScript types. use context7

Implement authentication middleware in Next.js. use context7
```

## 🔧 Alternative Runtime Configurations

If you encounter issues with `npx`, try these alternatives:

### Using Bun

```json
{
  "mcpServers": {
    "context7": {
      "command": "bunx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

### Using Deno

```json
{
  "mcpServers": {
    "context7": {
      "command": "deno",
      "args": ["run", "--allow-net", "npm:@upstash/context7-mcp"]
    }
  }
}
```

## 🛠️ Troubleshooting

### Common Issues

1. **ERR_MODULE_NOT_FOUND**

   - Solution: Use `bunx` instead of `npx`
   - Update your config to use `"command": "bunx"`

2. **Permission Errors**

   - Ensure Node.js v18+ is installed
   - Try running with elevated permissions

3. **Context7 Not Responding**
   - Restart your IDE after configuration
   - Check that the MCP configuration is valid JSON
   - Verify Node.js version: `node --version`

### Testing Context7

To test if Context7 is working:

1. Restart your IDE
2. Try a simple prompt: `Show me how to create a React component. use context7`
3. Look for up-to-date React documentation in the response

## 🎵 Tunami Integration Benefits

Context7 is particularly valuable for the Tunami project because it provides:

- **Up-to-date Next.js 14 documentation** with App Router examples
- **Current Supabase API references** for auth, database, and real-time features
- **Latest TypeScript patterns** for type-safe development
- **Modern React patterns** including hooks and server components
- **TailwindCSS best practices** for responsive design

## 📚 Available Tools

Context7 provides these tools that work behind the scenes:

### `resolve-library-id`

Converts library names to Context7-compatible IDs.

### `get-library-docs`

Fetches documentation with parameters:

- `context7CompatibleLibraryID` (required)
- `topic` (optional): Focus on specific topics like "hooks", "routing"
- `tokens` (optional): Limit response size (default: 10000)

## 🔄 Integration with Other Tunami MCP Servers

Context7 works seamlessly with your existing Tunami MCP servers:

```
Use tunami-memory to store user preferences and context7 for React state management examples.

Fetch song metadata with tunami-fetch and show me how to display it using modern React patterns. use context7

Create a playlist component that uses tunami-postgres for data and context7 for TypeScript types.
```

## 📖 Environment Variables

Context7 supports these optional environment variables:

```bash
# Set minimum token count for documentation retrieval
DEFAULT_MINIMUM_TOKENS=10000
```

Add to your MCP configuration:

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "env": {
        "DEFAULT_MINIMUM_TOKENS": "6000"
      }
    }
  }
}
```

## 🚀 Next Steps

1. **Restart your IDE** after setup
2. **Test Context7** with a simple prompt
3. **Integrate with Tunami development** using the examples above
4. **Combine with other MCP servers** for enhanced functionality

## 📞 Support

If you encounter issues:

1. Check the [Context7 GitHub repository](https://github.com/upstash/context7)
2. Verify your Node.js version is 18+
3. Try alternative runtime configurations
4. Check IDE-specific MCP documentation

---

**Happy coding with up-to-date documentation! 🎵✨**
