'use client'

import { useState } from 'react'
import { Play, Pause, Heart, MoreHorizontal, Music } from 'lucide-react'
import { AudioTrack } from '@/types/audio'
import { useMobilePlayer } from '@/contexts/MobilePlayerContext'
import { useMobileDetection } from '@/hooks/useMobileDetection'
import { Button } from '@/components/ui/Button'

interface MobileTrackCardProps {
  track: AudioTrack
  playlist?: AudioTrack[]
  index?: number
  showArtwork?: boolean
  variant?: 'compact' | 'full'
  onLike?: () => void
  onShare?: () => void
  onAddToPlaylist?: () => void
}

export default function MobileTrackCard({
  track,
  playlist = [],
  index = 0,
  showArtwork = true,
  variant = 'full',
  onLike,
  onShare,
  onAddToPlaylist
}: MobileTrackCardProps) {
  const [isLiked, setIsLiked] = useState(false)
  const mobileDetection = useMobileDetection()
  const player = useMobilePlayer()

  const isCurrentTrack = player.currentTrack?.id === track.id
  const isPlaying = isCurrentTrack && player.isPlaying

  const handlePlayPause = () => {
    if (isCurrentTrack) {
      if (isPlaying) {
        player.pauseTrack()
      } else {
        player.playTrack(track)
      }
    } else {
      // Set new track and playlist
      player.setCurrentTrack(track, playlist.length > 0 ? playlist : [track], index)
      player.playTrack(track)
    }
  }

  const handleTrackClick = () => {
    if (mobileDetection.isMobile) {
      // On mobile, clicking the track should expand the player if it's the current track
      if (isCurrentTrack) {
        player.toggleExpanded()
      } else {
        handlePlayPause()
      }
    } else {
      handlePlayPause()
    }
  }

  const handleLike = () => {
    setIsLiked(!isLiked)
    onLike?.()
  }

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (variant === 'compact') {
    return (
      <div className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors">
        {/* Play button */}
        <Button
          onClick={handlePlayPause}
          className="w-12 h-12 rounded-full bg-purple-600 hover:bg-purple-700 flex items-center justify-center flex-shrink-0"
          size="sm"
        >
          {isPlaying ? (
            <Pause className="w-5 h-5 text-white" />
          ) : (
            <Play className="w-5 h-5 text-white ml-0.5" />
          )}
        </Button>

        {/* Track info */}
        <div 
          className="flex-1 min-w-0 cursor-pointer"
          onClick={handleTrackClick}
        >
          <h3 className={`font-medium text-sm truncate ${isCurrentTrack ? 'text-purple-400' : 'text-white'}`}>
            {track.title}
          </h3>
          <p className="text-gray-400 text-xs truncate">{track.artist}</p>
        </div>

        {/* Duration */}
        <span className="text-gray-400 text-xs flex-shrink-0">
          {formatDuration(track.duration || 0)}
        </span>
      </div>
    )
  }

  return (
    <div className="bg-gray-800/50 rounded-lg border border-gray-700 hover:border-gray-600 transition-all duration-200 overflow-hidden">
      {/* Track artwork and play button */}
      <div className="relative">
        {showArtwork && (
          <div 
            className="aspect-square bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center cursor-pointer"
            onClick={handleTrackClick}
          >
            <Music className="w-16 h-16 text-white opacity-80" />
            
            {/* Play overlay */}
            <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
              <Button
                onClick={(e) => {
                  e.stopPropagation()
                  handlePlayPause()
                }}
                className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30 flex items-center justify-center"
                size="sm"
              >
                {isPlaying ? (
                  <Pause className="w-8 h-8 text-white" />
                ) : (
                  <Play className="w-8 h-8 text-white ml-1" />
                )}
              </Button>
            </div>

            {/* Current track indicator */}
            {isCurrentTrack && (
              <div className="absolute top-2 right-2 w-3 h-3 bg-purple-400 rounded-full animate-pulse" />
            )}
          </div>
        )}
      </div>

      {/* Track info and controls */}
      <div className="p-4">
        <div 
          className="cursor-pointer mb-3"
          onClick={handleTrackClick}
        >
          <h3 className={`font-semibold text-lg mb-1 truncate ${isCurrentTrack ? 'text-purple-400' : 'text-white'}`}>
            {track.title}
          </h3>
          <p className="text-gray-400 text-sm truncate mb-2">{track.artist}</p>
          
          {track.aiTool && (
            <div className="flex items-center space-x-2 mb-2">
              <span className="px-2 py-1 bg-purple-600/20 text-purple-400 text-xs rounded-full">
                {track.aiTool}
              </span>
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              onClick={handlePlayPause}
              className="w-10 h-10 rounded-full bg-purple-600 hover:bg-purple-700 flex items-center justify-center"
              size="sm"
            >
              {isPlaying ? (
                <Pause className="w-5 h-5 text-white" />
              ) : (
                <Play className="w-5 h-5 text-white ml-0.5" />
              )}
            </Button>

            <Button
              onClick={handleLike}
              variant="ghost"
              size="sm"
              className={`w-10 h-10 ${isLiked ? 'text-red-400' : 'text-gray-400'}`}
            >
              <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-gray-400 text-sm">
              {formatDuration(track.duration || 0)}
            </span>
            
            <Button
              onClick={() => {/* TODO: More options */}}
              variant="ghost"
              size="sm"
              className="w-10 h-10 text-gray-400"
            >
              <MoreHorizontal className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 