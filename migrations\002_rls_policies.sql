-- Migration 002: Row Level Security Policies
-- Date: 2025-01-24
-- Description: Enable RLS and create security policies for all tables

-- =============================================
-- 1. ENA<PERSON><PERSON> RLS ON ALL TABLES
-- =============================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tracks ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlist_tracks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_agreements ENABLE ROW LEVEL SECURITY;

-- =============================================
-- 2. PROFILES POLICIES
-- =============================================

CREATE POLICY "Users can view their own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- =============================================
-- 3. TRACKS POLICIES
-- =============================================

CREATE POLICY "Public tracks are viewable by everyone"
  ON tracks FOR SELECT
  USING (is_public = true OR uploaded_by = auth.uid());

CREATE POLICY "Users can insert their own tracks"
  ON tracks FOR INSERT
  WITH CHECK (uploaded_by = auth.uid());

CREATE POLICY "Users can update their own tracks"
  ON tracks FOR UPDATE
  USING (uploaded_by = auth.uid());

CREATE POLICY "Users can delete their own tracks"
  ON tracks FOR DELETE
  USING (uploaded_by = auth.uid());

-- =============================================
-- 4. PLAYLISTS POLICIES
-- =============================================

CREATE POLICY "Public playlists are viewable by everyone"
  ON playlists FOR SELECT
  USING (is_public = true OR user_id = auth.uid());

CREATE POLICY "Users can manage their own playlists"
  ON playlists FOR ALL
  USING (user_id = auth.uid());

-- =============================================
-- 5. PLAYLIST TRACKS POLICIES
-- =============================================

CREATE POLICY "Playlist tracks viewable based on playlist access"
  ON playlist_tracks FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM playlists 
      WHERE playlists.id = playlist_tracks.playlist_id 
      AND (playlists.is_public = true OR playlists.user_id = auth.uid())
    )
  );

CREATE POLICY "Users can manage tracks in their playlists"
  ON playlist_tracks FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM playlists 
      WHERE playlists.id = playlist_tracks.playlist_id 
      AND playlists.user_id = auth.uid()
    )
  );

-- =============================================
-- 6. USER AGREEMENTS POLICIES
-- =============================================

CREATE POLICY "Users can view their own agreements"
  ON user_agreements FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own agreements"
  ON user_agreements FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- =============================================
-- 7. STORAGE POLICIES
-- =============================================

-- Audio tracks storage policies
CREATE POLICY "Audio files are publicly viewable"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'audio-tracks');

CREATE POLICY "Users can upload their own audio files"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'audio-tracks' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own audio files"
  ON storage.objects FOR UPDATE
  USING (
    bucket_id = 'audio-tracks' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own audio files"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'audio-tracks' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Playlist covers storage policies
CREATE POLICY "Playlist covers are publicly viewable"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'playlist-covers');

CREATE POLICY "Users can upload playlist covers"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'playlist-covers' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Profile avatars storage policies
CREATE POLICY "Profile avatars are publicly viewable"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'profile-avatars');

CREATE POLICY "Users can upload their own avatar"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'profile-avatars' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  ); 