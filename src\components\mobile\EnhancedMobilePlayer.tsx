'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { 
  Play, 
  Pause, 
  SkipB<PERSON>, 
  Ski<PERSON>Forward, 
  Heart,
  MoreHorizontal,
  ChevronDown,
  Share2,
  Volume2,
  VolumeX,
  Music,
  Shuffle,
  Repeat,
  Repeat1,
  Loader2
} from 'lucide-react'
import { AudioTrack } from '@/types/audio'
import { useMobileDetection } from '@/hooks/useMobileDetection'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { useSwipeGestures } from '@/hooks/useSwipeGestures'
import { useOrientation } from '@/hooks/useOrientation'
import { useMobileMediaSession } from '@/hooks/useMobileMediaSession'
import { useMobileNotifications } from '@/hooks/useMobileNotifications'
import { useBackgroundAudio } from '@/hooks/useBackgroundAudio'
import { Avatar } from '@/components/ui/Avatar'
import { Button } from '@/components/ui/Button'
import { playMobileAudio, createMobileAudioElement } from '@/utils/mobileAudio'

interface EnhancedMobilePlayerProps {
  track?: AudioTrack
  playlist?: AudioTrack[]
  currentIndex?: number
  isVisible?: boolean
  isExpanded?: boolean
  autoPlay?: boolean
  onPlay?: () => void
  onPause?: () => void
  onNext?: () => void
  onPrevious?: () => void
  onToggleExpanded?: () => void
  onToggleLike?: () => void
  onShare?: () => void
  onClose?: () => void
}

interface PlayerState {
  isPlaying: boolean
  isLoading: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean
  isLiked: boolean
  shuffleMode: boolean
  repeatMode: 'none' | 'all' | 'one'
  bufferedPercentage: number
}

export default function EnhancedMobilePlayer({
  track,
  playlist = [],
  currentIndex = 0,
  isVisible = true,
  isExpanded = false,
  autoPlay = false,
  onPlay,
  onPause,
  onNext,
  onPrevious,
  onToggleExpanded,
  onToggleLike,
  onShare,
  onClose
}: EnhancedMobilePlayerProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const progressBarRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<HTMLDivElement>(null)
  
  const mobileDetection = useMobileDetection()
  const networkStatus = useNetworkStatus()
  const orientation = useOrientation()
  
  const [state, setState] = useState<PlayerState>({
    isPlaying: false,
    isLoading: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    isLiked: false,
    shuffleMode: false,
    repeatMode: 'none',
    bufferedPercentage: 0
  })

  const [dragState, setDragState] = useState({
    isDragging: false,
    startY: 0,
    startHeight: 0
  })

  // Initialize mobile audio element
  useEffect(() => {
    if (!track) return

    setState(prev => ({ ...prev, isLoading: true }))

    const audio = createMobileAudioElement(
      track.src,
      networkStatus,
      () => setState(prev => ({ ...prev, isLoading: false })),
      (error) => {
        console.error('Audio loading error:', error)
        setState(prev => ({ ...prev, isLoading: false }))
      }
    )

    audioRef.current = audio

    // Audio event listeners
    const handleTimeUpdate = () => {
      if (audio) {
        setState(prev => ({ ...prev, currentTime: audio.currentTime }))
        
        // Update buffered percentage
        if (audio.buffered.length > 0) {
          const buffered = audio.buffered.end(audio.buffered.length - 1)
          const percentage = (buffered / audio.duration) * 100
          setState(prev => ({ ...prev, bufferedPercentage: percentage }))
        }
      }
    }

    const handleLoadedMetadata = () => {
      if (audio) {
        setState(prev => ({ ...prev, duration: audio.duration }))
      }
    }

    const handleEnded = () => {
      setState(prev => ({ ...prev, isPlaying: false }))
      handleNext()
    }

    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('ended', handleEnded)

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('ended', handleEnded)
      audio.pause()
    }
  }, [track])

  // Media Session API for lock screen controls
  useMobileMediaSession({
    track,
    isPlaying: state.isPlaying,
    onPlay: handlePlay,
    onPause: handlePause,
    onNext: handleNext,
    onPrevious: handlePrevious,
    onSeek: handleSeek
  })

  // Mobile notifications
  useMobileNotifications({
    track,
    isPlaying: state.isPlaying,
    onPlay: handlePlay,
    onPause: handlePause,
    onNext: handleNext,
    onPrevious: handlePrevious
  })

  // Background audio support
  useBackgroundAudio({
    audioRef,
    isPlaying: state.isPlaying
  })

  // Swipe gestures for track navigation
  const swipeHandlers = useSwipeGestures({
    onSwipeLeft: handleNext,
    onSwipeRight: handlePrevious,
    onSwipeUp: () => onToggleExpanded?.(),
    onSwipeDown: () => isExpanded && onToggleExpanded?.(),
    threshold: 50,
    enabled: isVisible
  })

  // Play/Pause functionality
  const handlePlay = useCallback(async () => {
    if (!audioRef.current || !track) return

    try {
      await playMobileAudio(audioRef.current, true)
      setState(prev => ({ ...prev, isPlaying: true }))
      onPlay?.()
    } catch (error) {
      console.error('Play failed:', error)
    }
  }, [track, onPlay])

  const handlePause = useCallback(() => {
    if (!audioRef.current) return

    audioRef.current.pause()
    setState(prev => ({ ...prev, isPlaying: false }))
    onPause?.()
  }, [onPause])

  const togglePlayPause = useCallback(() => {
    if (state.isPlaying) {
      handlePause()
    } else {
      handlePlay()
    }
  }, [state.isPlaying, handlePlay, handlePause])

  // Navigation
  const handleNext = useCallback(() => {
    onNext?.()
  }, [onNext])

  const handlePrevious = useCallback(() => {
    onPrevious?.()
  }, [onPrevious])

  // Seek functionality
  const handleSeek = useCallback((time: number) => {
    if (!audioRef.current) return

    audioRef.current.currentTime = time
    setState(prev => ({ ...prev, currentTime: time }))
  }, [])

  const handleProgressClick = useCallback((event: React.TouchEvent | React.MouseEvent) => {
    if (!progressBarRef.current || !state.duration) return

    const rect = progressBarRef.current.getBoundingClientRect()
    const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
    const percentage = (clientX - rect.left) / rect.width
    const time = percentage * state.duration

    handleSeek(time)
  }, [state.duration, handleSeek])

  // Volume control
  const toggleMute = useCallback(() => {
    if (!audioRef.current) return

    const newMuted = !state.isMuted
    audioRef.current.muted = newMuted
    setState(prev => ({ ...prev, isMuted: newMuted }))
  }, [state.isMuted])

  // Playback modes
  const toggleShuffle = useCallback(() => {
    setState(prev => ({ ...prev, shuffleMode: !prev.shuffleMode }))
  }, [])

  const toggleRepeat = useCallback(() => {
    setState(prev => ({
      ...prev,
      repeatMode: prev.repeatMode === 'none' ? 'all' : 
                  prev.repeatMode === 'all' ? 'one' : 'none'
    }))
  }, [])

  const toggleLike = useCallback(() => {
    setState(prev => ({ ...prev, isLiked: !prev.isLiked }))
    onToggleLike?.()
  }, [onToggleLike])

  // Drag to close functionality
  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (!isExpanded) return

    const touch = event.touches[0]
    setDragState({
      isDragging: true,
      startY: touch.clientY,
      startHeight: window.innerHeight
    })
  }, [isExpanded])

  const handleTouchMove = useCallback((event: React.TouchEvent) => {
    if (!dragState.isDragging || !isExpanded) return

    const touch = event.touches[0]
    const deltaY = touch.clientY - dragState.startY
    
    if (deltaY > 0) {
      // Only allow dragging down
      const opacity = Math.max(0, 1 - (deltaY / 200))
      if (playerRef.current) {
        playerRef.current.style.transform = `translateY(${deltaY}px)`
        playerRef.current.style.opacity = opacity.toString()
      }
    }
  }, [dragState, isExpanded])

  const handleTouchEnd = useCallback((event: React.TouchEvent) => {
    if (!dragState.isDragging) return

    const touch = event.changedTouches[0]
    const deltaY = touch.clientY - dragState.startY

    if (deltaY > 100) {
      // Close player if dragged down enough
      onToggleExpanded?.()
    }

    // Reset transform
    if (playerRef.current) {
      playerRef.current.style.transform = ''
      playerRef.current.style.opacity = ''
    }

    setDragState({ isDragging: false, startY: 0, startHeight: 0 })
  }, [dragState, onToggleExpanded])

  // Format time helper
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const progressPercentage = state.duration > 0 ? (state.currentTime / state.duration) * 100 : 0

  if (!isVisible || !track) return null

  // Compact mini player view
  if (!isExpanded) {
    return (
      <div
        className="fixed bottom-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-t border-gray-800"
        style={{ paddingBottom: 'env(safe-area-inset-bottom)' }}
        {...swipeHandlers}
      >
        <div className="flex items-center p-3 space-x-3">
          {/* Track artwork */}
          <div 
            className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0 cursor-pointer"
            onClick={onToggleExpanded}
          >
            <Music className="w-6 h-6 text-white" />
          </div>

          {/* Track info */}
          <div 
            className="flex-1 min-w-0 cursor-pointer"
            onClick={onToggleExpanded}
          >
            <h4 className="text-white font-medium truncate text-sm">{track.title}</h4>
            <p className="text-gray-400 text-xs truncate">{track.artist}</p>
          </div>

          {/* Mini controls */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={togglePlayPause}
              className="w-10 h-10 p-0"
            >
              {state.isLoading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : state.isPlaying ? (
                <Pause className="w-5 h-5" />
              ) : (
                <Play className="w-5 h-5" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleNext}
              className="w-10 h-10 p-0"
            >
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Mini progress bar */}
        <div className="h-1 bg-gray-800">
          <div 
            className="h-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-200"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    )
  }

  // Full expanded player view
  return (
    <div
      ref={playerRef}
      className={`fixed inset-0 z-50 bg-gradient-to-br from-gray-900 via-purple-900/20 to-blue-900/20 ${
        orientation.isLandscape ? 'flex' : 'flex flex-col'
      }`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      {...swipeHandlers}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 pt-8 flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleExpanded}
          className="w-10 h-10 p-0"
        >
          <ChevronDown className="w-6 h-6" />
        </Button>

        <div className="text-center flex-1">
          <p className="text-gray-400 text-sm">Playing from</p>
          <h3 className="text-white font-medium">Playlist</h3>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => {/* TODO: More options */}}
          className="w-10 h-10 p-0"
        >
          <MoreHorizontal className="w-6 h-6" />
        </Button>
      </div>

      <div className={`flex-1 ${orientation.isLandscape ? 'flex items-center space-x-8 px-8' : 'flex flex-col'}`}>
        {/* Track artwork */}
        <div className={`${orientation.isLandscape ? 'w-80 h-80' : 'flex-1 flex items-center justify-center px-8'}`}>
          <div 
            className={`${orientation.isLandscape ? 'w-full h-full' : 'w-80 h-80 max-w-full max-h-full'} 
              bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-2xl`}
          >
            <Music className="w-32 h-32 text-white opacity-80" />
          </div>
        </div>

        {/* Controls section */}
        <div className={`${orientation.isLandscape ? 'flex-1' : 'px-6 pb-8'}`}>
          {/* Track info */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-white mb-2">{track.title}</h2>
            <p className="text-gray-400 text-lg mb-1">{track.artist}</p>
            {track.aiTool && (
              <p className="text-purple-400 text-sm">Generated with {track.aiTool}</p>
            )}
          </div>

          {/* Progress bar */}
          <div className="mb-8">
            <div 
              ref={progressBarRef}
              onTouchStart={handleProgressClick}
              onMouseDown={handleProgressClick}
              className="relative w-full h-2 bg-gray-700 rounded-full cursor-pointer touch-manipulation mb-2"
            >
              {/* Buffered progress */}
              <div 
                className="absolute top-0 left-0 h-full bg-gray-600 rounded-full"
                style={{ width: `${state.bufferedPercentage}%` }}
              />
              
              {/* Current progress */}
              <div 
                className="absolute top-0 left-0 h-full bg-white rounded-full"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            
            <div className="flex justify-between text-sm text-gray-400">
              <span>{formatTime(state.currentTime)}</span>
              <span>{formatTime(state.duration)}</span>
            </div>
          </div>

          {/* Main controls */}
          <div className="flex items-center justify-center space-x-8 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevious}
              className="w-12 h-12 p-0"
            >
              <SkipBack className="w-6 h-6" />
            </Button>

            <Button
              onClick={togglePlayPause}
              className="w-20 h-20 rounded-full bg-white hover:bg-gray-100 text-gray-900"
              disabled={state.isLoading}
            >
              {state.isLoading ? (
                <Loader2 className="w-8 h-8 animate-spin" />
              ) : state.isPlaying ? (
                <Pause className="w-8 h-8" />
              ) : (
                <Play className="w-8 h-8 ml-1" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleNext}
              className="w-12 h-12 p-0"
            >
              <SkipForward className="w-6 h-6" />
            </Button>
          </div>

          {/* Secondary controls */}
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleShuffle}
              className={`w-10 h-10 p-0 ${state.shuffleMode ? 'text-purple-400' : 'text-gray-400'}`}
            >
              <Shuffle className="w-5 h-5" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleLike}
              className={`w-10 h-10 p-0 ${state.isLiked ? 'text-red-400' : 'text-gray-400'}`}
            >
              <Heart className={`w-5 h-5 ${state.isLiked ? 'fill-current' : ''}`} />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onShare}
              className="w-10 h-10 p-0 text-gray-400"
            >
              <Share2 className="w-5 h-5" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleRepeat}
              className={`w-10 h-10 p-0 ${state.repeatMode !== 'none' ? 'text-purple-400' : 'text-gray-400'}`}
            >
              {state.repeatMode === 'one' ? (
                <Repeat1 className="w-5 h-5" />
              ) : (
                <Repeat className="w-5 h-5" />
              )}
            </Button>

            {!mobileDetection.isIOS && (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMute}
                className="w-10 h-10 p-0 text-gray-400"
              >
                {state.isMuted ? (
                  <VolumeX className="w-5 h-5" />
                ) : (
                  <Volume2 className="w-5 h-5" />
                )}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 