'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Home, 
  Search, 
  Music, 
  Heart, 
  User, 
  Upload,
  History,
  Settings,
  Menu,
  X,
  ListMusic,
  Users,
  TrendingUp,
  ChevronDown,
  ChevronRight,
  Plus,
  Headphones,
  Library,
  PlayCircle,
  Clock,
  BarChart3,
  Wifi,
  WifiOff
} from 'lucide-react'
import { useMobileDetection } from '@/hooks/useMobileDetection'
import { useSwipeGestures } from '@/hooks/useSwipeGestures'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'

interface NavItem {
  href: string
  icon: React.ComponentType<{ className?: string }>
  label: string
  badge?: number
  primary?: boolean
  children?: NavItem[]
  isCollapsible?: boolean
}

interface MobileNavigationProps {
  className?: string
  showLabels?: boolean
  variant?: 'bottom' | 'drawer'
  autoHide?: boolean
  hapticFeedback?: boolean
}

const primaryNavItems: NavItem[] = [
  { href: '/', icon: Home, label: 'Home', primary: true },
  { href: '/browse', icon: Search, label: 'Browse', primary: true },
  { href: '/feed', icon: TrendingUp, label: 'Feed', primary: true },
  { href: '/users', icon: Users, label: 'Users', primary: true },
  { href: '/profile', icon: User, label: 'Profile', primary: true }
]

const libraryNavItems: NavItem[] = [
  { 
    href: '/library', 
    icon: Library, 
    label: 'My Library', 
    isCollapsible: true,
    children: [
      { href: '/playlists', icon: ListMusic, label: 'Playlists' },
      { href: '/liked', icon: Heart, label: 'Liked Songs' },
      { href: '/history', icon: Clock, label: 'Recently Played' },
      { href: '/downloads', icon: Headphones, label: 'Downloads' }
    ]
  },
  { href: '/upload', icon: Upload, label: 'Upload' },
  { href: '/analytics', icon: BarChart3, label: 'Analytics' },
  { href: '/settings', icon: Settings, label: 'Settings' }
]

export default function MobileNavigation({ 
  className = '', 
  showLabels = true,
  variant = 'bottom',
  autoHide = true,
  hapticFeedback = true
}: MobileNavigationProps) {
  const pathname = usePathname()
  const mobileDetection = useMobileDetection()
  const networkStatus = useNetworkStatus()
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const drawerRef = useRef<HTMLDivElement>(null)

  // Auto-hide navigation on scroll (for bottom variant)
  useEffect(() => {
    if (variant !== 'bottom' || !autoHide) return

    const handleScroll = () => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down - hide nav
        setIsVisible(false)
      } else {
        // Scrolling up - show nav
        setIsVisible(true)
      }
      
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY, variant, autoHide])

  // Close drawer when route changes
  useEffect(() => {
    setIsDrawerOpen(false)
  }, [pathname])

  // Handle drawer swipe gestures
  const swipeHandlers = useSwipeGestures({
    onSwipeRight: () => {
      if (variant === 'drawer' && !isDrawerOpen) {
        setIsDrawerOpen(true)
      }
    },
    onSwipeLeft: () => {
      if (variant === 'drawer' && isDrawerOpen) {
        setIsDrawerOpen(false)
      }
    },
    threshold: 80,
    enabled: variant === 'drawer'
  })

  // Don't render on desktop
  if (mobileDetection.isDesktop) {
    return null
  }

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  const handleNavClick = (href: string) => {
    // Add haptic feedback if available and enabled
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10) // Light haptic feedback
    }
    
    // Close drawer if navigating
    if (variant === 'drawer') {
      setIsDrawerOpen(false)
    }
  }

  const toggleExpanded = (href: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(href)) {
      newExpanded.delete(href)
    } else {
      newExpanded.add(href)
    }
    setExpandedItems(newExpanded)
    
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(15) // Medium haptic feedback
    }
  }

  const renderNavItem = (item: NavItem, isChild: boolean = false) => {
    const Icon = item.icon
    const active = isActive(item.href)
    const expanded = expandedItems.has(item.href)
    const hasChildren = item.children && item.children.length > 0

    return (
      <div key={item.href}>
        <div className={`flex items-center ${isChild ? 'pl-6' : ''}`}>
          <Link
            href={hasChildren ? '#' : item.href}
            onClick={(e) => {
              if (hasChildren) {
                e.preventDefault()
                toggleExpanded(item.href)
              } else {
                handleNavClick(item.href)
              }
            }}
            className={`flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors flex-1 ${
              active && !hasChildren
                ? 'bg-purple-600 text-white'
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            }`}
          >
            <Icon className="w-5 h-5 flex-shrink-0" />
            <span className="font-medium">{item.label}</span>
            {item.badge && (
              <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[1.25rem] text-center">
                {item.badge > 99 ? '99+' : item.badge}
              </span>
            )}
          </Link>
          
          {hasChildren && (
            <button
              onClick={() => toggleExpanded(item.href)}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              {expanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>
          )}
        </div>

        {/* Children */}
        {hasChildren && expanded && (
          <div className="mt-1 space-y-1 animate-in slide-in-from-top-2 duration-200">
            {item.children?.map((child) => renderNavItem(child, true))}
          </div>
        )}
      </div>
    )
  }

  if (variant === 'drawer') {
    return (
      <ComponentErrorBoundary>
        {/* Edge swipe detector */}
        <div
          className="fixed top-0 left-0 w-4 h-full z-40 touch-none md:hidden"
          {...swipeHandlers}
        />

        {/* Drawer Toggle Button */}
        <button
          onClick={() => setIsDrawerOpen(!isDrawerOpen)}
          className="fixed top-4 left-4 z-50 w-12 h-12 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-xl flex items-center justify-center text-white hover:bg-gray-800 transition-all duration-200 shadow-lg md:hidden"
          aria-label="Toggle navigation menu"
        >
          {isDrawerOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
        </button>

        {/* Backdrop */}
        {isDrawerOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden backdrop-blur-sm"
            onClick={() => setIsDrawerOpen(false)}
          />
        )}

        {/* Drawer */}
        <nav
          ref={drawerRef}
          className={`fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-gray-900/95 backdrop-blur-xl border-r border-gray-700 z-50 transform transition-transform duration-300 ease-out md:hidden ${
            isDrawerOpen ? 'translate-x-0' : '-translate-x-full'
          } ${className}`}
          {...swipeHandlers}
        >
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="p-4 pt-16 border-b border-gray-800">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                  <Music className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">Tunami</h2>
                  <p className="text-xs text-gray-400">AI Music Platform</p>
                </div>
              </div>
              
              {/* Network status indicator */}
              <div className="flex items-center space-x-2 text-xs">
                {networkStatus.isOnline ? (
                  <>
                    <Wifi className="w-3 h-3 text-green-400" />
                    <span className="text-green-400">Online</span>
                    {networkStatus.downlink && (
                      <span className="text-gray-400">
                        • {networkStatus.downlink}Mbps
                      </span>
                    )}
                  </>
                ) : (
                  <>
                    <WifiOff className="w-3 h-3 text-red-400" />
                    <span className="text-red-400">Offline</span>
                  </>
                )}
              </div>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-4 space-y-6">
              {/* Primary Navigation */}
              <div>
                <p className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-3 px-3">
                  Main
                </p>
                <div className="space-y-1">
                  {primaryNavItems.map((item) => renderNavItem(item))}
                </div>
              </div>

              {/* Library Navigation */}
              <div>
                <p className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-3 px-3">
                  Library
                </p>
                <div className="space-y-1">
                  {libraryNavItems.map((item) => renderNavItem(item))}
                </div>
              </div>

              {/* Quick Actions */}
              <div>
                <p className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-3 px-3">
                  Quick Actions
                </p>
                <div className="space-y-2">
                  <button
                    onClick={() => handleNavClick('/upload')}
                    className="w-full flex items-center space-x-3 px-3 py-3 rounded-lg bg-purple-600 hover:bg-purple-700 text-white transition-colors"
                  >
                    <Plus className="w-5 h-5" />
                    <span className="font-medium">Upload Music</span>
                  </button>
                  
                  <button
                    onClick={() => handleNavClick('/playlists/create')}
                    className="w-full flex items-center space-x-3 px-3 py-3 rounded-lg bg-gray-800 hover:bg-gray-700 text-gray-300 transition-colors"
                  >
                    <PlayCircle className="w-5 h-5" />
                    <span className="font-medium">Create Playlist</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-800">
              <div className="text-xs text-gray-500 text-center">
                <p>© 2024 Tunami</p>
                <p>AI-Powered Music Platform</p>
              </div>
            </div>
          </div>
        </nav>
      </ComponentErrorBoundary>
    )
  }

  // Bottom navigation variant
  return (
    <ComponentErrorBoundary>
      <nav
        className={`fixed bottom-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-xl border-t border-gray-700 transition-transform duration-300 md:hidden ${
          isVisible ? 'translate-y-0' : 'translate-y-full'
        } ${className}`}
        style={{
          paddingBottom: 'max(0.5rem, env(safe-area-inset-bottom))',
        }}
      >
        <div className="flex items-center justify-around py-2 px-1">
          {primaryNavItems.map((item) => {
            const Icon = item.icon
            const active = isActive(item.href)
            
            return (
              <Link
                key={item.href}
                href={item.href}
                onClick={() => handleNavClick(item.href)}
                className={`flex flex-col items-center justify-center px-2 py-2 min-w-0 relative transition-all duration-200 rounded-lg ${
                  active 
                    ? 'text-purple-400 bg-purple-400/10 scale-105' 
                    : 'text-gray-400 hover:text-gray-300'
                }`}
                aria-label={item.label}
              >
                <div className="relative">
                  <Icon 
                    className={`w-6 h-6 transition-transform duration-200 ${
                      active ? 'scale-110' : 'scale-100'
                    }`} 
                  />
                  {item.badge && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium animate-pulse">
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}
                </div>
                
                {showLabels && (
                  <span 
                    className={`text-xs mt-1 truncate max-w-full transition-opacity duration-200 ${
                      active ? 'opacity-100 font-medium' : 'opacity-80'
                    }`}
                  >
                    {item.label}
                  </span>
                )}
                
                {/* Active indicator */}
                {active && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-purple-400 rounded-full" />
                )}
              </Link>
            )
          })}
        </div>
      </nav>
    </ComponentErrorBoundary>
  )
}

// Hook for programmatic navigation control
export function useMobileNavigation() {
  const [isVisible, setIsVisible] = useState(true)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  
  const hideNavigation = () => setIsVisible(false)
  const showNavigation = () => setIsVisible(true)
  const toggleNavigation = () => setIsVisible(prev => !prev)
  
  const openDrawer = () => setIsDrawerOpen(true)
  const closeDrawer = () => setIsDrawerOpen(false)
  const toggleDrawer = () => setIsDrawerOpen(prev => !prev)
  
  return {
    isVisible,
    hideNavigation,
    showNavigation,
    toggleNavigation,
    isDrawerOpen,
    openDrawer,
    closeDrawer,
    toggleDrawer
  }
}

// Navigation item types for external usage
export type { NavItem, MobileNavigationProps } 