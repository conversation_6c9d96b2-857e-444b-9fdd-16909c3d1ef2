// Add a simple inline loading state for buttons and small components
export function InlineLoading({ text = 'Loading...', className = '' }: { text?: string; className?: string }) {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin" />
      <span className="text-gray-300 text-sm">{text}</span>
    </div>
  )
}

// Add a loading state for audio operations
export function AudioLoading({ text = 'Loading audio...', className = '' }: { text?: string; className?: string }) {
  return (
    <div className={`flex items-center justify-center p-4 ${className}`}>
      <div className="text-center">
        <div className="w-8 h-8 border-2 border-purple-400 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
        <p className="text-gray-300 text-sm">{text}</p>
      </div>
    </div>
  )
} 