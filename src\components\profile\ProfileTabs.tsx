// Profile Tabs Component
'use client'

import { useState } from 'react'
import { Track, Playlist } from '@/types/database'
import { ExtendedProfile } from '@/types/profile'

interface ProfileTabsProps {
  profile: ExtendedProfile
  tracks: Track[]
  playlists: Playlist[]
  isOwnProfile: boolean
  onLoadMoreTracks: () => void
  onLoadMorePlaylists: () => void
}

export default function ProfileTabs({
  profile,
  tracks,
  playlists,
  isOwnProfile,
  onLoadMoreTracks,
  onLoadMorePlaylists
}: ProfileTabsProps) {
  const [activeTab, setActiveTab] = useState<'tracks' | 'playlists'>('tracks')

  const formatDuration = (seconds: number | null) => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getAIToolIcon = (tool: string) => {
    const icons: Record<string, string> = {
      suno: '🎵',
      udio: '🎶',
      mubert: '🎼',
      aiva: '🎹',
      amper: '🎸',
      custom: '⚡',
      other: '🎧'
    }
    return icons[tool] || '🎧'
  }

  const tabs = [
    {
      id: 'tracks' as const,
      label: 'Tracks',
      count: tracks.length,
      icon: '🎵'
    },
    {
      id: 'playlists' as const,
      label: 'Playlists',
      count: playlists.length,
      icon: '📋'
    }
  ]

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Tab Headers */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-purple-500 text-purple-600 dark:text-purple-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
              <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full text-xs">
                {tab.count}
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'tracks' && (
          <div className="space-y-4">
            {tracks.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🎵</div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {isOwnProfile ? "You haven't uploaded any tracks yet" : "No tracks uploaded yet"}
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  {isOwnProfile ? "Start creating and uploading your AI-generated music!" : "Check back later for new tracks."}
                </p>
                {isOwnProfile && (
                  <button className="mt-4 bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Upload Your First Track
                  </button>
                )}
              </div>
            ) : (
              <>
                {tracks.map((track) => (
                  <div
                    key={track.id}
                    className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                  >
                    {/* Track Cover */}
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-2xl">
                        {getAIToolIcon(track.ai_tool)}
                      </span>
                    </div>

                    {/* Track Info */}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 dark:text-white truncate">
                        {track.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                        {track.artist_name}
                      </p>
                      <div className="flex items-center gap-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                        <span>{formatDuration(track.duration)}</span>
                        {track.genre && <span>• {track.genre}</span>}
                        {track.mood && <span>• {track.mood}</span>}
                        <span>• {formatDate(track.created_at)}</span>
                      </div>
                    </div>

                    {/* Track Stats */}
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center gap-1">
                        <span>▶️</span>
                        <span>{track.play_count || 0}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>❤️</span>
                        <span>{track.like_count || 0}</span>
                      </div>
                    </div>

                    {/* Play Button */}
                    <button className="w-10 h-10 bg-purple-600 hover:bg-purple-700 text-white rounded-full flex items-center justify-center transition-colors">
                      ▶️
                    </button>
                  </div>
                ))}

                {/* Load More Button */}
                <div className="text-center pt-4">
                  <button
                    onClick={onLoadMoreTracks}
                    className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg font-medium transition-colors"
                  >
                    Load More Tracks
                  </button>
                </div>
              </>
            )}
          </div>
        )}

        {activeTab === 'playlists' && (
          <div className="space-y-4">
            {playlists.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📋</div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {isOwnProfile ? "You haven't created any playlists yet" : "No playlists created yet"}
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  {isOwnProfile ? "Create your first playlist to organize your favorite tracks!" : "Check back later for new playlists."}
                </p>
                {isOwnProfile && (
                  <button className="mt-4 bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Create Your First Playlist
                  </button>
                )}
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {playlists.map((playlist) => (
                    <div
                      key={playlist.id}
                      className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                    >
                      {/* Playlist Cover */}
                      <div className="w-full aspect-square bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg mb-4 flex items-center justify-center">
                        {playlist.cover_image_url ? (
                          <img
                            src={playlist.cover_image_url}
                            alt={playlist.name}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <span className="text-white text-4xl">📋</span>
                        )}
                      </div>

                      {/* Playlist Info */}
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white truncate mb-1">
                          {playlist.name}
                        </h4>
                        {playlist.description && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                            {playlist.description}
                          </p>
                        )}
                        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                          <span>{playlist.track_count} tracks</span>
                          <span>{playlist.is_public ? '🌍 Public' : '🔒 Private'}</span>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Created {formatDate(playlist.created_at)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Load More Button */}
                <div className="text-center pt-4">
                  <button
                    onClick={onLoadMorePlaylists}
                    className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg font-medium transition-colors"
                  >
                    Load More Playlists
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  )
} 