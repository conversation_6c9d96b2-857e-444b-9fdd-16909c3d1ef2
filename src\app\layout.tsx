import './globals.css'
import '../styles/browser-compatibility.css'
import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'
import { Toaster } from 'react-hot-toast'
import { AuthProvider } from '@/contexts/AuthContext'
import { AudioProvider } from '@/contexts/AudioContext'
import { MobilePlayerProvider } from '@/contexts/MobilePlayerContext'
import { BrowserCompatibilityProvider } from '@/components/browser/BrowserCompatibilityProvider'
import { ServiceWorkerRegistration } from '@/components/pwa/ServiceWorkerRegistration'
import PWAInstallPrompt from '@/components/pwa/PWAInstallPrompt'
import { PWAMetadata } from '@/components/pwa/PWAMetadata'
import PerformanceMonitor from '@/components/ui/PerformanceMonitor'
import GlobalAudioPlayer from '@/components/audio/GlobalAudioPlayer'
import { MobilePlayerContainer } from '@/components/mobile/MobilePlayerContainer'

const inter = Inter({ subsets: ['latin'] })

// Server-safe SkipToMain component
const SkipToMain = () => (
  <a
    href="#main-content"
    className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-purple-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
  >
    Skip to main content
  </a>
)

export const metadata: Metadata = {
  title: 'Tunami - AI Music Streaming Platform',
  description: 'Create, discover, and stream AI-generated music. World-class music platform with intelligent recommendations.',
  keywords: 'AI music, music streaming, AI generated music, Suno, Udio, music creation, playlists, audio streaming',
  authors: [{ name: 'Tunami Team' }],
  manifest: '/manifest.json',
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3003'),
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/icons/icon-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/icons/icon-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: [
      { url: '/icons/apple-touch-icon.png', sizes: '180x180' },
    ],
  },
  openGraph: {
    title: 'Tunami - AI Music Streaming Platform',
    description: 'Create, discover, and stream AI-generated music with advanced AI tools',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3003',
    siteName: 'Tunami',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Tunami AI Music Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tunami - AI Music Streaming Platform',
    description: 'Create, discover, and stream AI-generated music',
    images: ['/og-image.png'],
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'black-translucent',
  },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <PWAMetadata />
        
        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://supabase.com" />
        
        {/* Mobile-specific meta tags */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="Tunami" />
        
        {/* Color scheme and theme */}
        <meta name="color-scheme" content="dark" />
        <meta name="theme-color" content="#0f172a" />
        
        {/* Audio and media session capabilities */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="application-name" content="Tunami" />
      </head>
      
      <body className={`${inter.className} bg-gray-950 text-white antialiased`}>
        <ComponentErrorBoundary level="critical">
          <BrowserCompatibilityProvider>
            <AuthProvider>
              <AudioProvider>
                <MobilePlayerProvider>
                  {/* Skip to main content link for accessibility */}
                  <SkipToMain />
                  
                  {/* Main application container */}
                  <div 
                    className="flex h-screen bg-gray-950"
                    role="application"
                    aria-label="Tunami AI Music Platform"
                  >
                    {/* Main content area */}
                    <main 
                      id="main-content"
                      className="h-full w-full overflow-auto"
                      role="main"
                      aria-label="Main content area"
                      tabIndex={-1}
                    >
                      {children}
                    </main>
                  </div>

                  {/* Global Audio Player - handles all audio playback */}
                  <GlobalAudioPlayer />

                  {/* Mobile Player Container - mobile-optimized player UI */}
                  <MobilePlayerContainer />

                  {/* PWA Components for enhanced functionality */}
                  <ServiceWorkerRegistration />
                  <PWAInstallPrompt />
                  
                  {/* Live region for screen reader announcements */}
                  <div 
                    id="live-region"
                    aria-live="polite"
                    aria-atomic="true"
                    className="sr-only"
                  />
                  
                  {/* Status region for important updates */}
                  <div 
                    id="status-region"
                    role="status"
                    aria-live="assertive"
                    aria-atomic="true"
                    className="sr-only"
                  />
                </MobilePlayerProvider>
              </AudioProvider>
            </AuthProvider>
          </BrowserCompatibilityProvider>
          
          {/* Performance Monitor (Development Only) */}
          {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}
          
          {/* Toast Notifications with accessibility */}
          <Toaster
            position="top-right"
            toastOptions={{
              className: '',
              duration: 4000,
              style: {
                background: '#1f2937',
                color: '#f9fafb',
                border: '1px solid #374151',
                borderRadius: '8px',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#1f2937',
                },
                ariaProps: {
                  role: 'status',
                  'aria-live': 'polite',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#1f2937',
                },
                ariaProps: {
                  role: 'alert',
                  'aria-live': 'assertive',
                },
              },
              loading: {
                ariaProps: {
                  role: 'status',
                  'aria-live': 'polite',
                  'aria-busy': 'true',
                },
              },
            }}
          />
        </ComponentErrorBoundary>
      </body>
    </html>
  )
} 