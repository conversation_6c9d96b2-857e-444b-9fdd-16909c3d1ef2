/* Cross-browser compatibility styles for Tunami */

/* CSS Custom Properties with Fallbacks */
:root {
  /* Color system */
  --primary-color: #8b5cf6;
  --primary-hover: #7c3aed;
  --background-color: #0f172a;
  --surface-color: #1e293b;
  --text-color: #f8fafc;
  --text-muted: #94a3b8;
  
  /* Typography */
  --font-size-base: 16px;
  --line-height-base: 1.5;
  
  /* Layout */
  --border-radius: 0.5rem;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  
  /* Animation timing */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and normalize with cross-browser support */
* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margins and paddings */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

/* Body defaults */
body {
  line-height: 1;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* Lists */
ol, ul {
  list-style: none;
}

/* Tables */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Form elements */
button, input, optgroup, select, textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}

/* Remove button styles */
button {
  background: none;
  border: none;
  cursor: pointer;
  font: inherit;
}

/* Audio element styles */
audio {
  display: block;
  width: 100%;
}

/* Cross-browser audio controls */
audio::-webkit-media-controls-panel {
  background-color: var(--surface-color, #1e293b);
  color: var(--text-color, #f8fafc);
}

audio::-webkit-media-controls-play-button {
  background-color: var(--primary-color, #8b5cf6);
  border-radius: 50%;
}

audio::-webkit-media-controls-timeline {
  background-color: var(--surface-color, #1e293b);
  border-radius: var(--border-radius, 0.5rem);
}

audio::-webkit-media-controls-current-time-display,
audio::-webkit-media-controls-time-remaining-display {
  color: var(--text-color, #f8fafc);
}

audio::-webkit-media-controls-volume-slider {
  background-color: var(--surface-color, #1e293b);
  border-radius: var(--border-radius, 0.5rem);
}

/* Firefox audio controls */
audio::-moz-range-track {
  background-color: var(--surface-color, #1e293b);
  border-radius: var(--border-radius, 0.5rem);
}

audio::-moz-range-thumb {
  background-color: var(--primary-color, #8b5cf6);
  border-radius: 50%;
  border: none;
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari audio playback fixes */
  audio {
    -webkit-appearance: none;
    appearance: none;
  }
  
  /* Prevent zoom on form focus */
  input[type="text"],
  input[type="password"],
  input[type="email"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px !important;
  }
  
  /* Fix viewport issues */
  body {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Disable text selection on touch */
  .no-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

/* Chrome/Webkit specific */
@media screen and (-webkit-min-device-pixel-ratio:0) and (min-resolution:.001dpcm) {
  /* Chrome-specific styles */
  .chrome-optimize {
    will-change: transform;
    transform: translateZ(0);
  }
  
  /* Optimize scrollbars for Chrome */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--surface-color, #1e293b);
    border-radius: var(--border-radius, 0.5rem);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--primary-color, #8b5cf6);
    border-radius: var(--border-radius, 0.5rem);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover, #7c3aed);
  }
}

/* Firefox specific */
@-moz-document url-prefix() {
  /* Firefox-specific styles */
  .firefox-optimize {
    scroll-behavior: smooth;
  }
  
  /* Firefox scrollbars */
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color, #8b5cf6) var(--surface-color, #1e293b);
  }
}

/* Edge specific */
@supports (-ms-ime-align:auto) {
  /* Legacy Edge styles */
  .edge-fallback {
    /* Fallbacks for legacy Edge */
  }
}

/* CSS Grid with Flexbox fallback */
.grid-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

@supports (display: grid) {
  .grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

/* Flexbox with display: block fallback */
.flex-container {
  display: block;
}

@supports (display: flex) {
  .flex-container {
    display: flex;
  }
}

/* CSS Custom Properties fallbacks */
.primary-text {
  color: #8b5cf6; /* Fallback */
  color: var(--primary-color, #8b5cf6);
}

.background-dark {
  background-color: #0f172a; /* Fallback */
  background-color: var(--background-color, #0f172a);
}

.surface-dark {
  background-color: #1e293b; /* Fallback */
  background-color: var(--surface-color, #1e293b);
}

/* Backdrop filter with fallback */
.backdrop-blur {
  background-color: rgba(15, 23, 42, 0.8); /* Fallback */
}

@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background-color: rgba(15, 23, 42, 0.6);
  }
}

/* Transforms with fallbacks */
.transform-gpu {
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
}

/* Transitions with vendor prefixes */
.transition-all {
  transition: all var(--duration-normal, 300ms) var(--easing, ease);
  -webkit-transition: all var(--duration-normal, 300ms) var(--easing, ease);
  -moz-transition: all var(--duration-normal, 300ms) var(--easing, ease);
  -o-transition: all var(--duration-normal, 300ms) var(--easing, ease);
}

/* Animations with vendor prefixes */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@-webkit-keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@-moz-keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  -webkit-animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  -moz-animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Loading spinner */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@-webkit-keyframes spin {
  from { -webkit-transform: rotate(0deg); }
  to { -webkit-transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
  -webkit-animation: spin 1s linear infinite;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Touch optimization */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Prevent text scaling */
  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
  
  /* Optimize tap behavior */
  .tap-highlight-none {
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Mobile scrolling */
  .scroll-smooth {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-optimize {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #0f172a;
    --surface-color: #1e293b;
    --text-color: #f8fafc;
    --text-muted: #94a3b8;
  }
}

/* Light mode fallback */
@media (prefers-color-scheme: light) {
  :root {
    --background-color: #ffffff;
    --surface-color: #f8fafc;
    --text-color: #0f172a;
    --text-muted: #64748b;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  * {
    color: black !important;
    background: white !important;
  }
}

/* Focus styles for accessibility */
:focus {
  outline: 2px solid var(--primary-color, #8b5cf6);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-color, #8b5cf6);
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Browser-specific CSS classes (applied via JavaScript) */
.browser-chrome .chrome-specific {
  /* Chrome-specific styles */
}

.browser-firefox .firefox-specific {
  /* Firefox-specific styles */
}

.browser-safari .safari-specific {
  /* Safari-specific styles */
}

.browser-edge .edge-specific {
  /* Edge-specific styles */
}

.platform-ios .ios-specific {
  /* iOS-specific styles */
}

.platform-android .android-specific {
  /* Android-specific styles */
}

.device-mobile .mobile-specific {
  /* Mobile-specific styles */
}

.device-tablet .tablet-specific {
  /* Tablet-specific styles */
}

.device-desktop .desktop-specific {
  /* Desktop-specific styles */
} 