import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface SmoothTransitionProps {
  children: React.ReactNode
  isLoading?: boolean
  loadingComponent?: React.ReactNode
  className?: string
  duration?: number
}

export const SmoothTransition: React.FC<SmoothTransitionProps> = ({
  children,
  isLoading = false,
  loadingComponent,
  className = '',
  duration = 0.3
}) => {
  const [showContent, setShowContent] = useState(!isLoading)
  const timeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    if (isLoading) {
      setShowContent(false)
    } else {
      // Delay showing content to ensure smooth transition
      timeoutRef.current = setTimeout(() => {
        setShowContent(true)
      }, 100)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [isLoading])

  const fadeVariants = {
    hidden: { 
      opacity: 0, 
      y: 10,
      scale: 0.98
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    exit: { 
      opacity: 0, 
      y: -10,
      scale: 0.98,
      transition: {
        duration: duration * 0.7,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            variants={fadeVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {loadingComponent}
          </motion.div>
        ) : showContent ? (
          <motion.div
            key="content"
            variants={fadeVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {children}
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  )
}

interface AuthTransitionProps {
  isAuthenticated: boolean
  isLoading: boolean
  authenticatedComponent: React.ReactNode
  unauthenticatedComponent: React.ReactNode
  loadingComponent?: React.ReactNode
  className?: string
}

export const AuthTransition: React.FC<AuthTransitionProps> = ({
  isAuthenticated,
  isLoading,
  authenticatedComponent,
  unauthenticatedComponent,
  loadingComponent,
  className = ''
}) => {
  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0,
      scale: 0.95
    }),
    center: {
      x: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 300 : -300,
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    })
  }

  const getKey = () => {
    if (isLoading) return 'loading'
    return isAuthenticated ? 'authenticated' : 'unauthenticated'
  }

  const getDirection = () => {
    if (isLoading) return 0
    return isAuthenticated ? 1 : -1
  }

  const getCurrentComponent = () => {
    if (isLoading) return loadingComponent
    return isAuthenticated ? authenticatedComponent : unauthenticatedComponent
  }

  return (
    <div className={className}>
      <AnimatePresence mode="wait" custom={getDirection()}>
        <motion.div
          key={getKey()}
          custom={getDirection()}
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
        >
          {getCurrentComponent()}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}

interface StaggeredFadeInProps {
  children: React.ReactNode[]
  delay?: number
  duration?: number
  className?: string
}

export const StaggeredFadeIn: React.FC<StaggeredFadeInProps> = ({
  children,
  delay = 0.1,
  duration = 0.3,
  className = ''
}) => {
  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: delay
      }
    }
  }

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  return (
    <motion.div
      className={className}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {children.map((child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  )
}

interface PulseLoaderProps {
  size?: 'sm' | 'md' | 'lg'
  color?: string
  className?: string
}

export const PulseLoader: React.FC<PulseLoaderProps> = ({
  size = 'md',
  color = 'primary-400',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  }

  const pulseVariants = {
    pulse: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  return (
    <div className={`flex space-x-1 ${className}`}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`${sizeClasses[size]} bg-${color} rounded-full`}
          variants={pulseVariants}
          animate="pulse"
          style={{
            animationDelay: `${index * 0.2}s`
          }}
        />
      ))}
    </div>
  )
} 