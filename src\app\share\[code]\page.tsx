'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Play, Heart, Music, Users, Clock, Share2 } from 'lucide-react'
import { getSharedContent } from '@/lib/social'
import { createBrowserClient } from '@/lib/supabase'
import type { ShareLink } from '@/types/database'
import { Avatar } from '@/components/ui/Avatar'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import Link from 'next/link'

const supabase = createBrowserClient()

export default function SharedContentPage() {
  const { code } = useParams()
  const router = useRouter()
  const [shareData, setShareData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentUser, setCurrentUser] = useState<any>(null)

  useEffect(() => {
    getCurrentUser()
    loadSharedContent()
  }, [code])

  async function getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser()
    setCurrentUser(user)
  }

  async function loadSharedContent() {
    try {
      setIsLoading(true)
      const data = await getSharedContent(code as string)
      
      if (!data) {
        setError('This share link is invalid, expired, or has been removed.')
        return
      }

      setShareData(data)
    } catch (error) {
      console.error('Error loading shared content:', error)
      setError('Failed to load shared content.')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <Card className="bg-gray-800/50 border-gray-700 p-8 text-center max-w-md">
          <div className="animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-300">Loading shared content...</p>
        </Card>
      </div>
    )
  }

  if (error || !shareData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <Card className="bg-gray-800/50 border-gray-700 p-8 text-center max-w-md">
          <Share2 className="w-16 h-16 text-gray-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-white mb-2">Content Not Found</h2>
          <p className="text-gray-400 mb-6">
            {error || 'This share link is invalid or has expired.'}
          </p>
          <Link href="/">
            <Button>Go to Home</Button>
          </Link>
        </Card>
      </div>
    )
  }

  const isTrack = shareData.track_id && shareData.tracks
  const isPlaylist = shareData.playlist_id && shareData.playlists

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-6 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            Shared {isTrack ? 'Track' : 'Playlist'}
          </h1>
          <p className="text-gray-300">
            Someone shared this with you on Tunami
          </p>
        </div>

        {/* Track Content */}
        {isTrack && (
          <TrackSharedContent track={shareData.tracks} currentUser={currentUser} />
        )}

        {/* Playlist Content */}
        {isPlaylist && (
          <PlaylistSharedContent playlist={shareData.playlists} currentUser={currentUser} />
        )}

        {/* Share Stats */}
        <Card className="bg-gray-800/50 border-gray-700 p-4 mt-6">
          <div className="flex items-center justify-between text-sm text-gray-400">
            <div className="flex items-center gap-4">
              <span>Shared {shareData.access_count} times</span>
              {shareData.expires_at && (
                <span>
                  Expires {new Date(shareData.expires_at).toLocaleDateString()}
                </span>
              )}
            </div>
            <Link href="/users" className="text-blue-400 hover:underline">
              Discover more users
            </Link>
          </div>
        </Card>

        {/* Call to Action */}
        {!currentUser && (
          <Card className="bg-gradient-to-r from-purple-600 to-blue-600 border-none p-6 mt-6 text-center">
            <h3 className="text-xl font-semibold text-white mb-2">
              Join Tunami
            </h3>
            <p className="text-gray-200 mb-4">
              Create an account to upload your own tracks, create playlists, and connect with other music creators.
            </p>
            <div className="flex gap-3 justify-center">
              <Link href="/auth?mode=signup">
                <Button variant="secondary">Sign Up</Button>
              </Link>
              <Link href="/auth">
                <Button variant="outline">Sign In</Button>
              </Link>
            </div>
          </Card>
        )}
      </div>
    </div>
  )
}

function TrackSharedContent({ track, currentUser }: { track: any, currentUser: any }) {
  const [isPlaying, setIsPlaying] = useState(false)

  return (
    <div className="space-y-6">
      {/* Track Card */}
      <Card className="bg-gray-800/50 backdrop-blur-sm border-gray-700 p-8">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Track Artwork */}
          <div className="w-full md:w-64 h-64 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
            <Music className="w-24 h-24 text-white" />
          </div>

          {/* Track Info */}
          <div className="flex-1">
            <h2 className="text-3xl font-bold text-white mb-2">{track.title}</h2>
            <p className="text-xl text-gray-300 mb-4">{track.artist_name}</p>
            
            {track.description && (
              <p className="text-gray-400 mb-4">{track.description}</p>
            )}

            <div className="flex items-center gap-6 mb-6 text-sm text-gray-400">
              {track.genre && (
                <span className="capitalize">{track.genre.replace('_', ' ')}</span>
              )}
              {track.mood && (
                <span className="capitalize">{track.mood}</span>
              )}
              {track.duration && (
                <span className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {Math.floor(track.duration / 60)}:
                  {(track.duration % 60).toString().padStart(2, '0')}
                </span>
              )}
            </div>

            {/* Creator Info */}
            {track.profiles && (
              <div className="flex items-center gap-3 mb-6">
                <Avatar
                  src={track.profiles.avatar_url}
                  alt={track.profiles.full_name || track.profiles.username || ''}
                  size="sm"
                />
                <div>
                  <p className="text-white font-medium">
                    {track.profiles.full_name || track.profiles.username}
                  </p>
                  <p className="text-gray-400 text-sm">Creator</p>
                </div>
              </div>
            )}

            {/* Stats */}
            <div className="flex items-center gap-6 mb-6">
              <div className="flex items-center gap-2 text-gray-400">
                <Play className="w-5 h-5" />
                <span>{track.play_count || 0} plays</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400">
                <Heart className="w-5 h-5" />
                <span>{track.like_count || 0} likes</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <Button className="flex items-center gap-2">
                <Play className="w-4 h-4" />
                {isPlaying ? 'Pause' : 'Play'}
              </Button>
              
              {currentUser && (
                <Button variant="outline" className="flex items-center gap-2">
                  <Heart className="w-4 h-4" />
                  Like
                </Button>
              )}

              {track.profiles?.username && (
                <Link href={`/users/${track.profiles.username}`}>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    View Profile
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

function PlaylistSharedContent({ playlist, currentUser }: { playlist: any, currentUser: any }) {
  return (
    <div className="space-y-6">
      {/* Playlist Card */}
      <Card className="bg-gray-800/50 backdrop-blur-sm border-gray-700 p-8">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Playlist Cover */}
          <div className="w-full md:w-64 h-64 bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
            {playlist.cover_image_url ? (
              <img 
                src={playlist.cover_image_url} 
                alt={playlist.name}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <Play className="w-24 h-24 text-white" />
            )}
          </div>

          {/* Playlist Info */}
          <div className="flex-1">
            <h2 className="text-3xl font-bold text-white mb-2">{playlist.name}</h2>
            
            {playlist.description && (
              <p className="text-gray-400 mb-4">{playlist.description}</p>
            )}

            {/* Creator Info */}
            {playlist.profiles && (
              <div className="flex items-center gap-3 mb-6">
                <Avatar
                  src={playlist.profiles.avatar_url}
                  alt={playlist.profiles.full_name || playlist.profiles.username || ''}
                  size="sm"
                />
                <div>
                  <p className="text-white font-medium">
                    {playlist.profiles.full_name || playlist.profiles.username}
                  </p>
                  <p className="text-gray-400 text-sm">Creator</p>
                </div>
              </div>
            )}

            {/* Stats */}
            <div className="flex items-center gap-6 mb-6 text-gray-400">
              <span>{playlist.track_count || 0} tracks</span>
              {playlist.total_duration && (
                <span>
                  {Math.floor(playlist.total_duration / 60)} minutes
                </span>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <Button className="flex items-center gap-2">
                <Play className="w-4 h-4" />
                Play All
              </Button>

              {playlist.profiles?.username && (
                <Link href={`/users/${playlist.profiles.username}`}>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    View Profile
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
} 