'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Search, Filter, TrendingUp, Sparkles, Music, Clock, Heart, Shuffle, Headphones, X, Grid3X3, List, ChevronRight } from 'lucide-react'
import MainLayout from '@/components/layout/MainLayout'
import TrackCard from '@/components/tracks/TrackCard'
import { AudioTrack } from '@/types/track'
import { TrackCardSkeleton, SectionHeaderSkeleton } from '@/components/ui/LoadingSkeleton'
import { NoTracksFound } from '@/components/ui/EmptyState'
import { useAudio } from '@/contexts/AudioContext'
import Link from 'next/link'

interface BrowseCategory {
  id: string
  title: string
  description: string
  icon: string
  color: string
  href: string
  stats: string
}

interface QuickFilter {
  id: string
  name: string
  color: string
  icon: string
}

export default function BrowsePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setQueue } = useAudio()

  const [loading, setLoading] = useState(true)
  const [featuredTracks, setFeaturedTracks] = useState<AudioTrack[]>([])
  const [trendingTracks, setTrendingTracks] = useState<AudioTrack[]>([])

  const browseCategories: BrowseCategory[] = [
    {
      id: 'ai-tools',
      title: 'Browse by AI Tool',
      description: 'Explore tracks by Suno, Udio, MusicGen, and more',
      icon: '🤖',
      color: 'bg-gradient-to-r from-blue-500 to-cyan-500',
      href: '/browse/ai-tools',
      stats: '8 AI Tools • 5.4K tracks'
    },
    {
      id: 'recent',
      title: 'Recently Uploaded',
      description: 'Discover the latest AI-generated music',
      icon: '🕒',
      color: 'bg-gradient-to-r from-green-500 to-emerald-500',
      href: '/browse/recent',
      stats: 'Updated hourly • Fresh content'
    },
    {
      id: 'popular',
      title: 'Popular Tracks',
      description: 'Most played and trending tracks',
      icon: '🔥',
      color: 'bg-gradient-to-r from-orange-500 to-red-500',
      href: '/browse/popular',
      stats: 'Top charts • Trending now'
    },
    {
      id: 'discover',
      title: 'Random Discovery',
      description: 'Find your next favorite with AI recommendations',
      icon: '🎲',
      color: 'bg-gradient-to-r from-purple-500 to-pink-500',
      href: '/browse/discover',
      stats: 'Personalized • Surprise me'
    }
  ]

  const quickFilters: QuickFilter[] = [
    { id: 'electronic', name: 'Electronic', color: 'bg-cyan-600', icon: '🎛️' },
    { id: 'ambient', name: 'Ambient', color: 'bg-green-600', icon: '🌊' },
    { id: 'classical', name: 'Classical', color: 'bg-yellow-600', icon: '🎼' },
    { id: 'jazz', name: 'Jazz', color: 'bg-purple-600', icon: '🎷' },
    { id: 'rock', name: 'Rock', color: 'bg-red-600', icon: '🎸' },
    { id: 'pop', name: 'Pop', color: 'bg-pink-600', icon: '🎤' }
  ]

  // Load featured and trending tracks
  const loadTracks = useCallback(async () => {
    setLoading(true)
    
    // Mock API call - replace with actual API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const aiTools = ['Suno', 'Udio', 'MusicGen', 'AIVA', 'Custom']
    const genres = ['electronic', 'ambient', 'classical', 'jazz', 'rock', 'pop']
    
    // Featured tracks
    const mockFeatured: AudioTrack[] = Array.from({ length: 6 }, (_, i) => ({
      id: `featured-${i}`,
      title: `Featured Track ${i + 1}`,
      artist: `Featured Artist ${i + 1}`,
      duration: 180 + Math.floor(Math.random() * 120),
      src: `/audio/demo${(i % 3) + 1}.mp3`,
      file_url: `/audio/demo${(i % 3) + 1}.mp3`,
      aiTool: aiTools[Math.floor(Math.random() * aiTools.length)],
      genre: genres[Math.floor(Math.random() * genres.length)],
      created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      is_public: true,
      play_count: Math.floor(Math.random() * 50000) + 10000,
      like_count: Math.floor(Math.random() * 5000) + 1000
    }))

    // Trending tracks
    const mockTrending: AudioTrack[] = Array.from({ length: 8 }, (_, i) => ({
      id: `trending-${i}`,
      title: `Trending Track ${i + 1}`,
      artist: `Trending Artist ${i + 1}`,
      duration: 150 + Math.floor(Math.random() * 150),
      src: `/audio/demo${(i % 3) + 1}.mp3`,
      file_url: `/audio/demo${(i % 3) + 1}.mp3`,
      aiTool: aiTools[Math.floor(Math.random() * aiTools.length)],
      genre: genres[Math.floor(Math.random() * genres.length)],
      created_at: new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000).toISOString(),
      is_public: true,
      play_count: Math.floor(Math.random() * 25000) + 5000,
      like_count: Math.floor(Math.random() * 2500) + 500,
      trending_score: Math.random() * 100
    }))

    setFeaturedTracks(mockFeatured)
    setTrendingTracks(mockTrending)
    setLoading(false)
  }, [])

  useEffect(() => {
    loadTracks()
  }, [loadTracks])

  const handleQuickFilter = (genreId: string) => {
    router.push(`/browse/discover?genre=${genreId}`)
  }

  const handlePlayFeatured = () => {
    if (featuredTracks.length > 0) {
      setQueue(featuredTracks)
    }
  }

  const handlePlayTrending = () => {
    if (trendingTracks.length > 0) {
      setQueue(trendingTracks)
    }
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Browse & Discover</h1>
          <p className="text-gray-400">Explore AI-generated music in every way imaginable</p>
        </div>

        {/* Browse Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">Browse Categories</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {browseCategories.map((category) => (
              <Link key={category.id} href={category.href}>
                <div className={`${category.color} rounded-xl p-6 text-white hover:scale-105 transition-transform cursor-pointer group`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-3xl">{category.icon}</div>
                    <ChevronRight className="w-5 h-5 opacity-70 group-hover:opacity-100 transition-opacity" />
                  </div>
                  <h3 className="text-lg font-bold mb-2">{category.title}</h3>
                  <p className="text-sm opacity-90 mb-3">{category.description}</p>
                  <p className="text-xs opacity-75">{category.stats}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Quick Genre Filters */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-white mb-6">Quick Genre Access</h2>
          <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
            {quickFilters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => handleQuickFilter(filter.id)}
                className={`${filter.color} hover:scale-105 transition-transform p-4 rounded-lg text-white font-medium text-center`}
              >
                <div className="text-2xl mb-2">{filter.icon}</div>
                <div className="text-sm">{filter.name}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Featured Tracks */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold text-white flex items-center space-x-2">
              <Sparkles className="w-6 h-6 text-yellow-500" />
              <span>Featured Tracks</span>
            </h2>
            <div className="flex items-center space-x-3">
              <button
                onClick={handlePlayFeatured}
                className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg text-white transition-colors"
              >
                <Shuffle className="w-4 h-4" />
                <span>Play All</span>
              </button>
              <Link
                href="/browse/popular"
                className="text-purple-400 hover:text-purple-300 text-sm font-medium"
              >
                View All →
              </Link>
            </div>
          </div>
          
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <TrackCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              {featuredTracks.map((track) => (
                <TrackCard
                  key={track.id}
                  track={track}
                  layout="vertical"
                  size="sm"
                  showArtwork={true}
                  showStats={true}
                  showActions={true}
                />
              ))}
            </div>
          )}
        </div>

        {/* Trending Now */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold text-white flex items-center space-x-2">
              <TrendingUp className="w-6 h-6 text-orange-500" />
              <span>Trending Now</span>
            </h2>
            <div className="flex items-center space-x-3">
              <button
                onClick={handlePlayTrending}
                className="flex items-center space-x-2 bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded-lg text-white transition-colors"
              >
                <Shuffle className="w-4 h-4" />
                <span>Play All</span>
              </button>
              <Link
                href="/browse/popular?sort=trending"
                className="text-orange-400 hover:text-orange-300 text-sm font-medium"
              >
                View All →
              </Link>
            </div>
          </div>
          
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <TrackCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
              {trendingTracks.map((track) => (
                <TrackCard
                  key={track.id}
                  track={track}
                  layout="vertical"
                  size="sm"
                  showArtwork={true}
                  showStats={true}
                  showActions={true}
                />
              ))}
            </div>
          )}
        </div>

        {/* Discovery CTA */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-8 text-center text-white">
          <div className="text-4xl mb-4">🎲</div>
          <h2 className="text-2xl font-bold mb-2">Ready to Discover Something New?</h2>
          <p className="text-lg opacity-90 mb-6">Let our AI help you find your next favorite track</p>
          <div className="flex items-center justify-center space-x-4">
            <Link
              href="/browse/discover"
              className="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Surprise Me
            </Link>
            <Link
              href="/browse/recent"
              className="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Latest Uploads
            </Link>
          </div>
        </div>
      </div>
    </MainLayout>
  )
} 