// Tunami Service Worker
// Version 1.0.0

const CACHE_NAME = 'tunami-v1.0.0'
const OFFLINE_CACHE = 'tunami-offline-v1.0.0'
const AUDIO_CACHE = 'tunami-audio-v1.0.0'
const API_CACHE = 'tunami-api-v1.0.0'

// Critical assets to cache immediately
const CRITICAL_ASSETS = [
  '/',
  '/offline',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/_next/static/css/app/layout.css',
  '/_next/static/chunks/webpack.js',
  '/_next/static/chunks/main.js',
  '/_next/static/chunks/pages/_app.js'
]

// Pages to cache for offline access
const OFFLINE_PAGES = [
  '/',
  '/browse',
  '/upload',
  '/profile',
  '/playlists',
  '/offline'
]

// Background sync queue
const BACKGROUND_SYNC_TAG = 'tunami-background-sync'
const UPLOAD_SYNC_TAG = 'tunami-upload-sync'

// Cache duration settings (in milliseconds)
const CACHE_DURATIONS = {
  STATIC: 7 * 24 * 60 * 60 * 1000,    // 7 days
  API: 5 * 60 * 1000,                 // 5 minutes
  AUDIO: 30 * 24 * 60 * 60 * 1000,    // 30 days
  OFFLINE: 24 * 60 * 60 * 1000        // 1 day
}

// Install event - cache critical assets
self.addEventListener('install', (event) => {
  console.log('🎵 Service Worker: Installing...')
  
  event.waitUntil(
    Promise.all([
      caches.open(CACHE_NAME).then((cache) => {
        console.log('🎵 Service Worker: Caching critical assets')
        return cache.addAll(CRITICAL_ASSETS)
      }),
      caches.open(OFFLINE_CACHE).then((cache) => {
        console.log('🎵 Service Worker: Caching offline pages')
        return cache.addAll(OFFLINE_PAGES)
      })
    ]).then(() => {
      console.log('🎵 Service Worker: Installation complete')
      // Skip waiting to activate immediately
      return self.skipWaiting()
    })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🎵 Service Worker: Activating...')
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== OFFLINE_CACHE && 
                cacheName !== AUDIO_CACHE && 
                cacheName !== API_CACHE) {
              console.log('🎵 Service Worker: Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      }),
      // Claim all clients
      self.clients.claim()
    ]).then(() => {
      console.log('🎵 Service Worker: Activation complete')
    })
  )
})

// Fetch event - handle network requests with caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }

  // Skip cross-origin requests (except for audio files)
  if (url.origin !== self.location.origin && !isAudioFile(url)) {
    return
  }

  event.respondWith(handleFetch(request))
})

async function handleFetch(request) {
  const url = new URL(request.url)
  
  try {
    // Different caching strategies based on request type
    if (isStaticAsset(url)) {
      return await cacheFirst(request, CACHE_NAME)
    } else if (isAPIRequest(url)) {
      return await networkFirst(request, API_CACHE)
    } else if (isAudioFile(url)) {
      return await cacheFirst(request, AUDIO_CACHE)
    } else if (isPageRequest(url)) {
      return await staleWhileRevalidate(request, OFFLINE_CACHE)
    } else {
      return await networkFirst(request, CACHE_NAME)
    }
  } catch (error) {
    console.log('🎵 Service Worker: Fetch failed, serving offline page:', error)
    
    // Serve offline page for navigation requests
    if (request.mode === 'navigate') {
      return await getOfflinePage()
    }
    
    // Return cached version if available
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Return fallback response
    return new Response('Offline', { 
      status: 503, 
      statusText: 'Service Unavailable' 
    })
  }
}

// Cache first strategy - serve from cache, fallback to network
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse && !isCacheExpired(cachedResponse)) {
    return cachedResponse
  }
  
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const responseClone = networkResponse.clone()
      await cache.put(request, responseClone)
    }
    
    return networkResponse
  } catch (error) {
    if (cachedResponse) {
      return cachedResponse
    }
    throw error
  }
}

// Network first strategy - try network, fallback to cache
async function networkFirst(request, cacheName) {
  const cache = await caches.open(cacheName)
  
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const responseClone = networkResponse.clone()
      await cache.put(request, responseClone)
    }
    
    return networkResponse
  } catch (error) {
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    throw error
  }
}

// Stale while revalidate strategy - serve from cache, update in background
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName)
  const cachedResponse = await cache.match(request)
  
  // Always try to fetch in background
  const networkPromise = fetch(request).then(async (networkResponse) => {
    if (networkResponse.ok) {
      const responseClone = networkResponse.clone()
      await cache.put(request, responseClone)
    }
    return networkResponse
  }).catch(() => {
    // Network failed, but we might have cached version
  })
  
  // Return cached version immediately if available
  if (cachedResponse && !isCacheExpired(cachedResponse)) {
    // Update in background
    networkPromise.catch(() => {})
    return cachedResponse
  }
  
  // Wait for network if no cache
  return await networkPromise
}

// Check if cache entry is expired
function isCacheExpired(response) {
  const cachedDate = response.headers.get('sw-cached-date')
  if (!cachedDate) return false
  
  const cacheTime = new Date(cachedDate).getTime()
  const now = Date.now()
  
  return (now - cacheTime) > CACHE_DURATIONS.STATIC
}

// Helper functions to identify request types
function isStaticAsset(url) {
  return url.pathname.startsWith('/_next/') ||
         url.pathname.startsWith('/icons/') ||
         url.pathname.includes('.css') ||
         url.pathname.includes('.js') ||
         url.pathname.includes('.png') ||
         url.pathname.includes('.svg') ||
         url.pathname.includes('.jpg') ||
         url.pathname.includes('.jpeg') ||
         url.pathname.includes('.webp')
}

function isAPIRequest(url) {
  return url.pathname.startsWith('/api/') ||
         url.hostname.includes('supabase')
}

function isAudioFile(url) {
  return url.pathname.includes('.mp3') ||
         url.pathname.includes('.wav') ||
         url.pathname.includes('.flac') ||
         url.pathname.includes('.ogg') ||
         url.pathname.includes('.m4a') ||
         url.searchParams.has('audio') ||
         url.pathname.includes('/audio/')
}

function isPageRequest(url) {
  return url.origin === self.location.origin &&
         !isStaticAsset(url) &&
         !isAPIRequest(url) &&
         !isAudioFile(url)
}

// Get offline page
async function getOfflinePage() {
  const cache = await caches.open(OFFLINE_CACHE)
  const offlinePage = await cache.match('/offline')
  
  if (offlinePage) {
    return offlinePage
  }
  
  // Fallback offline page
  return new Response(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>Tunami - Offline</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { 
            font-family: system-ui, sans-serif; 
            text-align: center; 
            padding: 2rem;
            background: #111827;
            color: white;
          }
          .offline-icon { font-size: 4rem; margin-bottom: 1rem; }
          .offline-title { font-size: 2rem; margin-bottom: 1rem; }
          .offline-message { color: #9CA3AF; }
        </style>
      </head>
      <body>
        <div class="offline-icon">🎵</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
          Check your internet connection and try again.
        </p>
      </body>
    </html>
  `, {
    headers: { 'Content-Type': 'text/html' }
  })
}

// Background sync for uploads and data
self.addEventListener('sync', (event) => {
  console.log('🎵 Service Worker: Background sync triggered:', event.tag)
  
  if (event.tag === BACKGROUND_SYNC_TAG) {
    event.waitUntil(doBackgroundSync())
  } else if (event.tag === UPLOAD_SYNC_TAG) {
    event.waitUntil(syncUploads())
  }
})

async function doBackgroundSync() {
  try {
    console.log('🎵 Service Worker: Performing background sync')
    
    // Sync queued analytics
    await syncAnalytics()
    
    // Sync user preferences
    await syncUserPreferences()
    
    // Update cached data
    await updateCachedData()
    
    console.log('🎵 Service Worker: Background sync completed')
  } catch (error) {
    console.error('🎵 Service Worker: Background sync failed:', error)
    throw error
  }
}

async function syncUploads() {
  try {
    console.log('🎵 Service Worker: Syncing uploads')
    
    // Get queued uploads from IndexedDB
    const uploads = await getQueuedUploads()
    
    for (const upload of uploads) {
      try {
        await processUpload(upload)
        await removeQueuedUpload(upload.id)
        
        // Notify client of successful upload
        self.clients.matchAll().then(clients => {
          clients.forEach(client => {
            client.postMessage({
              type: 'UPLOAD_SYNCED',
              uploadId: upload.id,
              success: true
            })
          })
        })
      } catch (error) {
        console.error('🎵 Service Worker: Upload sync failed:', upload.id, error)
        
        // Mark upload as failed
        await markUploadFailed(upload.id, error.message)
      }
    }
    
    console.log('🎵 Service Worker: Upload sync completed')
  } catch (error) {
    console.error('🎵 Service Worker: Upload sync failed:', error)
    throw error
  }
}

// Placeholder functions for data management (implement with IndexedDB)
async function syncAnalytics() {
  // Sync queued analytics events
}

async function syncUserPreferences() {
  // Sync user preference changes
}

async function updateCachedData() {
  // Update frequently accessed data
}

async function getQueuedUploads() {
  // Get uploads from IndexedDB queue
  return []
}

async function processUpload(upload) {
  // Process upload to server
}

async function removeQueuedUpload(uploadId) {
  // Remove from IndexedDB queue
}

async function markUploadFailed(uploadId, error) {
  // Mark upload as failed in IndexedDB
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('🎵 Service Worker: Push notification received')
  
  const options = {
    body: 'New music is available!',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    tag: 'tunami-notification',
    data: {},
    actions: [
      {
        action: 'play',
        title: 'Play Now',
        icon: '/icons/play-action.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/dismiss-action.png'
      }
    ],
    vibrate: [200, 100, 200],
    requireInteraction: false
  }
  
  if (event.data) {
    try {
      const payload = event.data.json()
      
      if (payload.title) options.title = payload.title
      if (payload.body) options.body = payload.body
      if (payload.icon) options.icon = payload.icon
      if (payload.image) options.image = payload.image
      if (payload.badge) options.badge = payload.badge
      if (payload.tag) options.tag = payload.tag
      if (payload.data) options.data = payload.data
      if (payload.actions) options.actions = payload.actions
      if (payload.vibrate) options.vibrate = payload.vibrate
      if (payload.silent) options.silent = payload.silent
      if (payload.requireInteraction) options.requireInteraction = payload.requireInteraction
      
    } catch (error) {
      console.error('🎵 Service Worker: Invalid push payload:', error)
    }
  }
  
  event.waitUntil(
    self.registration.showNotification(
      options.title || 'Tunami',
      options
    )
  )
})

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('🎵 Service Worker: Notification clicked:', event.action)
  
  event.notification.close()
  
  const action = event.action
  const data = event.notification.data
  
  let url = '/'
  
  // Handle different actions
  switch (action) {
    case 'play':
      if (data.trackId) {
        url = `/play?track=${data.trackId}`
      } else if (data.url) {
        url = data.url
      }
      break
    case 'like':
      // Handle like action (could be background operation)
      if (data.trackId) {
        handleLikeTrack(data.trackId)
      }
      return
    case 'dismiss':
      // Just close notification
      return
    default:
      // Default action
      if (data.url) {
        url = data.url
      }
  }
  
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if app is already open
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          client.focus()
          client.navigate(url)
          return
        }
      }
      
      // Open new window/tab
      if (clients.openWindow) {
        return clients.openWindow(url)
      }
    })
  )
})

async function handleLikeTrack(trackId) {
  try {
    // Queue like action for background sync
    await queueAction('like', { trackId })
    
    // Try immediate sync if online
    if (navigator.onLine) {
      await syncActions()
    }
  } catch (error) {
    console.error('🎵 Service Worker: Failed to handle like action:', error)
  }
}

// Message handling for communication with main thread
self.addEventListener('message', (event) => {
  const { type, payload } = event.data
  
  console.log('🎵 Service Worker: Message received:', type)
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'GET_VERSION':
      event.ports[0]?.postMessage({
        type: 'VERSION_INFO',
        version: '1.0.0'
      })
      break
      
    case 'CACHE_AUDIO':
      if (payload.url) {
        cacheAudioFile(payload.url)
      }
      break
      
    case 'PRELOAD_CRITICAL':
      if (payload.urls) {
        preloadResources(payload.urls)
      }
      break
      
    case 'QUEUE_UPLOAD':
      if (payload.upload) {
        queueUpload(payload.upload)
      }
      break
      
    default:
      console.log('🎵 Service Worker: Unknown message type:', type)
  }
})

// Cache audio file for offline playback
async function cacheAudioFile(url) {
  try {
    const cache = await caches.open(AUDIO_CACHE)
    const response = await fetch(url)
    
    if (response.ok) {
      await cache.put(url, response)
      console.log('🎵 Service Worker: Audio file cached:', url)
    }
  } catch (error) {
    console.error('🎵 Service Worker: Failed to cache audio:', error)
  }
}

// Preload critical resources
async function preloadResources(urls) {
  try {
    const cache = await caches.open(CACHE_NAME)
    
    for (const url of urls) {
      try {
        const response = await fetch(url)
        if (response.ok) {
          await cache.put(url, response)
        }
      } catch (error) {
        console.log('🎵 Service Worker: Failed to preload:', url)
      }
    }
  } catch (error) {
    console.error('🎵 Service Worker: Preload failed:', error)
  }
}

// Queue upload for background sync
async function queueUpload(upload) {
  try {
    // Store in IndexedDB for background sync
    console.log('🎵 Service Worker: Upload queued:', upload.id)
    
    // Register background sync
    await self.registration.sync.register(UPLOAD_SYNC_TAG)
  } catch (error) {
    console.error('🎵 Service Worker: Failed to queue upload:', error)
  }
}

// Queue action for background sync
async function queueAction(action, data) {
  try {
    // Store action in IndexedDB
    console.log('🎵 Service Worker: Action queued:', action)
    
    // Register background sync
    await self.registration.sync.register(BACKGROUND_SYNC_TAG)
  } catch (error) {
    console.error('🎵 Service Worker: Failed to queue action:', error)
  }
}

// Sync queued actions
async function syncActions() {
  try {
    // Process queued actions
    console.log('🎵 Service Worker: Syncing actions')
  } catch (error) {
    console.error('🎵 Service Worker: Action sync failed:', error)
  }
}

// Error handling
self.addEventListener('error', (event) => {
  console.error('🎵 Service Worker: Error:', event.error)
})

self.addEventListener('unhandledrejection', (event) => {
  console.error('🎵 Service Worker: Unhandled rejection:', event.reason)
})

console.log('🎵 Service Worker: Script loaded successfully') 