// Comprehensive Error Handling System for Tunami
// Handles network errors, audio errors, and provides user-friendly messages

import { toast } from 'react-hot-toast'

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'network',
  AUDIO = 'audio',
  AUTH = 'auth',
  UPLOAD = 'upload',
  DATABASE = 'database',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  RATE_LIMIT = 'rate_limit',
  UNKNOWN = 'unknown'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Custom error class with enhanced information
export class TunamiError extends Error {
  public readonly type: ErrorType
  public readonly severity: ErrorSeverity
  public readonly code?: string
  public readonly userMessage: string
  public readonly retryable: boolean
  public readonly timestamp: Date
  public readonly context?: Record<string, any>

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    options: {
      code?: string
      userMessage?: string
      retryable?: boolean
      context?: Record<string, any>
    } = {}
  ) {
    super(message)
    this.name = 'TunamiError'
    this.type = type
    this.severity = severity
    this.code = options.code
    this.userMessage = options.userMessage || this.getDefaultUserMessage()
    this.retryable = options.retryable ?? this.getDefaultRetryable()
    this.timestamp = new Date()
    this.context = options.context
  }

  private getDefaultUserMessage(): string {
    switch (this.type) {
      case ErrorType.NETWORK:
        return 'Network connection issue. Please check your internet connection.'
      case ErrorType.AUDIO:
        return 'Audio playback error. The file may be corrupted or unsupported.'
      case ErrorType.AUTH:
        return 'Authentication error. Please log in again.'
      case ErrorType.UPLOAD:
        return 'Upload failed. Please try again with a different file.'
      case ErrorType.DATABASE:
        return 'Database error. Please try again later.'
      case ErrorType.VALIDATION:
        return 'Invalid input. Please check your data and try again.'
      case ErrorType.PERMISSION:
        return 'Permission denied. You don\'t have access to this resource.'
      case ErrorType.RATE_LIMIT:
        return 'Too many requests. Please wait a moment and try again.'
      default:
        return 'An unexpected error occurred. Please try again.'
    }
  }

  private getDefaultRetryable(): boolean {
    switch (this.type) {
      case ErrorType.NETWORK:
      case ErrorType.DATABASE:
      case ErrorType.RATE_LIMIT:
        return true
      case ErrorType.AUTH:
      case ErrorType.VALIDATION:
      case ErrorType.PERMISSION:
        return false
      default:
        return true
    }
  }
}

// Audio-specific error types
export enum AudioErrorCode {
  MEDIA_ERR_ABORTED = 'MEDIA_ERR_ABORTED',
  MEDIA_ERR_NETWORK = 'MEDIA_ERR_NETWORK',
  MEDIA_ERR_DECODE = 'MEDIA_ERR_DECODE',
  MEDIA_ERR_SRC_NOT_SUPPORTED = 'MEDIA_ERR_SRC_NOT_SUPPORTED',
  MEDIA_ERR_ENCRYPTED = 'MEDIA_ERR_ENCRYPTED',
  PLAYBACK_FAILED = 'PLAYBACK_FAILED',
  LOAD_FAILED = 'LOAD_FAILED'
}

// Network error detection
export function isNetworkError(error: any): boolean {
  if (!error) return false
  
  // Check for common network error indicators
  const networkIndicators = [
    'fetch',
    'network',
    'connection',
    'timeout',
    'offline',
    'ERR_NETWORK',
    'ERR_INTERNET_DISCONNECTED',
    'Failed to fetch'
  ]
  
  const errorString = error.toString().toLowerCase()
  return networkIndicators.some(indicator => errorString.includes(indicator))
}

// Supabase error handler
export function handleSupabaseError(error: any, context?: Record<string, any>): TunamiError {
  if (!error) {
    return new TunamiError('Unknown database error', ErrorType.DATABASE)
  }

  // Network errors
  if (isNetworkError(error)) {
    return new TunamiError(
      error.message || 'Network error',
      ErrorType.NETWORK,
      ErrorSeverity.MEDIUM,
      {
        code: 'SUPABASE_NETWORK_ERROR',
        retryable: true,
        context
      }
    )
  }

  // Authentication errors
  if (error.message?.includes('JWT') || error.message?.includes('auth')) {
    return new TunamiError(
      error.message,
      ErrorType.AUTH,
      ErrorSeverity.HIGH,
      {
        code: 'SUPABASE_AUTH_ERROR',
        userMessage: 'Please log in again to continue.',
        retryable: false,
        context
      }
    )
  }

  // Permission errors
  if (error.message?.includes('permission') || error.message?.includes('policy')) {
    return new TunamiError(
      error.message,
      ErrorType.PERMISSION,
      ErrorSeverity.HIGH,
      {
        code: 'SUPABASE_PERMISSION_ERROR',
        userMessage: 'You don\'t have permission to perform this action.',
        retryable: false,
        context
      }
    )
  }

  // Rate limiting
  if (error.message?.includes('rate limit') || error.status === 429) {
    return new TunamiError(
      error.message,
      ErrorType.RATE_LIMIT,
      ErrorSeverity.MEDIUM,
      {
        code: 'SUPABASE_RATE_LIMIT',
        userMessage: 'Too many requests. Please wait a moment and try again.',
        retryable: true,
        context
      }
    )
  }

  // Generic database error
  return new TunamiError(
    error.message || 'Database operation failed',
    ErrorType.DATABASE,
    ErrorSeverity.MEDIUM,
    {
      code: 'SUPABASE_DATABASE_ERROR',
      retryable: true,
      context
    }
  )
}

// Audio error handler
export function handleAudioError(error: any, audioElement?: HTMLAudioElement): TunamiError {
  let errorCode: AudioErrorCode
  let userMessage: string
  let severity = ErrorSeverity.MEDIUM

  if (audioElement?.error) {
    switch (audioElement.error.code) {
      case MediaError.MEDIA_ERR_ABORTED:
        errorCode = AudioErrorCode.MEDIA_ERR_ABORTED
        userMessage = 'Audio playback was interrupted.'
        severity = ErrorSeverity.LOW
        break
      case MediaError.MEDIA_ERR_NETWORK:
        errorCode = AudioErrorCode.MEDIA_ERR_NETWORK
        userMessage = 'Network error while loading audio. Check your connection.'
        severity = ErrorSeverity.MEDIUM
        break
      case MediaError.MEDIA_ERR_DECODE:
        errorCode = AudioErrorCode.MEDIA_ERR_DECODE
        userMessage = 'Audio file is corrupted or in an unsupported format.'
        severity = ErrorSeverity.HIGH
        break
      case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
        errorCode = AudioErrorCode.MEDIA_ERR_SRC_NOT_SUPPORTED
        userMessage = 'Audio format not supported by your browser.'
        severity = ErrorSeverity.HIGH
        break
      default:
        errorCode = AudioErrorCode.PLAYBACK_FAILED
        userMessage = 'Audio playback failed. Please try again.'
    }
  } else {
    errorCode = AudioErrorCode.PLAYBACK_FAILED
    userMessage = 'Audio playback failed. Please try again.'
  }

  return new TunamiError(
    error?.message || 'Audio error',
    ErrorType.AUDIO,
    severity,
    {
      code: errorCode,
      userMessage,
      retryable: errorCode !== AudioErrorCode.MEDIA_ERR_SRC_NOT_SUPPORTED,
      context: {
        audioSrc: audioElement?.src,
        currentTime: audioElement?.currentTime,
        duration: audioElement?.duration,
        readyState: audioElement?.readyState,
        networkState: audioElement?.networkState
      }
    }
  )
}

// Upload error handler
export function handleUploadError(error: any, file?: File): TunamiError {
  // File size errors
  if (error.message?.includes('size') || error.message?.includes('large')) {
    return new TunamiError(
      error.message,
      ErrorType.UPLOAD,
      ErrorSeverity.MEDIUM,
      {
        code: 'UPLOAD_FILE_TOO_LARGE',
        userMessage: 'File is too large. Please choose a smaller file (max 50MB).',
        retryable: false,
        context: { fileName: file?.name, fileSize: file?.size }
      }
    )
  }

  // File type errors
  if (error.message?.includes('type') || error.message?.includes('format')) {
    return new TunamiError(
      error.message,
      ErrorType.UPLOAD,
      ErrorSeverity.MEDIUM,
      {
        code: 'UPLOAD_INVALID_FILE_TYPE',
        userMessage: 'Invalid file type. Please upload MP3, WAV, or FLAC files only.',
        retryable: false,
        context: { fileName: file?.name, fileType: file?.type }
      }
    )
  }

  // Network errors during upload
  if (isNetworkError(error)) {
    return new TunamiError(
      error.message,
      ErrorType.NETWORK,
      ErrorSeverity.MEDIUM,
      {
        code: 'UPLOAD_NETWORK_ERROR',
        userMessage: 'Upload failed due to network issues. Please try again.',
        retryable: true,
        context: { fileName: file?.name }
      }
    )
  }

  // Generic upload error
  return new TunamiError(
    error.message || 'Upload failed',
    ErrorType.UPLOAD,
    ErrorSeverity.MEDIUM,
    {
      code: 'UPLOAD_FAILED',
      retryable: true,
      context: { fileName: file?.name, fileSize: file?.size }
    }
  )
}

// Retry mechanism
export class RetryHandler {
  private maxRetries: number
  private baseDelay: number
  private maxDelay: number

  constructor(maxRetries = 3, baseDelay = 1000, maxDelay = 10000) {
    this.maxRetries = maxRetries
    this.baseDelay = baseDelay
    this.maxDelay = maxDelay
  }

  async execute<T>(
    operation: () => Promise<T>,
    errorHandler?: (error: any) => TunamiError
  ): Promise<T> {
    let lastError: any

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        const tunamiError = errorHandler ? errorHandler(error) : new TunamiError(
          (error as Error)?.message || 'Operation failed',
          ErrorType.UNKNOWN
        )

        // Don't retry if error is not retryable or we've reached max attempts
        if (!tunamiError.retryable || attempt === this.maxRetries) {
          throw tunamiError
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.baseDelay * Math.pow(2, attempt),
          this.maxDelay
        )

        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 0.1 * delay
        await new Promise(resolve => setTimeout(resolve, delay + jitter))
      }
    }

    throw lastError
  }
}

// Global error handler for unhandled errors
export function setupGlobalErrorHandler() {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    const error = new TunamiError(
      'An unexpected error occurred',
      ErrorType.UNKNOWN,
      ErrorSeverity.HIGH,
      {
        code: 'UNHANDLED_REJECTION',
        context: { reason: event.reason }
      }
    )

    showErrorToast(error)
    logError(error)
  })

  // Handle JavaScript errors
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error)
    
    const error = new TunamiError(
      'A JavaScript error occurred',
      ErrorType.UNKNOWN,
      ErrorSeverity.HIGH,
      {
        code: 'JAVASCRIPT_ERROR',
        context: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      }
    )

    showErrorToast(error)
    logError(error)
  })
}

// Toast notification helpers
export function showErrorToast(error: TunamiError) {
  const options = {
    duration: error.severity === ErrorSeverity.CRITICAL ? 8000 : 4000,
    style: {
      background: '#1f2937',
      color: '#f3f4f6',
      border: '1px solid #ef4444'
    }
  }

  toast.error(error.userMessage, options)
}

export function showSuccessToast(message: string) {
  toast.success(message, {
    style: {
      background: '#1f2937',
      color: '#f3f4f6',
      border: '1px solid #10b981'
    }
  })
}

export function showLoadingToast(message: string) {
  return toast.loading(message, {
    style: {
      background: '#1f2937',
      color: '#f3f4f6',
      border: '1px solid #8b5cf6'
    }
  })
}

// Error logging (can be extended to send to external service)
export function logError(error: TunamiError) {
  const errorLog = {
    message: error.message,
    type: error.type,
    severity: error.severity,
    code: error.code,
    timestamp: error.timestamp,
    context: error.context,
    userAgent: navigator.userAgent,
    url: window.location.href
  }

  console.error('Tunami Error:', errorLog)

  // In production, send to error tracking service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to Sentry, LogRocket, etc.
    // Sentry.captureException(error, { extra: errorLog })
  }
}

// Validation helpers
export function validateEmail(email: string): string | null {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!email) return 'Email is required'
  if (!emailRegex.test(email)) return 'Please enter a valid email address'
  return null
}

export function validatePassword(password: string): string | null {
  if (!password) return 'Password is required'
  if (password.length < 8) return 'Password must be at least 8 characters long'
  if (!/(?=.*[a-z])/.test(password)) return 'Password must contain at least one lowercase letter'
  if (!/(?=.*[A-Z])/.test(password)) return 'Password must contain at least one uppercase letter'
  if (!/(?=.*\d)/.test(password)) return 'Password must contain at least one number'
  return null
}

export function validateTrackTitle(title: string): string | null {
  if (!title?.trim()) return 'Track title is required'
  if (title.trim().length < 2) return 'Track title must be at least 2 characters long'
  if (title.trim().length > 100) return 'Track title must be less than 100 characters'
  return null
}

export function validateAudioFile(file: File): string | null {
  const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/flac']
  const maxSize = 50 * 1024 * 1024 // 50MB

  if (!allowedTypes.includes(file.type)) {
    return 'Please upload MP3, WAV, or FLAC files only'
  }

  if (file.size > maxSize) {
    return 'File size must be less than 50MB'
  }

  return null
}

// Export default retry handler instance
export const defaultRetryHandler = new RetryHandler() 