// Audio utility functions for Tunami
// Handles conversion between database types and audio player types

import type { Track } from '@/types/database'
import type { AudioTrack } from '@/types/track'

/**
 * Convert a database Track to an AudioTrack for the global audio player
 */
export function convertTrackToAudioTrack(track: Track): AudioTrack {
  return {
    id: track.id,
    title: track.title,
    artist: track.artist_name,
    src: track.file_url || '',
    file_url: track.file_url,
    duration: track.duration,
    genre: track.genre || undefined,
    aiTool: track.ai_tool,
    created_at: track.created_at,
    is_public: track.is_public,
    cover_image_url: undefined, // Could be added later
    description: track.ai_prompt || undefined,
  }
}

/**
 * Convert multiple database Tracks to AudioTracks
 */
export function convertTracksToAudioTracks(tracks: Track[]): AudioTrack[] {
  return tracks.map(convertTrackToAudioTrack)
}

/**
 * Check if a track has a valid audio source
 */
export function hasValidAudioSource(track: Track | AudioTrack): boolean {
  if ('file_url' in track) {
    return !!(track.file_url && track.file_url.trim())
  }
  return !!(track.src && track.src.trim())
}

/**
 * Format duration for display
 */
export function formatDuration(seconds: number | null | undefined): string {
  if (!seconds || seconds <= 0) return '0:00'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

/**
 * Get AI tool display name
 */
export function getAIToolDisplayName(tool: string): string {
  const tools: Record<string, string> = {
    'suno': 'Suno AI',
    'udio': 'Udio',
    'mubert': 'Mubert',
    'soundraw': 'Soundraw',
    'aiva': 'AIVA',
    'amper': 'Amper Music',
    'jukedeck': 'Jukedeck',
    'endlesss': 'Endlesss',
    'custom': 'Custom Model',
    'other': 'Other AI Tool'
  }
  return tools[tool] || tool
}

/**
 * Format genre for display
 */
export function formatGenre(genre: string): string {
  return genre.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
} 