import { supabase } from './supabase'
import { 
  Playlist, 
  PlaylistWithTracks, 
  CreatePlaylistData, 
  UpdatePlaylistData, 
  AddTrackToPlaylistData,
  ReorderTrackData,
  PlaylistFilters,
  PlaylistStats
} from '@/types/playlist'

export interface BulkTrackOperation {
  track_id: string
  position?: number
}

export interface PlaylistPermissions {
  canView: boolean
  canEdit: boolean
  canDelete: boolean
  canAddTracks: boolean
  canRemoveTracks: boolean
  canReorder: boolean
}

export interface PlaylistShareData {
  playlist_id: string
  share_url: string
  expires_at?: string
  is_public: boolean
}

export class EnhancedPlaylistService {
  // Enhanced playlist creation with validation
  static async createPlaylist(data: CreatePlaylistData): Promise<Playlist> {
    const { data: user } = await supabase.auth.getUser()
    if (!user.user) {
      throw new Error('User not authenticated')
    }

    const { data: playlist, error } = await supabase
      .from('playlists')
      .insert([{
        name: data.name.trim(),
        description: data.description?.trim(),
        is_public: data.is_public || false,
        cover_image_url: data.cover_image_url,
        user_id: user.user.id
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create playlist: ${error.message}`)
    }

    return playlist
  }

  // Enhanced playlist update with ownership validation
  static async updatePlaylist(id: string, data: UpdatePlaylistData): Promise<Playlist> {
    // First check if user can modify this playlist
    const { data: canModify, error: permError } = await supabase
      .rpc('can_modify_playlist', { p_playlist_id: id })

    if (permError) {
      throw new Error(`Permission check failed: ${permError.message}`)
    }

    if (!canModify) {
      throw new Error('Unauthorized: You do not have permission to modify this playlist')
    }

    const { data: playlist, error } = await supabase
      .from('playlists')
      .update({
        name: data.name?.trim(),
        description: data.description?.trim(),
        is_public: data.is_public,
        cover_image_url: data.cover_image_url
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update playlist: ${error.message}`)
    }

    return playlist
  }

  // Enhanced playlist deletion with cascade handling
  static async deletePlaylist(id: string): Promise<void> {
    const { data: canModify, error: permError } = await supabase
      .rpc('can_modify_playlist', { p_playlist_id: id })

    if (permError) {
      throw new Error(`Permission check failed: ${permError.message}`)
    }

    if (!canModify) {
      throw new Error('Unauthorized: You do not have permission to delete this playlist')
    }

    const { error } = await supabase
      .from('playlists')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete playlist: ${error.message}`)
    }
  }

  // Get playlist with tracks using optimized RPC function
  static async getPlaylist(id: string): Promise<PlaylistWithTracks> {
    const { data, error } = await supabase
      .rpc('get_playlist_with_tracks', { p_playlist_id: id })

    if (error) {
      throw new Error(`Failed to fetch playlist: ${error.message}`)
    }

    if (!data || data.length === 0) {
      throw new Error('Playlist not found or access denied')
    }

    // Transform the flat result into nested structure
    const playlistData = data[0]
    const playlist: PlaylistWithTracks = {
      id: playlistData.playlist_id,
      name: playlistData.playlist_name,
      description: playlistData.playlist_description,
      is_public: playlistData.playlist_is_public,
      cover_image_url: playlistData.playlist_cover_image_url,
      created_at: playlistData.playlist_created_at,
      updated_at: playlistData.playlist_updated_at,
      user_id: playlistData.playlist_user_id,
      tracks: data
        .filter((row: any) => row.track_id) // Filter out playlists with no tracks
        .map((row: any) => ({
          id: `${row.playlist_id}-${row.track_id}`, // Composite ID
          playlist_id: row.playlist_id,
          track_id: row.track_id,
          position: row.position,
          added_at: row.added_at,
          track: {
            id: row.track_id,
            title: row.track_title,
            artist: row.track_artist,
            album: row.track_album,
            duration: row.track_duration,
            file_url: row.track_file_url,
            cover_image_url: row.track_cover_image_url,
            genre: row.track_genre,
            created_at: '', // Not needed for this context
            is_public: true // Assume public for now
          }
        }))
        .sort((a: any, b: any) => a.position - b.position)
    }

    return playlist
  }

  // Get user playlists with stats using RPC
  static async getUserPlaylists(userId?: string): Promise<Playlist[]> {
    const { data, error } = await supabase
      .rpc('get_user_playlists_with_stats', { p_user_id: userId || null })

    if (error) {
      throw new Error(`Failed to fetch user playlists: ${error.message}`)
    }

    return data.map((playlist: any) => ({
      ...playlist,
      track_count: Number(playlist.track_count),
      total_duration: Number(playlist.total_duration)
    }))
  }

  // Search public playlists with pagination
  static async getPublicPlaylists(filters?: PlaylistFilters): Promise<Playlist[]> {
    const { data, error } = await supabase
      .rpc('search_public_playlists', {
        p_search_term: filters?.search || null,
        p_limit: 50,
        p_offset: 0
      })

    if (error) {
      throw new Error(`Failed to fetch public playlists: ${error.message}`)
    }

    return data.map((playlist: any) => ({
      ...playlist,
      track_count: Number(playlist.track_count),
      total_duration: Number(playlist.total_duration)
    }))
  }

  // Add single track to playlist
  static async addTrackToPlaylist(data: AddTrackToPlaylistData): Promise<void> {
    const { error } = await supabase
      .rpc('add_tracks_to_playlist', {
        p_playlist_id: data.playlist_id,
        p_track_ids: [data.track_id],
        p_start_position: data.position || null
      })

    if (error) {
      throw new Error(`Failed to add track to playlist: ${error.message}`)
    }
  }

  // Add multiple tracks to playlist (bulk operation)
  static async addTracksToPlaylist(
    playlistId: string, 
    trackIds: string[], 
    startPosition?: number
  ): Promise<BulkTrackOperation[]> {
    const { data, error } = await supabase
      .rpc('add_tracks_to_playlist', {
        p_playlist_id: playlistId,
        p_track_ids: trackIds,
        p_start_position: startPosition || null
      })

    if (error) {
      throw new Error(`Failed to add tracks to playlist: ${error.message}`)
    }

    return data || []
  }

  // Remove single track from playlist
  static async removeTrackFromPlaylist(playlistId: string, trackId: string): Promise<void> {
    const { error } = await supabase
      .rpc('remove_tracks_from_playlist', {
        p_playlist_id: playlistId,
        p_track_ids: [trackId]
      })

    if (error) {
      throw new Error(`Failed to remove track from playlist: ${error.message}`)
    }
  }

  // Remove multiple tracks from playlist (bulk operation)
  static async removeTracksFromPlaylist(playlistId: string, trackIds: string[]): Promise<number> {
    const { data, error } = await supabase
      .rpc('remove_tracks_from_playlist', {
        p_playlist_id: playlistId,
        p_track_ids: trackIds
      })

    if (error) {
      throw new Error(`Failed to remove tracks from playlist: ${error.message}`)
    }

    return data || 0
  }

  // Efficient track reordering using RPC
  static async reorderPlaylistTracks(playlistId: string, tracks: ReorderTrackData[]): Promise<void> {
    const trackPositions = tracks.map(track => ({
      track_id: track.track_id,
      position: track.new_position
    }))

    const { error } = await supabase
      .rpc('reorder_playlist_tracks', {
        p_playlist_id: playlistId,
        p_track_positions: trackPositions
      })

    if (error) {
      throw new Error(`Failed to reorder playlist tracks: ${error.message}`)
    }
  }

  // Duplicate playlist using RPC
  static async duplicatePlaylist(
    sourcePlaylistId: string, 
    newName: string, 
    newDescription?: string,
    isPublic: boolean = false
  ): Promise<string> {
    const { data, error } = await supabase
      .rpc('duplicate_playlist', {
        p_source_playlist_id: sourcePlaylistId,
        p_new_name: newName,
        p_new_description: newDescription || null,
        p_is_public: isPublic
      })

    if (error) {
      throw new Error(`Failed to duplicate playlist: ${error.message}`)
    }

    return data
  }

  // Get playlist permissions for current user
  static async getPlaylistPermissions(playlistId: string): Promise<PlaylistPermissions> {
    const { data: user } = await supabase.auth.getUser()
    if (!user.user) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canAddTracks: false,
        canRemoveTracks: false,
        canReorder: false
      }
    }

    // Get playlist info
    const { data: playlist, error } = await supabase
      .from('playlists')
      .select('user_id, is_public')
      .eq('id', playlistId)
      .single()

    if (error) {
      throw new Error(`Failed to check playlist permissions: ${error.message}`)
    }

    const isOwner = playlist.user_id === user.user.id
    const canView = isOwner || playlist.is_public

    return {
      canView,
      canEdit: isOwner,
      canDelete: isOwner,
      canAddTracks: isOwner,
      canRemoveTracks: isOwner,
      canReorder: isOwner
    }
  }

  // Generate playlist share URL
  static async generateShareUrl(playlistId: string): Promise<PlaylistShareData> {
    const { data: playlist, error } = await supabase
      .from('playlists')
      .select('is_public, user_id')
      .eq('id', playlistId)
      .single()

    if (error) {
      throw new Error(`Failed to get playlist info: ${error.message}`)
    }

    const { data: user } = await supabase.auth.getUser()
    if (!user.user || playlist.user_id !== user.user.id) {
      throw new Error('Unauthorized: You can only share your own playlists')
    }

    const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_SITE_URL
    const shareUrl = `${baseUrl}/playlist/${playlistId}`

    return {
      playlist_id: playlistId,
      share_url: shareUrl,
      is_public: playlist.is_public
    }
  }

  // Get playlists containing a specific track
  static async getPlaylistsWithTrack(trackId: string): Promise<Playlist[]> {
    const { data, error } = await supabase
      .rpc('get_playlists_with_track', { p_track_id: trackId })

    if (error) {
      throw new Error(`Failed to fetch playlists with track: ${error.message}`)
    }

    return data || []
  }

  // Check if track exists in playlist
  static async isTrackInPlaylist(playlistId: string, trackId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('playlist_tracks')
      .select('id')
      .eq('playlist_id', playlistId)
      .eq('track_id', trackId)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to check track in playlist: ${error.message}`)
    }

    return !!data
  }

  // Get playlist statistics
  static async getPlaylistStats(playlistId: string): Promise<PlaylistStats> {
    const { data, error } = await supabase
      .from('playlist_tracks')
      .select(`
        track:tracks (duration),
        added_at
      `)
      .eq('playlist_id', playlistId)

    if (error) {
      throw new Error(`Failed to fetch playlist stats: ${error.message}`)
    }

    const trackCount = data.length
    const totalDuration = data.reduce((sum, item: any) => sum + (item.track?.duration || 0), 0)
    const lastUpdated = data.length > 0 
      ? Math.max(...data.map(item => new Date(item.added_at).getTime()))
      : Date.now()

    return {
      track_count: trackCount,
      total_duration: totalDuration,
      last_updated: new Date(lastUpdated).toISOString()
    }
  }

  // Validate playlist data
  static validatePlaylistData(data: CreatePlaylistData | UpdatePlaylistData): string[] {
    const errors: string[] = []

    if ('name' in data) {
      if (!data.name || data.name.trim().length === 0) {
        errors.push('Playlist name is required')
      } else if (data.name.trim().length < 2) {
        errors.push('Playlist name must be at least 2 characters')
      } else if (data.name.trim().length > 100) {
        errors.push('Playlist name must be less than 100 characters')
      }
    }

    if (data.description && data.description.length > 500) {
      errors.push('Description must be less than 500 characters')
    }

    if (data.cover_image_url && !this.isValidUrl(data.cover_image_url)) {
      errors.push('Cover image URL is not valid')
    }

    return errors
  }

  // URL validation helper
  private static isValidUrl(string: string): boolean {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  // Batch operations for better performance
  static async batchUpdatePlaylistTracks(
    playlistId: string,
    operations: Array<{
      type: 'add' | 'remove' | 'reorder'
      trackId?: string
      trackIds?: string[]
      position?: number
      newPosition?: number
    }>
  ): Promise<void> {
    // Group operations by type for efficiency
    const addOps = operations.filter(op => op.type === 'add')
    const removeOps = operations.filter(op => op.type === 'remove')
    const reorderOps = operations.filter(op => op.type === 'reorder')

    try {
      // Execute remove operations first
      if (removeOps.length > 0) {
        const trackIds = removeOps.flatMap(op => op.trackIds || (op.trackId ? [op.trackId] : []))
        if (trackIds.length > 0) {
          await this.removeTracksFromPlaylist(playlistId, trackIds)
        }
      }

      // Execute add operations
      if (addOps.length > 0) {
        const trackIds = addOps.flatMap(op => op.trackIds || (op.trackId ? [op.trackId] : []))
        if (trackIds.length > 0) {
          await this.addTracksToPlaylist(playlistId, trackIds)
        }
      }

      // Execute reorder operations
      if (reorderOps.length > 0) {
        const reorderData: ReorderTrackData[] = reorderOps
          .filter(op => op.trackId && op.newPosition !== undefined)
          .map(op => ({
            playlist_id: playlistId,
            track_id: op.trackId!,
            new_position: op.newPosition!
          }))
        
        if (reorderData.length > 0) {
          await this.reorderPlaylistTracks(playlistId, reorderData)
        }
      }
    } catch (error) {
      throw new Error(`Batch operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
} 