// Profile Header Component
'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ExtendedProfile, UserStats } from '@/types/profile'
import { formatListeningTime } from '@/lib/profile'

interface ProfileHeaderProps {
  profile: ExtendedProfile
  stats: UserStats | null
  isOwnProfile: boolean
  onEditClick?: () => void
}

export default function ProfileHeader({ 
  profile, 
  stats, 
  isOwnProfile, 
  onEditClick 
}: ProfileHeaderProps) {
  const [imageError, setImageError] = useState(false)

  const handleImageError = () => {
    setImageError(true)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <div className="relative w-32 h-32 mx-auto md:mx-0">
            {profile.avatar_url && !imageError ? (
              <Image
                src={profile.avatar_url}
                alt={profile.full_name || 'Profile'}
                fill
                className="rounded-full object-cover border-4 border-purple-200 dark:border-purple-800"
                onError={handleImageError}
              />
            ) : (
              <div className="w-full h-full rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center border-4 border-purple-200 dark:border-purple-800">
                <span className="text-white text-2xl font-bold">
                  {profile.full_name ? getInitials(profile.full_name) : '?'}
                </span>
              </div>
            )}
            
            {/* Premium Badge */}
            {profile.is_premium && (
              <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                ⭐ PRO
              </div>
            )}
          </div>
        </div>

        {/* Profile Info */}
        <div className="flex-1 text-center md:text-left">
          <div className="mb-4">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {profile.full_name || 'Anonymous User'}
            </h1>
            
            {profile.username && (
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-2">
                @{profile.username}
              </p>
            )}
            
            {profile.location && (
              <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center justify-center md:justify-start gap-1">
                <span>📍</span>
                {profile.location}
              </p>
            )}
          </div>

          {/* Bio */}
          {profile.bio && (
            <p className="text-gray-700 dark:text-gray-300 mb-4 max-w-2xl">
              {profile.bio}
            </p>
          )}

          {/* Social Links */}
          {(profile.website_url || profile.twitter_url || profile.instagram_url || 
            profile.youtube_url || profile.spotify_url || profile.soundcloud_url) && (
            <div className="flex flex-wrap gap-3 justify-center md:justify-start mb-4">
              {profile.website_url && (
                <a
                  href={profile.website_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-600 transition-colors"
                  title="Website"
                >
                  🌐
                </a>
              )}
              {profile.twitter_url && (
                <a
                  href={profile.twitter_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-500 transition-colors"
                  title="Twitter"
                >
                  🐦
                </a>
              )}
              {profile.instagram_url && (
                <a
                  href={profile.instagram_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-pink-500 hover:text-pink-600 transition-colors"
                  title="Instagram"
                >
                  📷
                </a>
              )}
              {profile.youtube_url && (
                <a
                  href={profile.youtube_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-red-500 hover:text-red-600 transition-colors"
                  title="YouTube"
                >
                  📺
                </a>
              )}
              {profile.spotify_url && (
                <a
                  href={profile.spotify_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-green-500 hover:text-green-600 transition-colors"
                  title="Spotify"
                >
                  🎵
                </a>
              )}
              {profile.soundcloud_url && (
                <a
                  href={profile.soundcloud_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-orange-500 hover:text-orange-600 transition-colors"
                  title="SoundCloud"
                >
                  ☁️
                </a>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 justify-center md:justify-start">
            {isOwnProfile ? (
              <>
                <button
                  onClick={onEditClick}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Edit Profile
                </button>
                <Link
                  href="/profile/settings"
                  className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg font-medium transition-colors"
                >
                  Settings
                </Link>
              </>
            ) : (
              <>
                <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                  Follow
                </button>
                <button className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg font-medium transition-colors">
                  Message
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Stats */}
      {stats && (
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {formatNumber(stats.tracks_uploaded)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Tracks</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {formatNumber(stats.playlists_created)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Playlists</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {formatNumber(stats.total_plays)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Plays</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {formatNumber(stats.total_likes_received)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Likes</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {formatListeningTime(stats.total_listening_time)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Listened</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                {formatNumber(stats.tracks_liked)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Liked</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 