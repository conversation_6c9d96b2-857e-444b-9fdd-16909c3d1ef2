'use client'

import { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase, auth } from '@/lib/supabase'
import type { Profile } from '@/types/database'
import { authPerformance, withAuthPerformance, createDebouncedAuthHandler } from '@/lib/auth-performance'

interface AuthContextType {
  user: User | null
  session: Session | null
  profile: Profile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signUp: (email: string, password: string, options?: { data?: { full_name?: string; username?: string } }) => Promise<{ data: any; error: any }>
  signInWithGoogle: (options?: { redirectTo?: string }) => Promise<{ data: any; error: any }>
  signInWithGitHub: (options?: { redirectTo?: string }) => Promise<{ data: any; error: any }>
  signOut: () => Promise<{ error: any }>
  updateProfile: (updates: Partial<Profile>) => Promise<{ data: Profile | null; error: any }>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Profile cache to avoid redundant database calls
const profileCache = new Map<string, { profile: Profile | null; timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)
  
  // Refs to prevent race conditions
  const currentUserIdRef = useRef<string | null>(null)
  const profileFetchingRef = useRef<boolean>(false)

  // Optimized profile fetching with performance monitoring
  const fetchProfile = useCallback(async (userId: string): Promise<Profile | null> => {
    return authPerformance.optimizedProfileFetch(userId, async (id: string) => {
      // Prevent concurrent fetches for the same user
      if (profileFetchingRef.current) {
        return null
      }

      try {
        profileFetchingRef.current = true
        
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single()

        if (error && error.code !== 'PGRST116') {
          console.error('Error fetching profile:', error)
          return null
        }

        return data
      } catch (error) {
        console.error('Error fetching profile:', error)
        return null
      } finally {
        profileFetchingRef.current = false
      }
    })
  }, [])

  // Optimized profile creation
  const createProfile = useCallback(async (user: User): Promise<Profile | null> => {
    try {
      const profileData = {
        id: user.id,
        email: user.email!,
        full_name: user.user_metadata?.full_name || null,
        username: user.user_metadata?.username || null,
        avatar_url: user.user_metadata?.avatar_url || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      const { data, error } = await supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single()

      if (error) {
        console.error('Error creating profile:', error)
        return null
      }

      // Cache the new profile
      profileCache.set(user.id, { profile: data, timestamp: Date.now() })
      return data
    } catch (error) {
      console.error('Error creating profile:', error)
      return null
    }
  }, [])

  // Handle auth state changes efficiently with performance monitoring
  const handleAuthStateChange = useCallback(async (event: string, session: Session | null) => {
    const newUser = session?.user ?? null
    const newUserId = newUser?.id ?? null

    // Prevent unnecessary updates
    if (currentUserIdRef.current === newUserId && initialized) {
      return
    }

    // Track auth performance
    if (event === 'SIGNED_IN' || event === 'SIGNED_UP') {
      authPerformance.startLoginTimer()
    }

    currentUserIdRef.current = newUserId
    setSession(session)
    setUser(newUser)

    if (newUser) {
      // Only fetch profile if we don't have it or it's a new user
      if (!profile || profile.id !== newUser.id) {
        let userProfile = await fetchProfile(newUser.id)
        
        // Create profile if it doesn't exist (for new sign ups)
        if (!userProfile && (event === 'SIGNED_IN' || event === 'SIGNED_UP')) {
          userProfile = await createProfile(newUser)
        }
        
        setProfile(userProfile)
      }
    } else {
      setProfile(null)
      // Clear cache when user signs out
      authPerformance.clearCache()
    }

    if (!initialized) {
      setInitialized(true)
    }
    setLoading(false)

    // End performance tracking and log metrics
    if (event === 'SIGNED_IN' || event === 'SIGNED_UP') {
      authPerformance.endLoginTimer()
      authPerformance.logPerformanceMetrics()
    }
  }, [profile, initialized, fetchProfile, createProfile])

  // Initialize auth state with performance optimizations
  useEffect(() => {
    let mounted = true

    async function getInitialSession() {
      try {
        // Initialize performance optimizations
        await authPerformance.initialize()

        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
          setLoading(false)
          return
        }

        if (mounted) {
          await handleAuthStateChange('INITIAL_SESSION', session)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        if (mounted) {
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Use debounced auth state change handler for better performance
    const debouncedHandler = createDebouncedAuthHandler(handleAuthStateChange, 50)

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (mounted) {
        await debouncedHandler(event, session)
      }
    })

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [handleAuthStateChange])

  // Optimized sign in function with performance tracking
  const signIn = useCallback(
    withAuthPerformance(async (email: string, password: string) => {
      try {
        setLoading(true)
        const result = await auth.signIn(email, password)
        return result
      } catch (error) {
        console.error('Sign in error:', error)
        return { data: null, error }
      } finally {
        // Don't set loading to false here, let auth state change handle it
      }
    }, 'Email Sign In'),
    []
  )

  // Optimized sign up function
  const signUp = useCallback(async (
    email: string,
    password: string,
    options?: { data?: { full_name?: string; username?: string } }
  ) => {
    try {
      setLoading(true)
      const result = await auth.signUp(email, password, options)
      return result
    } catch (error) {
      console.error('Sign up error:', error)
      return { data: null, error }
    } finally {
      // Don't set loading to false here, let auth state change handle it
    }
  }, [])

  // Google OAuth sign in
  const signInWithGoogle = useCallback(async (options?: { redirectTo?: string }) => {
    try {
      setLoading(true)
      const result = await auth.signInWithGoogle(options)
      return result
    } catch (error) {
      console.error('Google sign in error:', error)
      return { data: null, error }
    } finally {
      // Don't set loading to false here, OAuth redirect will handle it
    }
  }, [])

  // GitHub OAuth sign in
  const signInWithGitHub = useCallback(async (options?: { redirectTo?: string }) => {
    try {
      setLoading(true)
      const result = await auth.signInWithGitHub(options)
      return result
    } catch (error) {
      console.error('GitHub sign in error:', error)
      return { data: null, error }
    } finally {
      // Don't set loading to false here, OAuth redirect will handle it
    }
  }, [])

  // Optimized sign out function
  const signOut = useCallback(async () => {
    try {
      setLoading(true)
      const result = await auth.signOut()
      
      if (!result.error) {
        setUser(null)
        setSession(null)
        setProfile(null)
        currentUserIdRef.current = null
        profileCache.clear()
      }
      
      return result
    } catch (error) {
      console.error('Sign out error:', error)
      return { error }
    } finally {
      setLoading(false)
    }
  }, [])

  // Update profile function with cache invalidation
  const updateProfile = useCallback(async (updates: Partial<Profile>) => {
    if (!user) {
      return { data: null, error: { message: 'No user logged in' } }
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', user.id)
        .select()
        .single()

      if (!error && data) {
        setProfile(data)
        // Update cache
        profileCache.set(user.id, { profile: data, timestamp: Date.now() })
      }

      return { data, error }
    } catch (error) {
      console.error('Error updating profile:', error)
      return { data: null, error }
    }
  }, [user])

  // Refresh profile function
  const refreshProfile = useCallback(async () => {
    if (!user) return

    // Clear cache and fetch fresh data
    profileCache.delete(user.id)
    const freshProfile = await fetchProfile(user.id)
    setProfile(freshProfile)
  }, [user, fetchProfile])

  const value: AuthContextType = {
    user,
    session,
    profile,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signInWithGitHub,
    signOut,
    updateProfile,
    refreshProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function useRequireAuth() {
  const auth = useAuth()
  
  useEffect(() => {
    if (!auth.loading && !auth.user) {
      window.location.href = '/auth/login'
    }
  }, [auth.loading, auth.user])

  return auth
} 