// PWA Utility Functions for Tunami

export interface PWACapabilities {
  isSupported: boolean
  canInstall: boolean
  isInstalled: boolean
  isStandalone: boolean
  supportsServiceWorker: boolean
  supportsPushNotifications: boolean
  supportsBackgroundSync: boolean
  supportsWebShare: boolean
  supportsMediaSession: boolean
  supportsWakeLock: boolean
  supportsBadge: boolean
}

export function checkPWACapabilities(): PWACapabilities {
  if (typeof window === 'undefined') {
    return {
      isSupported: false,
      canInstall: false,
      isInstalled: false,
      isStandalone: false,
      supportsServiceWorker: false,
      supportsPushNotifications: false,
      supportsBackgroundSync: false,
      supportsWebShare: false,
      supportsMediaSession: false,
      supportsWakeLock: false,
      supportsBadge: false,
    }
  }

  const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                     (window.navigator as any).standalone === true ||
                     document.referrer.includes('android-app://')

  const supportsServiceWorker = 'serviceWorker' in navigator
  const supportsPushNotifications = 'PushManager' in window && 'Notification' in window
  const supportsBackgroundSync = 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype
  const supportsWebShare = 'share' in navigator
  const supportsMediaSession = 'mediaSession' in navigator
  const supportsWakeLock = 'wakeLock' in navigator
  const supportsBadge = 'setAppBadge' in navigator

  return {
    isSupported: supportsServiceWorker,
    canInstall: !isStandalone && supportsServiceWorker,
    isInstalled: isStandalone,
    isStandalone,
    supportsServiceWorker,
    supportsPushNotifications,
    supportsBackgroundSync,
    supportsWebShare,
    supportsMediaSession,
    supportsWakeLock,
    supportsBadge,
  }
}

export function isStandaloneApp(): boolean {
  if (typeof window === 'undefined') return false
  
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true ||
         document.referrer.includes('android-app://')
}

export function canInstallPWA(): boolean {
  return !isStandaloneApp() && 'serviceWorker' in navigator
}

export async function requestWakeLock(): Promise<WakeLockSentinel | null> {
  if (typeof window === 'undefined' || !('wakeLock' in navigator)) {
    return null
  }

  try {
    const wakeLock = await (navigator as any).wakeLock.request('screen')
    console.log('🎵 PWA: Wake lock acquired')
    return wakeLock
  } catch (error) {
    console.error('🎵 PWA: Wake lock failed:', error)
    return null
  }
}

export async function releaseWakeLock(wakeLock: WakeLockSentinel | null): Promise<void> {
  if (wakeLock) {
    try {
      await wakeLock.release()
      console.log('🎵 PWA: Wake lock released')
    } catch (error) {
      console.error('🎵 PWA: Wake lock release failed:', error)
    }
  }
}

export async function setAppBadge(count?: number): Promise<boolean> {
  if (typeof window === 'undefined' || !('setAppBadge' in navigator)) {
    return false
  }

  try {
    if (count === undefined) {
      await (navigator as any).setAppBadge()
    } else {
      await (navigator as any).setAppBadge(count)
    }
    return true
  } catch (error) {
    console.error('🎵 PWA: Set app badge failed:', error)
    return false
  }
}

export async function clearAppBadge(): Promise<boolean> {
  if (typeof window === 'undefined' || !('clearAppBadge' in navigator)) {
    return false
  }

  try {
    await (navigator as any).clearAppBadge()
    return true
  } catch (error) {
    console.error('🎵 PWA: Clear app badge failed:', error)
    return false
  }
}

export async function shareContent(data: ShareData): Promise<boolean> {
  if (typeof window === 'undefined' || !('share' in navigator)) {
    return false
  }

  try {
    await navigator.share(data)
    return true
  } catch (error) {
    if ((error as Error).name !== 'AbortError') {
      console.error('🎵 PWA: Share failed:', error)
    }
    return false
  }
}

export function setupMediaSession(metadata: MediaMetadataInit): void {
  if (typeof window === 'undefined' || !('mediaSession' in navigator)) {
    return
  }

  try {
    navigator.mediaSession.metadata = new MediaMetadata(metadata)
    console.log('🎵 PWA: Media session metadata set')
  } catch (error) {
    console.error('🎵 PWA: Media session setup failed:', error)
  }
}

export function setMediaSessionActionHandlers(handlers: {
  play?: () => void
  pause?: () => void
  stop?: () => void
  seekbackward?: () => void
  seekforward?: () => void
  previoustrack?: () => void
  nexttrack?: () => void
}): void {
  if (typeof window === 'undefined' || !('mediaSession' in navigator)) {
    return
  }

  try {
    Object.entries(handlers).forEach(([action, handler]) => {
      navigator.mediaSession.setActionHandler(action as MediaSessionAction, handler)
    })
    console.log('🎵 PWA: Media session action handlers set')
  } catch (error) {
    console.error('🎵 PWA: Media session action handlers failed:', error)
  }
}

export function updateMediaSessionPosition(position: MediaPositionState): void {
  if (typeof window === 'undefined' || !('mediaSession' in navigator)) {
    return
  }

  try {
    navigator.mediaSession.setPositionState(position)
  } catch (error) {
    console.error('🎵 PWA: Media session position update failed:', error)
  }
}

export async function cacheAudioFile(url: string): Promise<boolean> {
  if (!('serviceWorker' in navigator) || !navigator.serviceWorker.controller) {
    return false
  }

  try {
    navigator.serviceWorker.controller.postMessage({
      type: 'CACHE_AUDIO',
      payload: { url }
    })
    return true
  } catch (error) {
    console.error('🎵 PWA: Cache audio file failed:', error)
    return false
  }
}

export async function queueUploadForSync(upload: any): Promise<boolean> {
  if (!('serviceWorker' in navigator) || !navigator.serviceWorker.controller) {
    return false
  }

  try {
    navigator.serviceWorker.controller.postMessage({
      type: 'QUEUE_UPLOAD',
      payload: { upload }
    })
    return true
  } catch (error) {
    console.error('🎵 PWA: Queue upload failed:', error)
    return false
  }
}

export function getInstallPromptBehavior(): 'auto' | 'manual' | 'hidden' {
  // Check user's previous interaction with install prompt
  const dismissed = localStorage.getItem('tunami-install-dismissed')
  const installed = isStandaloneApp()
  
  if (installed) return 'hidden'
  
  if (dismissed) {
    const dismissedTime = parseInt(dismissed)
    const weekInMs = 7 * 24 * 60 * 60 * 1000
    
    // Show again after a week
    if (Date.now() - dismissedTime < weekInMs) {
      return 'manual'
    }
  }
  
  return 'auto'
}

export function trackPWAInstallation(): void {
  // Track PWA installation for analytics
  const event = new CustomEvent('pwa-installed', {
    detail: {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      standalone: isStandaloneApp()
    }
  })
  
  window.dispatchEvent(event)
  
  // Store in localStorage for analytics
  localStorage.setItem('tunami-pwa-installed', JSON.stringify({
    timestamp: Date.now(),
    version: '1.0.0'
  }))
}

export function getPWAInstallationInfo(): { isInstalled: boolean; timestamp?: number; version?: string } {
  const info = localStorage.getItem('tunami-pwa-installed')
  
  if (!info) {
    return { isInstalled: false }
  }
  
  try {
    const parsed = JSON.parse(info)
    return {
      isInstalled: true,
      timestamp: parsed.timestamp,
      version: parsed.version
    }
  } catch {
    return { isInstalled: false }
  }
}

export function optimizeForPWA(): void {
  if (typeof window === 'undefined') return
  
  // Apply PWA-specific optimizations
  if (isStandaloneApp()) {
    // Hide browser-specific UI elements
    document.documentElement.classList.add('pwa-standalone')
    
    // Prevent zoom on iOS
    const viewport = document.querySelector('meta[name="viewport"]')
    if (viewport && /iPhone|iPad|iPod/.test(navigator.userAgent)) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover'
      )
    }
    
    // Handle safe area insets
    if (CSS.supports('padding: env(safe-area-inset-top)')) {
      document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)')
      document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')
      document.documentElement.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)')
      document.documentElement.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)')
    }
  }
}

export function handlePWAUpdates(): void {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) return
  
  navigator.serviceWorker.addEventListener('message', (event) => {
    if (event.data?.type === 'SW_UPDATED') {
      // Show update notification
      const updateEvent = new CustomEvent('pwa-update-available', {
        detail: { version: event.data.version }
      })
      window.dispatchEvent(updateEvent)
    }
  })
}

export function preloadCriticalAudio(urls: string[]): void {
  if (!('serviceWorker' in navigator) || !navigator.serviceWorker.controller) {
    return
  }

  navigator.serviceWorker.controller.postMessage({
    type: 'PRELOAD_CRITICAL',
    payload: { urls }
  })
}

export function getOfflineCapabilities(): {
  canPlayCachedAudio: boolean
  canUploadWhenOnline: boolean
  canBrowseOffline: boolean
} {
  const capabilities = checkPWACapabilities()
  
  return {
    canPlayCachedAudio: capabilities.supportsServiceWorker,
    canUploadWhenOnline: capabilities.supportsBackgroundSync,
    canBrowseOffline: capabilities.supportsServiceWorker
  }
}

export async function checkCacheStatus(): Promise<{
  audioFiles: number
  totalSize: number
  lastUpdated: Date | null
}> {
  if (!('caches' in window)) {
    return { audioFiles: 0, totalSize: 0, lastUpdated: null }
  }

  try {
    const cache = await caches.open('tunami-audio-v1.0.0')
    const requests = await cache.keys()
    
    let totalSize = 0
    let lastUpdated: Date | null = null
    
    for (const request of requests) {
      const response = await cache.match(request)
      if (response) {
        const size = parseInt(response.headers.get('content-length') || '0')
        totalSize += size
        
        const dateHeader = response.headers.get('date')
        if (dateHeader) {
          const date = new Date(dateHeader)
          if (!lastUpdated || date > lastUpdated) {
            lastUpdated = date
          }
        }
      }
    }
    
    return {
      audioFiles: requests.length,
      totalSize,
      lastUpdated
    }
  } catch (error) {
    console.error('🎵 PWA: Cache status check failed:', error)
    return { audioFiles: 0, totalSize: 0, lastUpdated: null }
  }
}

export function formatCacheSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Initialize PWA optimizations
if (typeof window !== 'undefined') {
  // Apply optimizations when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', optimizeForPWA)
  } else {
    optimizeForPWA()
  }
  
  // Handle PWA updates
  handlePWAUpdates()
} 