// Recommendations Section Component
'use client'

import { useState } from 'react'
import { 
  Target, 
  Play, 
  MoreHorizontal, 
  RefreshCw,
  Music,
  Heart,
  Eye,
  Sparkles,
  TrendingUp,
  Users,
  Shuffle,
  Info
} from 'lucide-react'
import { PersonalizedRecommendation } from '@/types/dashboard'
import { useAudio } from '@/contexts/AudioContext'
import { convertTracksToAudioTracks } from '@/lib/audio-utils'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { EmptyState } from '@/components/ui/EmptyState'

interface RecommendationsSectionProps {
  recommendations: PersonalizedRecommendation[]
  loading: boolean
  error: string | null
  onRefresh: () => void
  onToggleLike: (trackId: string) => Promise<{ success: boolean; liked: boolean; error: string | null }>
}

export default function RecommendationsSection({
  recommendations,
  loading,
  error,
  onRefresh,
  onToggleLike
}: RecommendationsSectionProps) {
  const { setQueue } = useAudio()
  const [likingTracks, setLikingTracks] = useState<Set<string>>(new Set())
  const [showExplanations, setShowExplanations] = useState(false)
  const [actionErrors, setActionErrors] = useState<Record<string, string>>({})

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getReasonIcon = (reason: string) => {
    switch (reason) {
      case 'similar_genre':
        return <Music className="w-4 h-4 text-blue-400" />
      case 'similar_artist':
        return <Users className="w-4 h-4 text-green-400" />
      case 'similar_mood':
        return <Sparkles className="w-4 h-4 text-purple-400" />
      case 'trending':
        return <TrendingUp className="w-4 h-4 text-orange-400" />
      default:
        return <Target className="w-4 h-4 text-gray-400" />
    }
  }

  const getReasonText = (reason: string) => {
    switch (reason) {
      case 'similar_genre':
        return 'Similar Genre'
      case 'similar_artist':
        return 'Similar Artist'
      case 'similar_mood':
        return 'Similar Mood'
      case 'trending':
        return 'Trending'
      default:
        return 'Recommended'
    }
  }

  const getReasonColor = (reason: string) => {
    switch (reason) {
      case 'similar_genre':
        return 'bg-blue-600'
      case 'similar_artist':
        return 'bg-green-600'
      case 'similar_mood':
        return 'bg-purple-600'
      case 'trending':
        return 'bg-orange-600'
      default:
        return 'bg-gray-600'
    }
  }

  const getAIToolIcon = (tool: string) => {
    const icons: Record<string, string> = {
      suno: '🎵',
      udio: '🎶',
      mubert: '🎼',
      aiva: '🎹',
      amper: '🎸',
      custom: '⚡',
      other: '🎧'
    }
    return icons[tool] || '🎧'
  }

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-400'
    if (score >= 0.6) return 'text-yellow-400'
    return 'text-red-400'
  }

  const handlePlayTrack = (recommendation: PersonalizedRecommendation) => {
    try {
      const audioTracks = convertTracksToAudioTracks([recommendation.track])
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        setActionErrors(prev => ({ ...prev, [`play-${recommendation.track.id}`]: '' }))
      } else {
        setActionErrors(prev => ({ ...prev, [`play-${recommendation.track.id}`]: 'Unable to play this track' }))
      }
    } catch (error) {
      setActionErrors(prev => ({ ...prev, [`play-${recommendation.track.id}`]: 'Failed to play track' }))
    }
  }

  const handlePlayAll = () => {
    try {
      const tracks = recommendations.map(rec => rec.track)
      const audioTracks = convertTracksToAudioTracks(tracks)
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        setActionErrors(prev => ({ ...prev, 'play-all': '' }))
      } else {
        setActionErrors(prev => ({ ...prev, 'play-all': 'No tracks available to play' }))
      }
    } catch (error) {
      setActionErrors(prev => ({ ...prev, 'play-all': 'Failed to play tracks' }))
    }
  }

  const handleShuffle = () => {
    try {
      const shuffled = [...recommendations].sort(() => Math.random() - 0.5)
      const tracks = shuffled.map(rec => rec.track)
      const audioTracks = convertTracksToAudioTracks(tracks)
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        setActionErrors(prev => ({ ...prev, 'shuffle': '' }))
      } else {
        setActionErrors(prev => ({ ...prev, 'shuffle': 'No tracks available to shuffle' }))
      }
    } catch (error) {
      setActionErrors(prev => ({ ...prev, 'shuffle': 'Failed to shuffle tracks' }))
    }
  }

  const handleLike = async (trackId: string) => {
    if (likingTracks.has(trackId)) return

    setLikingTracks(prev => new Set(prev).add(trackId))
    setActionErrors(prev => ({ ...prev, [`like-${trackId}`]: '' }))
    
    try {
      const result = await onToggleLike(trackId)
      if (!result.success) {
        setActionErrors(prev => ({ 
          ...prev, 
          [`like-${trackId}`]: result.error || 'Failed to like track' 
        }))
      }
    } catch (error) {
      setActionErrors(prev => ({ 
        ...prev, 
        [`like-${trackId}`]: error instanceof Error ? error.message : 'Failed to like track' 
      }))
    } finally {
      setLikingTracks(prev => {
        const newSet = new Set(prev)
        newSet.delete(trackId)
        return newSet
      })
    }
  }

  // Loading state
  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Target className="w-5 h-5 text-purple-400" />
            Personalized Recommendations
          </h2>
          <LoadingSpinner size="sm" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" role="status" aria-label="Loading recommendations">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="bg-gray-700 rounded-lg p-4 animate-pulse">
              <div className="w-full aspect-square bg-gray-600 rounded-lg mb-3" />
              <div className="h-4 bg-gray-600 rounded w-3/4 mb-2" />
              <div className="h-3 bg-gray-600 rounded w-1/2" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Target className="w-5 h-5 text-purple-400" />
            Personalized Recommendations
          </h2>
        </div>
        
        <ErrorMessage
          message={error}
          onRetry={onRefresh}
          retryLabel="Reload Recommendations"
          severity="error"
          className="max-w-2xl"
        />
      </div>
    )
  }

  // Empty state
  if (recommendations.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Target className="w-5 h-5 text-purple-400" />
            Personalized Recommendations
          </h2>
        </div>
        
        <EmptyState
          icon={Target}
          title="No Recommendations Available"
          description="Listen to more music to get personalized recommendations!"
          action={{
            label: "Browse Music",
            href: "/browse"
          }}
        />
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white flex items-center gap-2">
          <Target className="w-5 h-5 text-purple-400" />
          Personalized Recommendations
          <span className="text-sm text-gray-400 font-normal">({recommendations.length})</span>
        </h2>
        
        <div className="flex items-center gap-3">
          {/* Show Explanations Toggle */}
          <button
            onClick={() => setShowExplanations(!showExplanations)}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
              showExplanations ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            aria-label={`${showExplanations ? 'Hide' : 'Show'} recommendation explanations`}
            title={`${showExplanations ? 'Hide' : 'Show'} explanations`}
          >
            <Info className="w-4 h-4" />
            {showExplanations ? 'Hide Details' : 'Show Details'}
          </button>

          {/* Action Buttons */}
          <button
            onClick={handlePlayAll}
            disabled={loading || recommendations.length === 0}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={`Play all ${recommendations.length} recommended tracks`}
          >
            <Play className="w-4 h-4" />
            Play All
          </button>

          <button
            onClick={handleShuffle}
            disabled={loading || recommendations.length === 0}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={`Shuffle ${recommendations.length} recommended tracks`}
          >
            <Shuffle className="w-4 h-4" />
            Shuffle
          </button>

          {/* Refresh Button */}
          <button
            onClick={onRefresh}
            disabled={loading}
            className="text-gray-400 hover:text-white transition-colors disabled:opacity-50"
            aria-label="Refresh recommendations"
            title="Refresh recommendations"
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Action Errors */}
      {Object.entries(actionErrors).map(([key, error]) => 
        error && (
          <ErrorMessage
            key={key}
            message={error}
            severity="warning"
            onDismiss={() => setActionErrors(prev => ({ ...prev, [key]: '' }))}
            className="mb-4"
          />
        )
      )}

      {/* Recommendations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {recommendations.map((recommendation) => (
          <div
            key={recommendation.track.id}
            className="bg-gray-700 hover:bg-gray-600 rounded-lg p-4 transition-colors group"
          >
            {/* Track Cover */}
            <div className="relative w-full aspect-square mb-3">
              <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-3xl">
                  {getAIToolIcon(recommendation.track.ai_tool)}
                </span>
              </div>
              
              {/* Reason Badge */}
              <div className={`absolute top-2 left-2 ${getReasonColor(recommendation.reason)} text-white px-2 py-1 rounded text-xs font-medium flex items-center gap-1`}>
                {getReasonIcon(recommendation.reason)}
                {getReasonText(recommendation.reason)}
              </div>

              {/* Confidence Score */}
              <div className={`absolute top-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs font-medium ${getConfidenceColor(recommendation.confidence_score)}`}>
                {Math.round(recommendation.confidence_score * 100)}%
              </div>

              {/* Play Button */}
              <button
                onClick={() => handlePlayTrack(recommendation)}
                className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <Play className="w-8 h-8 text-white" />
              </button>
            </div>

            {/* Track Info */}
            <div className="mb-3">
              <h4 className="font-medium text-white truncate mb-1">{recommendation.track.title}</h4>
              <p className="text-sm text-gray-400 truncate mb-1">{recommendation.track.artist_name}</p>
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span className="capitalize">{recommendation.track.genre}</span>
                <span>{formatDuration(recommendation.track.duration || 0)}</span>
              </div>
            </div>

            {/* Explanation */}
            {showExplanations && (
              <div className="mb-3 p-2 bg-gray-600 rounded text-xs text-gray-300">
                {recommendation.explanation}
              </div>
            )}

            {/* Stats & Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 text-xs text-gray-400">
                <div className="flex items-center gap-1">
                  <Eye className="w-3 h-3" />
                  <span>{recommendation.track.play_count || 0}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Heart className="w-3 h-3" />
                  <span>{recommendation.track.like_count || 0}</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Like Button */}
                <button
                  onClick={() => handleLike(recommendation.track.id)}
                  disabled={likingTracks.has(recommendation.track.id)}
                  className="text-gray-400 hover:text-red-400 transition-colors disabled:opacity-50"
                >
                  <Heart className="w-4 h-4" />
                </button>

                {/* More Options */}
                <button className="text-gray-400 hover:text-white transition-colors">
                  <MoreHorizontal className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recommendation Insights */}
      <div className="mt-6 pt-6 border-t border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4">Recommendation Insights</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(
            recommendations.reduce((acc, rec) => {
              acc[rec.reason] = (acc[rec.reason] || 0) + 1
              return acc
            }, {} as Record<string, number>)
          ).map(([reason, count]) => (
            <div key={reason} className="text-center">
              <div className="flex items-center justify-center mb-2">
                {getReasonIcon(reason)}
              </div>
              <div className="text-lg font-bold text-white">{count}</div>
              <div className="text-sm text-gray-400">{getReasonText(reason)}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Average Confidence */}
      <div className="mt-4 text-center">
        <div className="text-sm text-gray-400">
          Average Confidence: 
          <span className={`ml-1 font-medium ${getConfidenceColor(
            recommendations.reduce((sum, rec) => sum + rec.confidence_score, 0) / recommendations.length
          )}`}>
            {Math.round((recommendations.reduce((sum, rec) => sum + rec.confidence_score, 0) / recommendations.length) * 100)}%
          </span>
        </div>
      </div>
    </div>
  )
} 