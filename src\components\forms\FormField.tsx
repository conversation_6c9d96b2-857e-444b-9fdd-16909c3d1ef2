'use client'

import { useState, useEffect, forwardRef } from 'react'
import { Eye, EyeOff, AlertCircle, CheckCircle, Loader2 } from 'lucide-react'

export interface FormFieldProps {
  label: string
  name: string
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea' | 'select'
  placeholder?: string
  value?: string
  defaultValue?: string
  required?: boolean
  disabled?: boolean
  loading?: boolean
  error?: string | null
  success?: string | null
  helperText?: string
  maxLength?: number
  minLength?: number
  pattern?: string
  autoComplete?: string
  autoFocus?: boolean
  rows?: number // for textarea
  options?: Array<{ value: string; label: string }> // for select
  validate?: (value: string) => string | null
  validateOnBlur?: boolean
  validateOnChange?: boolean
  debounceMs?: number
  className?: string
  inputClassName?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  onFocus?: () => void
  onValidation?: (isValid: boolean, error: string | null) => void
}

const FormField = forwardRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement, FormFieldProps>(
  ({
    label,
    name,
    type = 'text',
    placeholder,
    value,
    defaultValue,
    required = false,
    disabled = false,
    loading = false,
    error,
    success,
    helperText,
    maxLength,
    minLength,
    pattern,
    autoComplete,
    autoFocus = false,
    rows = 4,
    options = [],
    validate,
    validateOnBlur = true,
    validateOnChange = false,
    debounceMs = 300,
    className = '',
    inputClassName = '',
    onChange,
    onBlur,
    onFocus,
    onValidation
  }, ref) => {
    const [internalValue, setInternalValue] = useState(value || defaultValue || '')
    const [internalError, setInternalError] = useState<string | null>(null)
    const [showPassword, setShowPassword] = useState(false)
    const [isFocused, setIsFocused] = useState(false)
    const [isValidating, setIsValidating] = useState(false)
    const [hasBeenBlurred, setHasBeenBlurred] = useState(false)

    // Debounced validation
    useEffect(() => {
      if (!validate || !validateOnChange || !hasBeenBlurred) return

      const timeoutId = setTimeout(async () => {
        if (internalValue !== '') {
          setIsValidating(true)
          const validationError = await Promise.resolve(validate(internalValue))
          setInternalError(validationError)
          onValidation?.(!validationError, validationError)
          setIsValidating(false)
        }
      }, debounceMs)

      return () => clearTimeout(timeoutId)
    }, [internalValue, validate, validateOnChange, debounceMs, hasBeenBlurred, onValidation])

    // Update internal value when prop changes
    useEffect(() => {
      if (value !== undefined) {
        setInternalValue(value)
      }
    }, [value])

    const handleChange = (newValue: string) => {
      setInternalValue(newValue)
      onChange?.(newValue)

      // Clear error when user starts typing
      if (internalError && newValue !== '') {
        setInternalError(null)
      }
    }

    const handleBlur = async () => {
      setIsFocused(false)
      setHasBeenBlurred(true)
      onBlur?.()

      // Validate on blur if enabled
      if (validate && validateOnBlur && internalValue !== '') {
        setIsValidating(true)
        const validationError = await Promise.resolve(validate(internalValue))
        setInternalError(validationError)
        onValidation?.(!validationError, validationError)
        setIsValidating(false)
      }
    }

    const handleFocus = () => {
      setIsFocused(true)
      onFocus?.()
    }

    // Determine current error state
    const currentError = error || internalError
    const hasError = Boolean(currentError)
    const hasSuccess = Boolean(success) && !hasError && !isValidating
    const isLoading = loading || isValidating

    // Base input classes
    const baseInputClasses = `
      w-full px-3 py-2 border rounded-lg transition-all duration-200
      focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent
      disabled:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50
      ${hasError 
        ? 'border-red-500 bg-red-50 text-red-900 placeholder-red-400' 
        : hasSuccess 
          ? 'border-green-500 bg-green-50 text-green-900' 
          : isFocused 
            ? 'border-purple-500 bg-white' 
            : 'border-gray-300 bg-white text-gray-900'
      }
      ${inputClassName}
    `

    const renderInput = () => {
      const commonProps = {
        id: name,
        name,
        value: internalValue,
        placeholder,
        required,
        disabled: disabled || isLoading,
        autoComplete,
        autoFocus,
        maxLength,
        minLength,
        pattern,
        className: baseInputClasses,
        onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => 
          handleChange(e.target.value),
        onBlur: handleBlur,
        onFocus: handleFocus
      }

      switch (type) {
        case 'textarea':
          return (
            <textarea
              {...commonProps}
              rows={rows}
              ref={ref as React.Ref<HTMLTextAreaElement>}
            />
          )

        case 'select':
          return (
            <select
              {...commonProps}
              ref={ref as React.Ref<HTMLSelectElement>}
            >
              <option value="">{placeholder || `Select ${label.toLowerCase()}`}</option>
              {options.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          )

        case 'password':
          return (
            <div className="relative">
              <input
                {...commonProps}
                type={showPassword ? 'text' : 'password'}
                ref={ref as React.Ref<HTMLInputElement>}
                className={`${baseInputClasses} pr-10`}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                disabled={disabled || isLoading}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                {showPassword ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
          )

        default:
          return (
            <input
              {...commonProps}
              type={type}
              ref={ref as React.Ref<HTMLInputElement>}
            />
          )
      }
    }

    return (
      <div className={`space-y-1 ${className}`}>
        {/* Label */}
        <label 
          htmlFor={name}
          className={`block text-sm font-medium transition-colors duration-200 ${
            hasError 
              ? 'text-red-700' 
              : hasSuccess 
                ? 'text-green-700' 
                : 'text-gray-700'
          }`}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>

        {/* Input Container */}
        <div className="relative">
          {renderInput()}
          
          {/* Status Icons */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {isLoading && (
              <Loader2 className="w-4 h-4 text-purple-500 animate-spin" />
            )}
            {hasError && !isLoading && (
              <AlertCircle className="w-4 h-4 text-red-500" />
            )}
            {hasSuccess && !isLoading && (
              <CheckCircle className="w-4 h-4 text-green-500" />
            )}
          </div>
        </div>

        {/* Helper Text / Error / Success Messages */}
        <div className="min-h-[1.25rem]">
          {currentError && (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircle className="w-3 h-3 flex-shrink-0" />
              {currentError}
            </p>
          )}
          {success && !hasError && (
            <p className="text-sm text-green-600 flex items-center gap-1">
              <CheckCircle className="w-3 h-3 flex-shrink-0" />
              {success}
            </p>
          )}
          {helperText && !hasError && !success && (
            <p className="text-sm text-gray-500">{helperText}</p>
          )}
        </div>

        {/* Character Count */}
        {maxLength && type !== 'password' && (
          <div className="text-xs text-gray-400 text-right">
            {internalValue.length}/{maxLength}
          </div>
        )}
      </div>
    )
  }
)

FormField.displayName = 'FormField'

export default FormField

// Specialized form field components
export function EmailField(props: Omit<FormFieldProps, 'type'>) {
  return (
    <FormField
      {...props}
      type="email"
      autoComplete="email"
      validate={(value) => {
        if (!value) return props.required ? 'Email is required' : null
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(value) ? null : 'Please enter a valid email address'
      }}
    />
  )
}

export function PasswordField(props: Omit<FormFieldProps, 'type'>) {
  return (
    <FormField
      {...props}
      type="password"
      autoComplete="current-password"
      validate={(value) => {
        if (!value) return props.required ? 'Password is required' : null
        if (value.length < 8) return 'Password must be at least 8 characters long'
        if (!/(?=.*[a-z])/.test(value)) return 'Password must contain at least one lowercase letter'
        if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain at least one uppercase letter'
        if (!/(?=.*\d)/.test(value)) return 'Password must contain at least one number'
        return null
      }}
    />
  )
}

export function ConfirmPasswordField(props: Omit<FormFieldProps, 'type'> & { originalPassword: string }) {
  return (
    <FormField
      {...props}
      type="password"
      autoComplete="new-password"
      validate={(value) => {
        if (!value) return props.required ? 'Please confirm your password' : null
        return value === props.originalPassword ? null : 'Passwords do not match'
      }}
    />
  )
} 