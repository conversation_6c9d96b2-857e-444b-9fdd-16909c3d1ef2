import { Track as DatabaseTrack } from './database'

// Frontend Track interface that extends the database Track with additional fields
export interface Track extends Omit<DatabaseTrack, 'artist_name'> {
  artist: string // Maps to artist_name from database
  album?: string // Additional field for album info
  cover_image_url?: string // Additional field for cover image
}

// Utility function to convert database track to frontend track
export function mapDatabaseTrackToFrontend(dbTrack: DatabaseTrack): Track {
  return {
    ...dbTrack,
    artist: dbTrack.artist_name,
    album: undefined, // Will be populated from other sources if available
    cover_image_url: undefined // Will be populated from other sources if available
  }
}

// Audio-specific track interface for the audio context
export interface AudioTrack {
  id: string
  title: string
  artist: string
  album?: string
  duration: number | null
  file_url: string | null
  src: string // Alias for file_url for backward compatibility
  cover_image_url?: string
  artwork?: string // Alias for cover_image_url for backward compatibility
  genre?: string
  created_at: string
  is_public: boolean
  aiTool?: string
  description?: string
  // Additional properties for browse and discovery features
  play_count?: number
  like_count?: number
  trending_score?: number
  discovery_score?: number
  mood?: string
  tags?: string[]
  bpm?: number
  key?: string
  energy_level?: number
  danceability?: number
  valence?: number // Musical positivity/happiness
}

// Utility function to convert Track to AudioTrack
export function mapTrackToAudioTrack(track: Track): AudioTrack {
  return {
    id: track.id,
    title: track.title,
    artist: track.artist,
    album: track.album,
    duration: track.duration,
    file_url: track.file_url,
    src: track.file_url || '', // Set src as alias for file_url
    cover_image_url: track.cover_image_url,
    artwork: track.cover_image_url, // Set artwork as alias for cover_image_url
    genre: track.genre || undefined,
    created_at: track.created_at,
    is_public: track.is_public,
    aiTool: track.ai_tool,
    description: track.ai_prompt || undefined,
    // Default values for additional properties
    play_count: 0,
    like_count: 0,
    trending_score: 0,
    discovery_score: 0,
    mood: undefined,
    tags: [],
    bpm: undefined,
    key: undefined,
    energy_level: undefined,
    danceability: undefined,
    valence: undefined
  }
} 