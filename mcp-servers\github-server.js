#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// Create MCP server for Tunami GitHub operations
const server = new McpServer({
  name: "tunami-github",
  version: "1.0.0"
});

// GitHub API base URL
const GITHUB_API_BASE = "https://api.github.com";

// Get GitHub token from environment
const GITHUB_TOKEN = process.env.GITHUB_TOKEN || process.env.GITHUB_PERSONAL_ACCESS_TOKEN;

// Helper function to make GitHub API requests
async function githubRequest(endpoint, options = {}) {
  const url = `${GITHUB_API_BASE}${endpoint}`;
  const headers = {
    'Accept': 'application/vnd.github.v3+json',
    'User-Agent': 'Tunami-Music-Platform/1.0',
    ...options.headers
  };

  if (GITHUB_TOKEN) {
    headers['Authorization'] = `token ${GITHUB_TOKEN}`;
  }

  const response = await fetch(url, {
    ...options,
    headers
  });

  if (!response.ok) {
    throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

// Get repository info tool
server.tool(
  "get_repository",
  {
    owner: z.string().describe("Repository owner"),
    repo: z.string().describe("Repository name")
  },
  async ({ owner, repo }) => {
    try {
      const repoData = await githubRequest(`/repos/${owner}/${repo}`);
      
      return {
        content: [{ 
          type: "text", 
          text: `Repository: ${owner}/${repo}\n\n${JSON.stringify(repoData, null, 2)}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error fetching repository: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// List repository files tool
server.tool(
  "list_repository_files",
  {
    owner: z.string().describe("Repository owner"),
    repo: z.string().describe("Repository name"),
    path: z.string().default("").describe("Path within repository"),
    ref: z.string().optional().describe("Branch or commit SHA")
  },
  async ({ owner, repo, path = "", ref }) => {
    try {
      let endpoint = `/repos/${owner}/${repo}/contents/${path}`;
      if (ref) {
        endpoint += `?ref=${ref}`;
      }
      
      const contents = await githubRequest(endpoint);
      
      const fileList = Array.isArray(contents) 
        ? contents.map(item => `${item.type === 'dir' ? '[DIR]' : '[FILE]'} ${item.name}`).join('\n')
        : `[FILE] ${contents.name}`;
      
      return {
        content: [{ 
          type: "text", 
          text: `Files in ${owner}/${repo}${path ? `/${path}` : ''}:\n\n${fileList}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error listing repository files: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Get file content tool
server.tool(
  "get_file_content",
  {
    owner: z.string().describe("Repository owner"),
    repo: z.string().describe("Repository name"),
    path: z.string().describe("File path"),
    ref: z.string().optional().describe("Branch or commit SHA")
  },
  async ({ owner, repo, path, ref }) => {
    try {
      let endpoint = `/repos/${owner}/${repo}/contents/${path}`;
      if (ref) {
        endpoint += `?ref=${ref}`;
      }
      
      const fileData = await githubRequest(endpoint);
      
      if (fileData.type !== 'file') {
        return {
          content: [{ 
            type: "text", 
            text: `Error: ${path} is not a file` 
          }],
          isError: true
        };
      }
      
      const content = Buffer.from(fileData.content, 'base64').toString('utf-8');
      
      return {
        content: [{ 
          type: "text", 
          text: `File: ${owner}/${repo}/${path}\n\n${content}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error getting file content: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// List repository issues tool
server.tool(
  "list_issues",
  {
    owner: z.string().describe("Repository owner"),
    repo: z.string().describe("Repository name"),
    state: z.enum(["open", "closed", "all"]).default("open").describe("Issue state"),
    labels: z.string().optional().describe("Comma-separated list of labels")
  },
  async ({ owner, repo, state = "open", labels }) => {
    try {
      let endpoint = `/repos/${owner}/${repo}/issues?state=${state}`;
      if (labels) {
        endpoint += `&labels=${encodeURIComponent(labels)}`;
      }
      
      const issues = await githubRequest(endpoint);
      
      const issueList = issues.map(issue => 
        `#${issue.number}: ${issue.title} (${issue.state}) - ${issue.user.login}`
      ).join('\n');
      
      return {
        content: [{ 
          type: "text", 
          text: `Issues in ${owner}/${repo} (${state}):\n\n${issueList}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error listing issues: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Create issue tool
server.tool(
  "create_issue",
  {
    owner: z.string().describe("Repository owner"),
    repo: z.string().describe("Repository name"),
    title: z.string().describe("Issue title"),
    body: z.string().optional().describe("Issue body"),
    labels: z.array(z.string()).optional().describe("Issue labels"),
    assignees: z.array(z.string()).optional().describe("Issue assignees")
  },
  async ({ owner, repo, title, body, labels, assignees }) => {
    if (!GITHUB_TOKEN) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: GitHub token required to create issues. Set GITHUB_TOKEN environment variable.` 
        }],
        isError: true
      };
    }

    try {
      const issueData = {
        title,
        body,
        labels,
        assignees
      };
      
      const issue = await githubRequest(`/repos/${owner}/${repo}/issues`, {
        method: 'POST',
        body: JSON.stringify(issueData),
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      return {
        content: [{ 
          type: "text", 
          text: `Created issue #${issue.number}: ${issue.title}\nURL: ${issue.html_url}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error creating issue: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// List pull requests tool
server.tool(
  "list_pull_requests",
  {
    owner: z.string().describe("Repository owner"),
    repo: z.string().describe("Repository name"),
    state: z.enum(["open", "closed", "all"]).default("open").describe("PR state"),
    base: z.string().optional().describe("Base branch")
  },
  async ({ owner, repo, state = "open", base }) => {
    try {
      let endpoint = `/repos/${owner}/${repo}/pulls?state=${state}`;
      if (base) {
        endpoint += `&base=${base}`;
      }
      
      const prs = await githubRequest(endpoint);
      
      const prList = prs.map(pr => 
        `#${pr.number}: ${pr.title} (${pr.state}) - ${pr.user.login} -> ${pr.base.ref}`
      ).join('\n');
      
      return {
        content: [{ 
          type: "text", 
          text: `Pull Requests in ${owner}/${repo} (${state}):\n\n${prList}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error listing pull requests: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Get repository commits tool
server.tool(
  "get_commits",
  {
    owner: z.string().describe("Repository owner"),
    repo: z.string().describe("Repository name"),
    sha: z.string().optional().describe("Branch or commit SHA"),
    per_page: z.number().default(10).describe("Number of commits to fetch")
  },
  async ({ owner, repo, sha, per_page = 10 }) => {
    try {
      let endpoint = `/repos/${owner}/${repo}/commits?per_page=${per_page}`;
      if (sha) {
        endpoint += `&sha=${sha}`;
      }
      
      const commits = await githubRequest(endpoint);
      
      const commitList = commits.map(commit => 
        `${commit.sha.substring(0, 7)}: ${commit.commit.message.split('\n')[0]} - ${commit.commit.author.name}`
      ).join('\n');
      
      return {
        content: [{ 
          type: "text", 
          text: `Recent commits in ${owner}/${repo}:\n\n${commitList}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error getting commits: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Search repositories tool
server.tool(
  "search_repositories",
  {
    query: z.string().describe("Search query"),
    sort: z.enum(["stars", "forks", "updated"]).optional().describe("Sort order"),
    per_page: z.number().default(10).describe("Number of results")
  },
  async ({ query, sort, per_page = 10 }) => {
    try {
      let endpoint = `/search/repositories?q=${encodeURIComponent(query)}&per_page=${per_page}`;
      if (sort) {
        endpoint += `&sort=${sort}`;
      }
      
      const searchResults = await githubRequest(endpoint);
      
      const repoList = searchResults.items.map(repo => 
        `${repo.full_name}: ${repo.description || 'No description'} (⭐ ${repo.stargazers_count})`
      ).join('\n');
      
      return {
        content: [{ 
          type: "text", 
          text: `Search results for "${query}":\n\n${repoList}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error searching repositories: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Start the server
const transport = new StdioServerTransport();
await server.connect(transport);

console.error("Tunami GitHub MCP Server running on stdio"); 