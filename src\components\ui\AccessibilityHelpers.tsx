'use client'

import React, { useEffect, useRef } from 'react'

// Skip to main content link
export const SkipToMain: React.FC = () => (
  <a
    href="#main-content"
    className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-purple-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
  >
    Skip to main content
  </a>
)

// Live region for announcements
interface LiveRegionProps {
  children: React.ReactNode
  politeness?: 'polite' | 'assertive' | 'off'
  atomic?: boolean
  className?: string
}

export const LiveRegion: React.FC<LiveRegionProps> = ({
  children,
  politeness = 'polite',
  atomic = true,
  className = ''
}) => (
  <div
    aria-live={politeness}
    aria-atomic={atomic}
    className={`sr-only ${className}`}
  >
    {children}
  </div>
)

// Focus trap for modals and dropdowns
interface FocusTrapProps {
  children: React.ReactNode
  enabled?: boolean
  onEscape?: () => void
}

export const FocusTrap: React.FC<FocusTrapProps> = ({
  children,
  enabled = true,
  onEscape
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const firstFocusableRef = useRef<HTMLElement | null>(null)
  const lastFocusableRef = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!enabled || !containerRef.current) return

    const container = containerRef.current
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )

    if (focusableElements.length === 0) return

    firstFocusableRef.current = focusableElements[0] as HTMLElement
    lastFocusableRef.current = focusableElements[focusableElements.length - 1] as HTMLElement

    // Focus first element
    firstFocusableRef.current?.focus()

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && onEscape) {
        onEscape()
        return
      }

      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstFocusableRef.current) {
            e.preventDefault()
            lastFocusableRef.current?.focus()
          }
        } else {
          if (document.activeElement === lastFocusableRef.current) {
            e.preventDefault()
            firstFocusableRef.current?.focus()
          }
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [enabled, onEscape])

  return (
    <div ref={containerRef}>
      {children}
    </div>
  )
}

// Loading state announcer
interface LoadingAnnouncerProps {
  isLoading: boolean
  loadingMessage?: string
  completedMessage?: string
}

export const LoadingAnnouncer: React.FC<LoadingAnnouncerProps> = ({
  isLoading,
  loadingMessage = 'Loading',
  completedMessage = 'Loading complete'
}) => (
  <LiveRegion politeness="polite">
    {isLoading ? loadingMessage : completedMessage}
  </LiveRegion>
)

// Error announcer
interface ErrorAnnouncerProps {
  error: string | null
  errorMessage?: string
}

export const ErrorAnnouncer: React.FC<ErrorAnnouncerProps> = ({
  error,
  errorMessage
}) => (
  <LiveRegion politeness="assertive">
    {error && (errorMessage || `Error: ${error}`)}
  </LiveRegion>
)

// Success announcer
interface SuccessAnnouncerProps {
  success: boolean
  message: string
}

export const SuccessAnnouncer: React.FC<SuccessAnnouncerProps> = ({
  success,
  message
}) => (
  <LiveRegion politeness="polite">
    {success && message}
  </LiveRegion>
)

// Visually hidden component
interface VisuallyHiddenProps {
  children: React.ReactNode
  className?: string
}

export const VisuallyHidden: React.FC<VisuallyHiddenProps> = ({
  children,
  className = ''
}) => (
  <span className={`sr-only ${className}`}>
    {children}
  </span>
)

// Keyboard navigation helper
export const useKeyboardNavigation = (
  itemsCount: number,
  onSelect: (index: number) => void,
  enabled: boolean = true
) => {
  const [focusedIndex, setFocusedIndex] = React.useState(0)

  const handleKeyDown = React.useCallback((e: KeyboardEvent) => {
    if (!enabled) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setFocusedIndex(prev => (prev + 1) % itemsCount)
        break
      case 'ArrowUp':
        e.preventDefault()
        setFocusedIndex(prev => (prev - 1 + itemsCount) % itemsCount)
        break
      case 'Enter':
      case ' ':
        e.preventDefault()
        onSelect(focusedIndex)
        break
      case 'Home':
        e.preventDefault()
        setFocusedIndex(0)
        break
      case 'End':
        e.preventDefault()
        setFocusedIndex(itemsCount - 1)
        break
    }
  }, [enabled, itemsCount, onSelect, focusedIndex])

  React.useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [enabled, handleKeyDown])

  return { focusedIndex, setFocusedIndex }
}

// ARIA labels helper
export const getAriaLabel = (base: string, current?: number, total?: number) => {
  if (current !== undefined && total !== undefined) {
    return `${base}, ${current} of ${total}`
  }
  return base
}

// Status update announcer
interface StatusUpdateProps {
  status: string | null
  delay?: number
}

export const StatusUpdate: React.FC<StatusUpdateProps> = ({
  status,
  delay = 500
}) => {
  const [announceStatus, setAnnounceStatus] = React.useState<string | null>(null)

  React.useEffect(() => {
    if (status) {
      const timer = setTimeout(() => {
        setAnnounceStatus(status)
      }, delay)

      return () => clearTimeout(timer)
    } else {
      setAnnounceStatus(null)
    }
  }, [status, delay])

  return (
    <LiveRegion politeness="polite">
      {announceStatus}
    </LiveRegion>
  )
} 