'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import {
  Upload,
  Camera,
  Mic,
  File,
  Music,
  Image,
  Video,
  Check,
  X,
  ArrowLeft,
  ArrowRight,
  Play,
  Pause,
  RotateCcw,
  Share2,
  Download,
  AlertCircle,
  Loader2,
  Plus,
  ChevronDown,
  ChevronUp,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Sparkles,
  Info
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useMobileDetection } from '@/hooks/useMobileDetection'
import { useSwipeGestures } from '@/hooks/useSwipeGestures'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'
import { uploadService, UploadProgress, UploadResult } from '@/lib/upload-service'
import { UploadFile, UploadMetadata } from '@/types/upload'

interface MobileUploadFlowProps {
  isOpen: boolean
  onClose: () => void
  onComplete?: (results: UploadResult[]) => void
  className?: string
}

interface UploadStep {
  id: string
  title: string
  description: string
  completed: boolean
  active: boolean
}

const uploadSteps: UploadStep[] = [
  {
    id: 'select',
    title: 'Select Files',
    description: 'Choose audio files to upload',
    completed: false,
    active: true
  },
  {
    id: 'metadata',
    title: 'Add Details',
    description: 'Provide track information',
    completed: false,
    active: false
  },
  {
    id: 'preview',
    title: 'Preview',
    description: 'Review before upload',
    completed: false,
    active: false
  },
  {
    id: 'upload',
    title: 'Upload',
    description: 'Upload to Tunami',
    completed: false,
    active: false
  }
]

const aiTools = [
  { id: 'suno', name: 'Suno AI', icon: '🎵' },
  { id: 'udio', name: 'Udio', icon: '🎶' },
  { id: 'mubert', name: 'Mubert', icon: '🎼' },
  { id: 'aiva', name: 'AIVA', icon: '🎹' },
  { id: 'amper', name: 'Amper Music', icon: '🎸' },
  { id: 'custom', name: 'Custom AI', icon: '⚡' },
  { id: 'other', name: 'Other', icon: '🎧' }
]

const genres = [
  'Pop', 'Rock', 'Hip-Hop', 'R&B', 'Country', 'Jazz', 'Blues', 'Classical',
  'Electronic', 'House', 'Techno', 'Ambient', 'Trance', 'Dubstep',
  'Folk', 'Indie', 'Alternative', 'Punk', 'Metal', 'Reggae', 'Funk', 'Soul',
  'World', 'Latin', 'Afrobeat', 'K-Pop', 'J-Pop', 'Synthwave', 'Lo-fi'
]

export default function MobileUploadFlow({
  isOpen,
  onClose,
  onComplete,
  className = ''
}: MobileUploadFlowProps) {
  const router = useRouter()
  const { user } = useAuth()
  const mobileDetection = useMobileDetection()

  const [currentStep, setCurrentStep] = useState(0)
  const [steps, setSteps] = useState(uploadSteps)
  const [files, setFiles] = useState<UploadFile[]>([])
  const [metadata, setMetadata] = useState<UploadMetadata[]>([])
  const [uploadProgress, setUploadProgress] = useState<Record<string, UploadProgress>>({})
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([])
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [dragActive, setDragActive] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const cameraInputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Swipe to close
  const swipeHandlers = useSwipeGestures({
    onSwipeDown: () => {
      if (currentStep === 0 && files.length === 0) {
        onClose()
      }
    },
    threshold: 100,
    enabled: isOpen
  })

  useEffect(() => {
    if (!user && isOpen) {
      router.push('/auth/login')
    }
  }, [user, isOpen, router])

  const updateStepStatus = (stepIndex: number, completed: boolean = false, active: boolean = false) => {
    setSteps(prev => prev.map((step, index) => ({
      ...step,
      completed: index < stepIndex || completed,
      active: index === stepIndex || active
    })))
  }

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || [])
    processFiles(selectedFiles)
  }, [])

  const processFiles = (selectedFiles: File[]) => {
    const audioFiles = selectedFiles.filter(file => file.type.startsWith('audio/'))
    
    if (audioFiles.length === 0) {
      alert('Please select audio files only.')
      return
    }

    const uploadFiles: UploadFile[] = audioFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'pending',
      progress: 0,
      preview: file.type.startsWith('audio/') ? URL.createObjectURL(file) : undefined
    }))

    setFiles(prev => [...prev, ...uploadFiles])

    // Initialize metadata for new files
    const newMetadata: UploadMetadata[] = uploadFiles.map(file => ({
      title: file.name.replace(/\.[^/.]+$/, ''),
      artist: '',
      aiTool: '',
      genre: '',
      description: '',
      tags: [],
      isPublic: true
    }))

    setMetadata(prev => [...prev, ...newMetadata])
  }

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
    setMetadata(prev => {
      const fileIndex = files.findIndex(f => f.id === fileId)
      return prev.filter((_, index) => index !== fileIndex)
    })
  }

  const updateMetadata = (index: number, updates: Partial<UploadMetadata>) => {
    setMetadata(prev => prev.map((meta, i) => 
      i === index ? { ...meta, ...updates } : meta
    ))
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      const nextStepIndex = currentStep + 1
      setCurrentStep(nextStepIndex)
      updateStepStatus(nextStepIndex)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      const prevStepIndex = currentStep - 1
      setCurrentStep(prevStepIndex)
      updateStepStatus(prevStepIndex)
    }
  }

  const startUpload = async () => {
    if (files.length === 0) return

    setIsUploading(true)
    updateStepStatus(3, false, true)

    try {
      const results: UploadResult[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const meta = metadata[i]

        const progressCallback = (progress: UploadProgress) => {
          setUploadProgress(prev => ({
            ...prev,
            [file.id]: progress
          }))
        }

        try {
          const result = await uploadService.uploadTrack(file.file, meta, progressCallback)
          results.push(result)
          
          setFiles(prev => prev.map(f => 
            f.id === file.id ? { ...f, status: 'completed' } : f
          ))
        } catch (error) {
          console.error('Upload failed for file:', file.name, error)
          results.push({
            success: false,
            error: error instanceof Error ? error.message : 'Upload failed',
            fileName: file.name
          })
          
          setFiles(prev => prev.map(f => 
            f.id === file.id ? { ...f, status: 'error' } : f
          ))
        }
      }

      setUploadResults(results)
      updateStepStatus(3, true)
      onComplete?.(results)
    } catch (error) {
      console.error('Upload process failed:', error)
    } finally {
      setIsUploading(false)
    }
  }

  const handleCamera = () => {
    if (cameraInputRef.current) {
      cameraInputRef.current.click()
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0: // File selection
        return files.length > 0
      case 1: // Metadata
        return metadata.every(meta => meta.title.trim() && meta.artist.trim())
      case 2: // Preview
        return true
      default:
        return false
    }
  }

  if (!isOpen) return null

  // Don't render on desktop
  if (mobileDetection.isDesktop) {
    return null
  }

  return (
    <ComponentErrorBoundary>
      <div className="fixed inset-0 z-50 bg-gray-900 md:hidden" ref={containerRef}>
        {/* Header */}
        <div className="sticky top-0 bg-gray-900 border-b border-gray-800 safe-area-top">
          <div className="flex items-center justify-between p-4">
            <button
              onClick={currentStep === 0 ? onClose : prevStep}
              className="p-2 -ml-2 rounded-full hover:bg-gray-800 transition-colors"
            >
              {currentStep === 0 ? (
                <X className="w-5 h-5" />
              ) : (
                <ArrowLeft className="w-5 h-5" />
              )}
            </button>

            <h1 className="text-lg font-semibold text-white">
              {steps[currentStep]?.title || 'Upload'}
            </h1>

            <div className="w-10" />
          </div>

          {/* Progress Steps */}
          <div className="px-4 pb-4">
            <div className="flex items-center space-x-2">
              {steps.map((step, index) => (
                <div key={step.id} className="flex-1 flex items-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-colors ${
                      step.completed
                        ? 'bg-green-500 text-white'
                        : step.active
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-700 text-gray-400'
                    }`}
                  >
                    {step.completed ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`flex-1 h-0.5 mx-2 transition-colors ${
                        step.completed ? 'bg-green-500' : 'bg-gray-700'
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Content */}
        <div 
          className="flex-1 overflow-y-auto safe-area-bottom"
          {...swipeHandlers}
        >
          {currentStep === 0 && (
            <FileSelectionStep
              files={files}
              onFileSelect={handleFileSelect}
              onFileRemove={removeFile}
              onCamera={handleCamera}
              dragActive={dragActive}
              setDragActive={setDragActive}
            />
          )}

          {currentStep === 1 && (
            <MetadataStep
              files={files}
              metadata={metadata}
              onMetadataUpdate={updateMetadata}
              showAdvanced={showAdvanced}
              setShowAdvanced={setShowAdvanced}
            />
          )}

          {currentStep === 2 && (
            <PreviewStep
              files={files}
              metadata={metadata}
            />
          )}

          {currentStep === 3 && (
            <UploadStep
              files={files}
              uploadProgress={uploadProgress}
              uploadResults={uploadResults}
              isUploading={isUploading}
            />
          )}
        </div>

        {/* Navigation Footer */}
        {currentStep < 3 && (
          <div className="sticky bottom-0 bg-gray-900 border-t border-gray-800 p-4 safe-area-bottom">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-400">
                {currentStep === 0 && `${files.length} file${files.length !== 1 ? 's' : ''} selected`}
                {currentStep === 1 && 'Add track details'}
                {currentStep === 2 && 'Ready to upload'}
              </div>

              <button
                onClick={currentStep === 2 ? startUpload : nextStep}
                disabled={!canProceed() || isUploading}
                className="px-6 py-3 bg-purple-600 text-white rounded-full hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {currentStep === 2 ? 'Upload' : 'Next'}
              </button>
            </div>
          </div>
        )}

        {/* Hidden file inputs */}
        <input
          ref={fileInputRef}
          type="file"
          accept="audio/*"
          multiple
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <input
          ref={cameraInputRef}
          type="file"
          accept="audio/*"
          capture="microphone"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>
    </ComponentErrorBoundary>
  )
}

// File Selection Step Component
interface FileSelectionStepProps {
  files: UploadFile[]
  onFileSelect: (event: React.ChangeEvent<HTMLInputElement>) => void
  onFileRemove: (fileId: string) => void
  onCamera: () => void
  dragActive: boolean
  setDragActive: (active: boolean) => void
}

function FileSelectionStep({
  files,
  onFileSelect,
  onFileRemove,
  onCamera,
  dragActive,
  setDragActive
}: FileSelectionStepProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    
    const droppedFiles = Array.from(e.dataTransfer.files)
    // Process dropped files similar to file input
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="p-4 space-y-6">
      {files.length === 0 ? (
        <>
          {/* Upload area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive
                ? 'border-purple-500 bg-purple-500/10'
                : 'border-gray-600 hover:border-gray-500'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">
              Upload Your Music
            </h3>
            <p className="text-gray-400 mb-6">
              Drag & drop audio files or tap to browse
            </p>
            
            <button
              onClick={() => fileInputRef.current?.click()}
              className="px-6 py-3 bg-purple-600 text-white rounded-full hover:bg-purple-700 transition-colors font-medium"
            >
              Choose Files
            </button>
          </div>

          {/* Quick actions */}
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={onCamera}
              className="flex flex-col items-center p-6 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <Mic className="w-8 h-8 text-blue-400 mb-2" />
              <span className="text-white font-medium">Record</span>
              <span className="text-gray-400 text-sm">Use microphone</span>
            </button>

            <button
              onClick={() => fileInputRef.current?.click()}
              className="flex flex-col items-center p-6 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <File className="w-8 h-8 text-green-400 mb-2" />
              <span className="text-white font-medium">Browse</span>
              <span className="text-gray-400 text-sm">Select files</span>
            </button>
          </div>

          {/* Upload requirements */}
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Info className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium mb-1">Upload Requirements</h4>
                <ul className="text-gray-400 text-sm space-y-1">
                  <li>• Supported formats: MP3, WAV, FLAC</li>
                  <li>• Maximum file size: 100MB per file</li>
                  <li>• Up to 10 files at once</li>
                  <li>• Ensure you own the rights to the music</li>
                </ul>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          {/* Selected files */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">
                Selected Files ({files.length})
              </h3>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>Add More</span>
              </button>
            </div>

            {files.map((file) => (
              <div
                key={file.id}
                className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg"
              >
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Music className="w-6 h-6 text-white" />
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-medium truncate">
                    {file.name}
                  </h4>
                  <p className="text-gray-400 text-sm">
                    {formatFileSize(file.size)} • {file.type}
                  </p>
                </div>

                <button
                  onClick={() => onFileRemove(file.id)}
                  className="p-2 rounded-full hover:bg-gray-700 transition-colors"
                >
                  <X className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            ))}
          </div>
        </>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*"
        multiple
        onChange={onFileSelect}
        className="hidden"
      />
    </div>
  )
}

// Metadata Step Component
interface MetadataStepProps {
  files: UploadFile[]
  metadata: UploadMetadata[]
  onMetadataUpdate: (index: number, updates: Partial<UploadMetadata>) => void
  showAdvanced: boolean
  setShowAdvanced: (show: boolean) => void
}

function MetadataStep({
  files,
  metadata,
  onMetadataUpdate,
  showAdvanced,
  setShowAdvanced
}: MetadataStepProps) {
  const [currentFileIndex, setCurrentFileIndex] = useState(0)
  const currentMeta = metadata[currentFileIndex] || {}

  const updateCurrentMetadata = (updates: Partial<UploadMetadata>) => {
    onMetadataUpdate(currentFileIndex, updates)
  }

  const addTag = (tag: string) => {
    if (tag.trim() && !currentMeta.tags?.includes(tag.trim())) {
      updateCurrentMetadata({
        tags: [...(currentMeta.tags || []), tag.trim()]
      })
    }
  }

  const removeTag = (tagToRemove: string) => {
    updateCurrentMetadata({
      tags: currentMeta.tags?.filter(tag => tag !== tagToRemove) || []
    })
  }

  return (
    <div className="p-4 space-y-6">
      {/* File navigation */}
      {files.length > 1 && (
        <div className="flex items-center justify-between">
          <button
            onClick={() => setCurrentFileIndex(Math.max(0, currentFileIndex - 1))}
            disabled={currentFileIndex === 0}
            className="p-2 rounded-full hover:bg-gray-800 disabled:opacity-50 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          <div className="text-center">
            <h3 className="text-white font-medium">
              {files[currentFileIndex]?.name}
            </h3>
            <p className="text-gray-400 text-sm">
              {currentFileIndex + 1} of {files.length}
            </p>
          </div>

          <button
            onClick={() => setCurrentFileIndex(Math.min(files.length - 1, currentFileIndex + 1))}
            disabled={currentFileIndex === files.length - 1}
            className="p-2 rounded-full hover:bg-gray-800 disabled:opacity-50 transition-colors"
          >
            <ArrowRight className="w-5 h-5" />
          </button>
        </div>
      )}

      {/* Basic metadata */}
      <div className="space-y-4">
        <div>
          <label className="block text-white font-medium mb-2">
            Track Title *
          </label>
          <input
            type="text"
            value={currentMeta.title || ''}
            onChange={(e) => updateCurrentMetadata({ title: e.target.value })}
            placeholder="Enter track title"
            className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
            style={{ fontSize: '16px' }}
          />
        </div>

        <div>
          <label className="block text-white font-medium mb-2">
            Artist *
          </label>
          <input
            type="text"
            value={currentMeta.artist || ''}
            onChange={(e) => updateCurrentMetadata({ artist: e.target.value })}
            placeholder="Enter artist name"
            className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
            style={{ fontSize: '16px' }}
          />
        </div>

        <div>
          <label className="block text-white font-medium mb-2">
            AI Tool Used
          </label>
          <select
            value={currentMeta.aiTool || ''}
            onChange={(e) => updateCurrentMetadata({ aiTool: e.target.value })}
            className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          >
            <option value="">Select AI tool (optional)</option>
            {aiTools.map((tool) => (
              <option key={tool.id} value={tool.id}>
                {tool.icon} {tool.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced metadata toggle */}
      <button
        onClick={() => setShowAdvanced(!showAdvanced)}
        className="flex items-center space-x-2 text-purple-400 hover:text-purple-300 transition-colors"
      >
        <span>Advanced Options</span>
        {showAdvanced ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </button>

      {/* Advanced metadata */}
      {showAdvanced && (
        <div className="space-y-4 pt-4 border-t border-gray-800">
          <div>
            <label className="block text-white font-medium mb-2">
              Genre
            </label>
            <select
              value={currentMeta.genre || ''}
              onChange={(e) => updateCurrentMetadata({ genre: e.target.value })}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
            >
              <option value="">Select genre (optional)</option>
              {genres.map((genre) => (
                <option key={genre} value={genre.toLowerCase()}>
                  {genre}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-white font-medium mb-2">
              Description
            </label>
            <textarea
              value={currentMeta.description || ''}
              onChange={(e) => updateCurrentMetadata({ description: e.target.value })}
              placeholder="Describe your track (optional)"
              rows={3}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 resize-none"
              style={{ fontSize: '16px' }}
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">
              Tags
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {currentMeta.tags?.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center space-x-1 px-3 py-1 bg-purple-600/20 border border-purple-500/30 rounded-full text-sm text-purple-300"
                >
                  <span>{tag}</span>
                  <button
                    onClick={() => removeTag(tag)}
                    className="p-0.5 rounded-full hover:bg-purple-500/20 transition-colors"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
            <input
              type="text"
              placeholder="Add tags (press Enter)"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  addTag(e.currentTarget.value)
                  e.currentTarget.value = ''
                }
              }}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
              style={{ fontSize: '16px' }}
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="text-white font-medium">
              Make Public
            </label>
            <button
              onClick={() => updateCurrentMetadata({ isPublic: !currentMeta.isPublic })}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                currentMeta.isPublic ? 'bg-purple-600' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  currentMeta.isPublic ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

// Preview Step Component
interface PreviewStepProps {
  files: UploadFile[]
  metadata: UploadMetadata[]
}

function PreviewStep({ files, metadata }: PreviewStepProps) {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null)

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="p-4 space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-white mb-2">
          Ready to Upload
        </h3>
        <p className="text-gray-400">
          Review your {files.length} track{files.length !== 1 ? 's' : ''} before uploading
        </p>
      </div>

      {files.map((file, index) => {
        const meta = metadata[index]
        const isExpanded = expandedIndex === index

        return (
          <div
            key={file.id}
            className="bg-gray-800 rounded-lg overflow-hidden"
          >
            <button
              onClick={() => setExpandedIndex(isExpanded ? null : index)}
              className="w-full p-4 text-left hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Music className="w-6 h-6 text-white" />
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-medium truncate">
                    {meta?.title || file.name}
                  </h4>
                  <p className="text-gray-400 text-sm">
                    {meta?.artist} • {formatFileSize(file.size)}
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  {meta?.aiTool && (
                    <Sparkles className="w-4 h-4 text-purple-400" />
                  )}
                  {meta?.isPublic ? (
                    <Unlock className="w-4 h-4 text-green-400" />
                  ) : (
                    <Lock className="w-4 h-4 text-gray-400" />
                  )}
                  {isExpanded ? (
                    <ChevronUp className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  )}
                </div>
              </div>
            </button>

            {isExpanded && (
              <div className="px-4 pb-4 border-t border-gray-700">
                <div className="space-y-3 pt-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Artist:</span>
                      <p className="text-white">{meta?.artist || 'Not specified'}</p>
                    </div>
                    <div>
                      <span className="text-gray-400">Genre:</span>
                      <p className="text-white">{meta?.genre || 'Not specified'}</p>
                    </div>
                  </div>

                  {meta?.description && (
                    <div>
                      <span className="text-gray-400 text-sm">Description:</span>
                      <p className="text-white">{meta.description}</p>
                    </div>
                  )}

                  {meta?.tags && meta.tags.length > 0 && (
                    <div>
                      <span className="text-gray-400 text-sm">Tags:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {meta.tags.map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 bg-purple-600/20 border border-purple-500/30 rounded text-xs text-purple-300"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

// Upload Step Component
interface UploadStepProps {
  files: UploadFile[]
  uploadProgress: Record<string, UploadProgress>
  uploadResults: UploadResult[]
  isUploading: boolean
}

function UploadStep({
  files,
  uploadProgress,
  uploadResults,
  isUploading
}: UploadStepProps) {
  const completedUploads = uploadResults.length
  const totalFiles = files.length
  const successfulUploads = uploadResults.filter(r => r.success).length

  if (completedUploads === totalFiles && !isUploading) {
    // Upload complete
    return (
      <div className="p-4 text-center">
        <div className="mb-6">
          {successfulUploads === totalFiles ? (
            <Check className="w-16 h-16 text-green-500 mx-auto mb-4" />
          ) : (
            <AlertCircle className="w-16 h-16 text-orange-500 mx-auto mb-4" />
          )}
          
          <h3 className="text-lg font-semibold text-white mb-2">
            Upload Complete!
          </h3>
          
          <p className="text-gray-400">
            {successfulUploads} of {totalFiles} track{totalFiles !== 1 ? 's' : ''} uploaded successfully
          </p>
        </div>

        <div className="space-y-3">
          {uploadResults.map((result, index) => (
            <div
              key={index}
              className={`flex items-center space-x-3 p-3 rounded-lg ${
                result.success ? 'bg-green-500/10 border border-green-500/20' : 'bg-red-500/10 border border-red-500/20'
              }`}
            >
              {result.success ? (
                <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
              ) : (
                <X className="w-5 h-5 text-red-500 flex-shrink-0" />
              )}
              
              <div className="flex-1 min-w-0">
                <p className="text-white font-medium truncate">
                  {result.fileName || files[index]?.name}
                </p>
                {result.error && (
                  <p className="text-red-400 text-sm">{result.error}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Upload in progress
  return (
    <div className="p-4">
      <div className="text-center mb-6">
        <Loader2 className="w-12 h-12 text-purple-500 animate-spin mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-white mb-2">
          Uploading Files...
        </h3>
        <p className="text-gray-400">
          {completedUploads} of {totalFiles} completed
        </p>
      </div>

      <div className="space-y-4">
        {files.map((file, index) => {
          const progress = uploadProgress[file.id]
          const result = uploadResults[index]
          const isComplete = !!result

          return (
            <div key={file.id} className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Music className="w-5 h-5 text-white" />
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-medium truncate">
                    {file.name}
                  </h4>
                  <p className="text-gray-400 text-sm">
                    {isComplete ? (
                      result?.success ? 'Upload complete' : 'Upload failed'
                    ) : progress ? (
                      `${progress.stage}: ${Math.round(progress.percentage)}%`
                    ) : (
                      'Waiting...'
                    )}
                  </p>
                </div>

                <div className="flex-shrink-0">
                  {isComplete ? (
                    result?.success ? (
                      <Check className="w-5 h-5 text-green-500" />
                    ) : (
                      <X className="w-5 h-5 text-red-500" />
                    )
                  ) : (
                    <Loader2 className="w-5 h-5 text-purple-500 animate-spin" />
                  )}
                </div>
              </div>

              {progress && !isComplete && (
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress.percentage}%` }}
                  />
                </div>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Hook for using mobile upload
export function useMobileUpload() {
  const [isOpen, setIsOpen] = useState(false)
  
  const openUpload = () => setIsOpen(true)
  const closeUpload = () => setIsOpen(false)
  const toggleUpload = () => setIsOpen(prev => !prev)
  
  return {
    isOpen,
    openUpload,
    closeUpload,
    toggleUpload
  }
} 