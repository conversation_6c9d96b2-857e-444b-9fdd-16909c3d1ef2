#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// Create MCP server for Tunami fetch operations
const server = new McpServer({
  name: "tunami-fetch",
  version: "1.0.0"
});

// Allowed domains for Tunami music platform
const ALLOWED_DOMAINS = [
  'musicbrainz.org',
  'last.fm',
  'spotify.com',
  'genius.com',
  'discogs.com',
  'allmusic.com',
  'bandcamp.com',
  'soundcloud.com',
  'youtube.com',
  'api.lyrics.ovh',
  'lyricsapi.vercel.app',
  'api.musixmatch.com'
];

// Helper function to check if URL is allowed
function isUrlAllowed(url) {
  try {
    const urlObj = new URL(url);
    return ALLOWED_DOMAINS.some(domain => 
      urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
    );
  } catch {
    return false;
  }
}

// Fetch URL tool
server.tool(
  "fetch_url",
  {
    url: z.string().describe("URL to fetch"),
    method: z.enum(["GET", "POST", "PUT", "DELETE"]).default("GET").describe("HTTP method"),
    headers: z.record(z.string()).optional().describe("HTTP headers"),
    body: z.string().optional().describe("Request body for POST/PUT requests")
  },
  async ({ url, method = "GET", headers = {}, body }) => {
    if (!isUrlAllowed(url)) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: URL not allowed. Only music-related domains are permitted.` 
        }],
        isError: true
      };
    }

    try {
      const fetchOptions = {
        method,
        headers: {
          'User-Agent': 'Tunami-Music-Platform/1.0',
          ...headers
        }
      };

      if (body && (method === "POST" || method === "PUT")) {
        fetchOptions.body = body;
      }

      const response = await fetch(url, fetchOptions);
      const contentType = response.headers.get('content-type') || '';
      
      let content;
      if (contentType.includes('application/json')) {
        content = await response.json();
        content = JSON.stringify(content, null, 2);
      } else {
        content = await response.text();
      }

      return {
        content: [{ 
          type: "text", 
          text: `Response from ${url}:\nStatus: ${response.status} ${response.statusText}\nContent-Type: ${contentType}\n\n${content}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error fetching URL: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Fetch music metadata tool
server.tool(
  "fetch_music_metadata",
  {
    artist: z.string().describe("Artist name"),
    track: z.string().optional().describe("Track name (optional)"),
    album: z.string().optional().describe("Album name (optional)")
  },
  async ({ artist, track, album }) => {
    try {
      const results = [];
      
      // Try MusicBrainz API
      try {
        let mbQuery = `artist:"${artist}"`;
        if (track) mbQuery += ` AND recording:"${track}"`;
        if (album) mbQuery += ` AND release:"${album}"`;
        
        const mbUrl = `https://musicbrainz.org/ws/2/recording?query=${encodeURIComponent(mbQuery)}&fmt=json&limit=5`;
        const mbResponse = await fetch(mbUrl, {
          headers: { 'User-Agent': 'Tunami-Music-Platform/1.0' }
        });
        
        if (mbResponse.ok) {
          const mbData = await mbResponse.json();
          results.push({
            source: 'MusicBrainz',
            data: mbData
          });
        }
      } catch (error) {
        console.error('MusicBrainz error:', error.message);
      }

      // Try Last.fm API (would need API key in real implementation)
      try {
        const lastfmUrl = `https://ws.audioscrobbler.com/2.0/?method=track.search&track=${encodeURIComponent(track || '')}&artist=${encodeURIComponent(artist)}&api_key=demo&format=json&limit=5`;
        const lastfmResponse = await fetch(lastfmUrl);
        
        if (lastfmResponse.ok) {
          const lastfmData = await lastfmResponse.json();
          results.push({
            source: 'Last.fm',
            data: lastfmData
          });
        }
      } catch (error) {
        console.error('Last.fm error:', error.message);
      }

      return {
        content: [{ 
          type: "text", 
          text: `Music metadata for Artist: ${artist}${track ? `, Track: ${track}` : ''}${album ? `, Album: ${album}` : ''}:\n\n${JSON.stringify(results, null, 2)}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error fetching music metadata: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Fetch lyrics tool
server.tool(
  "fetch_lyrics",
  {
    artist: z.string().describe("Artist name"),
    track: z.string().describe("Track name")
  },
  async ({ artist, track }) => {
    try {
      const results = [];
      
      // Try lyrics.ovh API
      try {
        const lyricsUrl = `https://api.lyrics.ovh/v1/${encodeURIComponent(artist)}/${encodeURIComponent(track)}`;
        const lyricsResponse = await fetch(lyricsUrl);
        
        if (lyricsResponse.ok) {
          const lyricsData = await lyricsResponse.json();
          results.push({
            source: 'lyrics.ovh',
            lyrics: lyricsData.lyrics || 'No lyrics found'
          });
        }
      } catch (error) {
        console.error('Lyrics.ovh error:', error.message);
      }

      if (results.length === 0) {
        return {
          content: [{ 
            type: "text", 
            text: `No lyrics found for "${track}" by ${artist}` 
          }]
        };
      }

      return {
        content: [{ 
          type: "text", 
          text: `Lyrics for "${track}" by ${artist}:\n\n${results[0].lyrics}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error fetching lyrics: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Fetch album art tool
server.tool(
  "fetch_album_art",
  {
    artist: z.string().describe("Artist name"),
    album: z.string().describe("Album name")
  },
  async ({ artist, album }) => {
    try {
      // Try MusicBrainz for album art
      const mbQuery = `artist:"${artist}" AND release:"${album}"`;
      const mbUrl = `https://musicbrainz.org/ws/2/release?query=${encodeURIComponent(mbQuery)}&fmt=json&limit=1`;
      
      const mbResponse = await fetch(mbUrl, {
        headers: { 'User-Agent': 'Tunami-Music-Platform/1.0' }
      });
      
      if (mbResponse.ok) {
        const mbData = await mbResponse.json();
        const releases = mbData.releases || [];
        
        if (releases.length > 0) {
          const releaseId = releases[0].id;
          const artUrl = `https://coverartarchive.org/release/${releaseId}/front`;
          
          return {
            content: [{ 
              type: "text", 
              text: `Album art URL for "${album}" by ${artist}:\n${artUrl}\n\nRelease info:\n${JSON.stringify(releases[0], null, 2)}` 
            }]
          };
        }
      }

      return {
        content: [{ 
          type: "text", 
          text: `No album art found for "${album}" by ${artist}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error fetching album art: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Check URL status tool
server.tool(
  "check_url_status",
  {
    url: z.string().describe("URL to check status")
  },
  async ({ url }) => {
    if (!isUrlAllowed(url)) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: URL not allowed. Only music-related domains are permitted.` 
        }],
        isError: true
      };
    }

    try {
      const response = await fetch(url, { 
        method: 'HEAD',
        headers: { 'User-Agent': 'Tunami-Music-Platform/1.0' }
      });
      
      const headers = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });

      return {
        content: [{ 
          type: "text", 
          text: `URL Status for ${url}:\nStatus: ${response.status} ${response.statusText}\nHeaders:\n${JSON.stringify(headers, null, 2)}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error checking URL status: ${error.message}` 
        }],
        isError: true
      };
    }
  }
);

// Start the server
const transport = new StdioServerTransport();
await server.connect(transport);

console.error("Tunami Fetch MCP Server running on stdio"); 