// Dashboard Stats Overview Component
'use client'

import React from 'react'
import { 
  BarChart3, 
  Music, 
  PlayCircle, 
  Heart, 
  TrendingUp,
  RefreshCw,
  Calendar,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { DashboardStats, DashboardTimeFilter } from '@/types/dashboard'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface DashboardStatsOverviewProps {
  stats: DashboardStats | null
  timeFilter: string
  timeFilters: DashboardTimeFilter[]
  onTimeFilterChange: (filter: DashboardTimeFilter['value']) => void
  loading: boolean
  error: string | null
  onRefresh?: () => void
}

interface StatCardProps {
  icon: React.ComponentType<{ className?: string }>
  title: string
  value: string | number
  change?: {
    value: number
    trend: 'up' | 'down' | 'neutral'
    label: string
  }
  loading?: boolean
  className?: string
}

const StatCard: React.FC<StatCardProps> = ({ 
  icon: Icon, 
  title, 
  value, 
  change,
  loading = false,
  className = '' 
}) => {
  const getTrendColor = (trend: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up': return 'text-green-400'
      case 'down': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up': return ArrowUp
      case 'down': return ArrowDown
      default: return null
    }
  }

  const TrendIcon = change ? getTrendIcon(change.trend) : null

  return (
    <div className={`bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center">
            <Icon className="w-5 h-5 text-purple-400" />
          </div>
          <h3 className="text-sm font-medium text-gray-300">{title}</h3>
        </div>
        {loading && <LoadingSpinner size="sm" />}
      </div>
      
      <div className="space-y-2">
        <div className="text-2xl font-bold text-white">
          {loading ? (
            <div className="h-8 w-20 bg-gray-700 rounded animate-pulse" />
          ) : (
            value
          )}
        </div>
        
        {change && !loading && (
          <div className="flex items-center gap-1 text-xs">
            {TrendIcon && <TrendIcon className={`w-3 h-3 ${getTrendColor(change.trend)}`} />}
            <span className={getTrendColor(change.trend)}>
              {change.value > 0 ? '+' : ''}{change.value}
            </span>
            <span className="text-gray-500">{change.label}</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default function DashboardStatsOverview({
  stats,
  timeFilter,
  timeFilters,
  onTimeFilterChange,
  loading,
  error,
  onRefresh
}: DashboardStatsOverviewProps) {
  if (error) {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white flex items-center gap-2">
            <BarChart3 className="w-6 h-6 text-purple-400" />
            Analytics Overview
          </h2>
        </div>
        
        <ErrorMessage
          message={error}
          onRetry={onRefresh}
          retryLabel="Reload Stats"
          className="max-w-2xl"
        />
      </div>
    )
  }

  return (
    <div className="mb-8">
      {/* Header with Time Filter */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
        <h2 className="text-xl font-bold text-white flex items-center gap-2">
          <BarChart3 className="w-6 h-6 text-purple-400" />
          Analytics Overview
        </h2>
        
        <div className="flex items-center gap-3">
          {/* Time Filter Dropdown */}
          <div className="relative">
            <select
              value={timeFilter}
              onChange={(e) => onTimeFilterChange(e.target.value as DashboardTimeFilter['value'])}
              className="appearance-none bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 pr-10 text-white text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              disabled={loading}
              aria-label="Select time period"
            >
              {timeFilters.map((filter) => (
                <option key={filter.value} value={filter.value}>
                  {filter.label}
                </option>
              ))}
            </select>
            <Calendar className="w-4 h-4 text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none" />
          </div>
          
          {/* Refresh Button */}
          {onRefresh && (
            <button
              onClick={onRefresh}
              disabled={loading}
              className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Refresh statistics"
              title="Refresh statistics"
            >
              <RefreshCw className={`w-4 h-4 text-gray-300 ${loading ? 'animate-spin' : ''}`} />
            </button>
          )}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={Music}
          title="Total Tracks"
          value={loading ? '' : (stats?.totalTracks?.toLocaleString() || '0')}
          change={stats?.totalTracksChange ? {
            value: stats.totalTracksChange,
            trend: stats.totalTracksChange > 0 ? 'up' : stats.totalTracksChange < 0 ? 'down' : 'neutral',
            label: 'vs last period'
          } : undefined}
          loading={loading}
        />
        
        <StatCard
          icon={PlayCircle}
          title="Total Plays"
          value={loading ? '' : (stats?.totalPlays?.toLocaleString() || '0')}
          change={stats?.totalPlaysChange ? {
            value: stats.totalPlaysChange,
            trend: stats.totalPlaysChange > 0 ? 'up' : stats.totalPlaysChange < 0 ? 'down' : 'neutral',
            label: 'vs last period'
          } : undefined}
          loading={loading}
        />
        
        <StatCard
          icon={Heart}
          title="Total Likes"
          value={loading ? '' : (stats?.totalLikes?.toLocaleString() || '0')}
          change={stats?.totalLikesChange ? {
            value: stats.totalLikesChange,
            trend: stats.totalLikesChange > 0 ? 'up' : stats.totalLikesChange < 0 ? 'down' : 'neutral',
            label: 'vs last period'
          } : undefined}
          loading={loading}
        />
        
        <StatCard
          icon={TrendingUp}
          title="Listening Time"
          value={loading ? '' : (stats?.totalListeningTime || '0h')}
          change={stats?.listeningTimeChange ? {
            value: stats.listeningTimeChange,
            trend: stats.listeningTimeChange > 0 ? 'up' : stats.listeningTimeChange < 0 ? 'down' : 'neutral',
            label: 'vs last period'
          } : undefined}
          loading={loading}
        />
      </div>

      {/* Additional Insights */}
      {stats && !loading && (
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {stats.topGenre && (
            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
              <div className="text-sm text-gray-400 mb-1">Top Genre</div>
              <div className="text-lg font-semibold text-white">{stats.topGenre}</div>
            </div>
          )}
          
          {stats.avgSessionLength && (
            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
              <div className="text-sm text-gray-400 mb-1">Avg Session</div>
              <div className="text-lg font-semibold text-white">{stats.avgSessionLength}</div>
            </div>
          )}
          
          {stats.streakDays !== undefined && (
            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
              <div className="text-sm text-gray-400 mb-1">Listening Streak</div>
              <div className="text-lg font-semibold text-white">
                {stats.streakDays} day{stats.streakDays !== 1 ? 's' : ''}
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Empty State */}
      {!loading && !error && !stats && (
        <div className="text-center py-12">
          <BarChart3 className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No Analytics Data</h3>
          <p className="text-gray-400 text-sm mb-4">
            Start listening to music to see your analytics dashboard.
          </p>
          {onRefresh && (
            <button
              onClick={onRefresh}
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
            >
              Refresh Data
            </button>
          )}
        </div>
      )}
    </div>
  )
} 