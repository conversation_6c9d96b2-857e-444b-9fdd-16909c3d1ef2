-- Migration 001: Initial Schema for AI Music Platform
-- Date: 2025-01-24
-- Description: Create core tables, types, and storage buckets

-- =============================================
-- 1. ENABLE EXTENSIONS
-- =============================================

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- 2. CUSTOM TYPES
-- =============================================

CREATE TYPE ai_tool_type AS ENUM (
  'suno',
  'udio',
  'mubert',
  'soundraw',
  'aiva',
  'amper',
  'jukedeck',
  'endlesss',
  'custom',
  'other'
);

CREATE TYPE genre_type AS ENUM (
  'pop',
  'rock',
  'hip_hop',
  'electronic',
  'jazz',
  'classical',
  'country',
  'folk',
  'r_and_b',
  'reggae',
  'blues',
  'metal',
  'punk',
  'indie',
  'ambient',
  'experimental',
  'world',
  'other'
);

CREATE TYPE mood_type AS ENUM (
  'happy',
  'sad',
  'energetic',
  'calm',
  'aggressive',
  'romantic',
  'mysterious',
  'epic',
  'chill',
  'uplifting',
  'dark',
  'nostalgic',
  'dreamy',
  'intense',
  'playful',
  'serious'
);

CREATE TYPE agreement_version_type AS ENUM (
  'v1.0',
  'v1.1',
  'v2.0',
  'v2.1'
);

-- =============================================
-- 3. CORE TABLES
-- =============================================

-- Profiles table (extends auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  username TEXT UNIQUE,
  avatar_url TEXT,
  bio TEXT,
  is_premium BOOLEAN DEFAULT FALSE,
  subscription_tier TEXT DEFAULT 'free',
  ai_generation_credits INTEGER DEFAULT 10,
  total_tracks_generated INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tracks table (AI-generated music)
CREATE TABLE tracks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  artist_name TEXT NOT NULL,
  file_url TEXT,
  file_path TEXT,
  file_size BIGINT,
  ai_tool ai_tool_type NOT NULL,
  duration INTEGER,
  genre genre_type,
  mood mood_type,
  tempo INTEGER,
  key_signature TEXT,
  ai_prompt TEXT,
  ai_parameters JSONB,
  is_public BOOLEAN DEFAULT TRUE,
  is_featured BOOLEAN DEFAULT FALSE,
  play_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  upload_status TEXT DEFAULT 'pending',
  uploaded_by UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Playlists table
CREATE TABLE playlists (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  cover_image_url TEXT,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  is_public BOOLEAN DEFAULT FALSE,
  is_collaborative BOOLEAN DEFAULT FALSE,
  track_count INTEGER DEFAULT 0,
  total_duration INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Playlist tracks junction table
CREATE TABLE playlist_tracks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  playlist_id UUID REFERENCES playlists(id) ON DELETE CASCADE NOT NULL,
  track_id UUID REFERENCES tracks(id) ON DELETE CASCADE NOT NULL,
  position INTEGER NOT NULL,
  added_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  added_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(playlist_id, position),
  UNIQUE(playlist_id, track_id)
);

-- User agreements table
CREATE TABLE user_agreements (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  agreement_type TEXT NOT NULL,
  agreement_version agreement_version_type NOT NULL,
  accepted_at TIMESTAMPTZ DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT,
  
  UNIQUE(user_id, agreement_type, agreement_version)
);

-- =============================================
-- 4. STORAGE BUCKETS
-- =============================================

INSERT INTO storage.buckets (id, name, public) VALUES 
  ('audio-tracks', 'audio-tracks', true),
  ('playlist-covers', 'playlist-covers', true),
  ('profile-avatars', 'profile-avatars', true); 