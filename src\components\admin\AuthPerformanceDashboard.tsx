'use client'

import React, { useState, useEffect } from 'react'
import { authPerformance } from '@/lib/auth-performance'
import { Activity, Clock, Database, TrendingUp, Users, Zap } from 'lucide-react'

interface PerformanceMetrics {
  loginStartTime: number
  loginEndTime: number
  profileFetchTime: number
  totalAuthTime: number
  cacheHits: number
  cacheMisses: number
}

export const AuthPerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only show in development mode
    if (process.env.NODE_ENV === 'development') {
      setIsVisible(true)
      
      // Update metrics every 5 seconds
      const interval = setInterval(() => {
        const currentMetrics = authPerformance.getMetrics()
        setMetrics(currentMetrics)
      }, 5000)

      // Initial load
      setMetrics(authPerformance.getMetrics())

      return () => clearInterval(interval)
    }
  }, [])

  if (!isVisible || !metrics) {
    return null
  }

  const cacheHitRate = metrics.cacheHits + metrics.cacheMisses > 0 
    ? ((metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100).toFixed(1)
    : '0'

  const getPerformanceColor = (time: number) => {
    if (time < 100) return 'text-green-400'
    if (time < 500) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getCacheColor = (rate: string) => {
    const numRate = parseFloat(rate)
    if (numRate >= 80) return 'text-green-400'
    if (numRate >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-dark-800/95 backdrop-blur-sm border border-gray-700 rounded-lg p-4 shadow-xl max-w-sm">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-300 flex items-center">
            <Activity className="w-4 h-4 mr-2 text-primary-400" />
            Auth Performance
          </h3>
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        </div>

        <div className="space-y-3">
          {/* Total Auth Time */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="w-3 h-3 mr-2 text-gray-400" />
              <span className="text-xs text-gray-400">Total Auth</span>
            </div>
            <span className={`text-xs font-mono ${getPerformanceColor(metrics.totalAuthTime)}`}>
              {metrics.totalAuthTime > 0 ? `${metrics.totalAuthTime.toFixed(0)}ms` : '-'}
            </span>
          </div>

          {/* Profile Fetch Time */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Users className="w-3 h-3 mr-2 text-gray-400" />
              <span className="text-xs text-gray-400">Profile Fetch</span>
            </div>
            <span className={`text-xs font-mono ${getPerformanceColor(metrics.profileFetchTime)}`}>
              {metrics.profileFetchTime > 0 ? `${metrics.profileFetchTime.toFixed(0)}ms` : '-'}
            </span>
          </div>

          {/* Cache Hit Rate */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Database className="w-3 h-3 mr-2 text-gray-400" />
              <span className="text-xs text-gray-400">Cache Hit Rate</span>
            </div>
            <span className={`text-xs font-mono ${getCacheColor(cacheHitRate)}`}>
              {cacheHitRate}%
            </span>
          </div>

          {/* Cache Stats */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Zap className="w-3 h-3 mr-2 text-gray-400" />
              <span className="text-xs text-gray-400">Cache Stats</span>
            </div>
            <span className="text-xs font-mono text-gray-300">
              {metrics.cacheHits}H / {metrics.cacheMisses}M
            </span>
          </div>

          {/* Performance Indicator */}
          <div className="pt-2 border-t border-gray-700">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-400">Performance</span>
              <div className="flex items-center">
                <TrendingUp className="w-3 h-3 mr-1 text-green-400" />
                <span className="text-xs text-green-400">Optimized</span>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Tips */}
        {metrics.totalAuthTime > 1000 && (
          <div className="mt-3 p-2 bg-yellow-500/10 border border-yellow-500/20 rounded text-xs text-yellow-400">
            ⚠️ Slow auth detected. Check network connection.
          </div>
        )}

        {parseFloat(cacheHitRate) < 50 && metrics.cacheHits + metrics.cacheMisses > 10 && (
          <div className="mt-3 p-2 bg-red-500/10 border border-red-500/20 rounded text-xs text-red-400">
            🔥 Low cache hit rate. Consider increasing cache TTL.
          </div>
        )}
      </div>
    </div>
  )
}

// Hook for accessing auth performance metrics
export const useAuthPerformance = () => {
  const [metrics, setMetrics] = useState(authPerformance.getMetrics())

  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(authPerformance.getMetrics())
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  return {
    metrics,
    logMetrics: () => authPerformance.logPerformanceMetrics(),
    clearCache: () => authPerformance.clearCache(),
    isOptimized: metrics.totalAuthTime < 500 && 
                 (metrics.cacheHits + metrics.cacheMisses === 0 || 
                  (metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) > 0.7)
  }
} 