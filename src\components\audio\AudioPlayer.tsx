'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { Play, Pause, Volume2, VolumeX, SkipBack, SkipForward, Loader2, Music } from 'lucide-react'
import { AudioTrack, AudioPlayerProps } from '@/types/audio'

export default function AudioPlayer({
  track,
  autoPlay = false,
  showControls = true,
  showTrackInfo = true,
  showVolumeControl = true,
  showSeekBar = true,
  showSkipButtons = false,
  enableKeyboardShortcuts = true,
  hasNext = false,
  hasPrevious = false,
  className = '',
  onPlay,
  onPause,
  onEnded,
  onNext,
  onPrevious,
  onTimeUpdate,
  onLoadedMetadata,
  onSeek,
  onVolumeChange
}: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null)
  const progressBarRef = useRef<HTMLDivElement>(null)
  const volumeBarRef = useRef<HTMLDivElement>(null)
  
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [progress, setProgress] = useState(0)

  // Handle play functionality
  const handlePlay = useCallback(async () => {
    if (!audioRef.current || !track) return
    
    setIsLoading(true)
    try {
      await audioRef.current.play()
      setIsPlaying(true)
      onPlay?.()
    } catch (error) {
      console.error('Play failed:', error)
      setIsLoading(false)
    }
  }, [track, onPlay])

  // Handle track changes
  useEffect(() => {
    if (track && audioRef.current) {
      const audio = audioRef.current
      
      // Reset state when track changes
      setIsPlaying(false)
      setCurrentTime(0)
      setDuration(0)
      setIsLoading(true)

      // Load new track
      audio.src = track.src
      audio.load()

      if (autoPlay) {
        // Small delay to ensure audio is loaded
        setTimeout(() => {
          handlePlay()
        }, 100)
      }
    }
  }, [track, autoPlay, handlePlay])

  // Handle volume changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume
    }
  }, [volume, isMuted])

  // Handle pause action
  const handlePause = useCallback(() => {
    if (!audioRef.current) return

    audioRef.current.pause()
    setIsPlaying(false)
    onPause?.()
  }, [onPause])

  // Toggle play/pause
  const togglePlayPause = useCallback(() => {
    if (isPlaying) {
      handlePause()
    } else {
      handlePlay()
    }
  }, [isPlaying, handlePlay, handlePause])

  // Handle seek functionality
  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !progressBarRef.current || !duration) return

    const rect = progressBarRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    const newTime = percentage * duration

    audioRef.current.currentTime = newTime
    setCurrentTime(newTime)
    onSeek?.(newTime)
  }

  // Handle volume control
  const handleVolumeChange = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!volumeBarRef.current) return

    const rect = volumeBarRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    
    setVolume(percentage)
    setIsMuted(false)
    onVolumeChange?.(percentage)
  }

  // Toggle mute
  const toggleMute = useCallback(() => {
    setIsMuted(!isMuted)
  }, [isMuted])

  // Handle next track
  const handleNext = () => {
    if (hasNext) {
      onNext?.()
    }
  }

  // Handle previous track
  const handlePrevious = () => {
    if (hasPrevious) {
      onPrevious?.()
    }
  }

  // Keyboard shortcuts
  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    if (!enableKeyboardShortcuts) return

    // Don't trigger if user is typing in an input
    if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return

    switch (e.code) {
      case 'Space':
        e.preventDefault()
        togglePlayPause()
        break
      case 'ArrowLeft':
        e.preventDefault()
        if (audioRef.current && duration) {
          const newTime = Math.max(0, currentTime - 5)
          audioRef.current.currentTime = newTime
          setCurrentTime(newTime)
        }
        break
      case 'ArrowRight':
        e.preventDefault()
        if (audioRef.current && duration) {
          const newTime = Math.min(duration, currentTime + 5)
          audioRef.current.currentTime = newTime
          setCurrentTime(newTime)
        }
        break
      case 'ArrowUp':
        e.preventDefault()
        setVolume(prev => Math.min(1, prev + 0.1))
        setIsMuted(false)
        break
      case 'ArrowDown':
        e.preventDefault()
        setVolume(prev => Math.max(0, prev - 0.1))
        break
      case 'KeyM':
        e.preventDefault()
        toggleMute()
        break
    }
  }, [enableKeyboardShortcuts, currentTime, duration, togglePlayPause, toggleMute])

  // Add keyboard event listeners
  useEffect(() => {
    if (enableKeyboardShortcuts) {
      document.addEventListener('keydown', handleKeyPress)
      return () => document.removeEventListener('keydown', handleKeyPress)
    }
  }, [handleKeyPress, enableKeyboardShortcuts])

  // Format time in MM:SS format
  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '0:00'
    
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Calculate progress percentage
  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0

  // Audio event handlers
  const handleTimeUpdate = () => {
    if (audioRef.current && !isDragging) {
      const time = audioRef.current.currentTime
      setCurrentTime(time)
      onTimeUpdate?.(time)
    }
  }

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      const dur = audioRef.current.duration
      setDuration(dur)
      setIsLoading(false)
      onLoadedMetadata?.(dur)
    }
  }

  const handleEnded = () => {
    setIsPlaying(false)
    setCurrentTime(0)
    onEnded?.()
  }

  const handleLoadStart = () => {
    setIsLoading(true)
  }

  const handleCanPlay = () => {
    setIsLoading(false)
  }

  const handleError = (e: any) => {
    console.error('Audio playback error:', e)
    setIsLoading(false)
    setIsPlaying(false)
  }

  // Don't render anything if no track
  if (!track) {
    return (
      <div className={`flex items-center justify-center p-8 text-gray-400 ${className}`}>
        <Music className="w-8 h-8 mr-3" />
        <span>No track selected</span>
      </div>
    )
  }

  return (
    <div className={`bg-gray-900 rounded-lg border border-gray-800 p-4 ${className}`}>
      {/* Hidden HTML5 Audio Element */}
      <audio
        ref={audioRef}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={handleEnded}
        onLoadStart={handleLoadStart}
        onCanPlay={handleCanPlay}
        onError={handleError}
        preload="metadata"
      />

      {/* Track Info Section */}
      {showTrackInfo && (
        <div className="flex items-center space-x-4 mb-4">
          {/* Track Artwork Placeholder */}
          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
            <Music className="w-6 h-6 text-white" />
          </div>
          
          {/* Track Details */}
          <div className="flex-1 min-w-0">
            <h3 className="text-white font-medium truncate">{track.title}</h3>
            <p className="text-gray-400 text-sm truncate">{track.artist}</p>
            {track.aiTool && (
              <p className="text-purple-400 text-xs">Generated with {track.aiTool}</p>
            )}
          </div>
        </div>
      )}

      {/* Controls Section */}
      {showControls && (
        <div className="space-y-4">
          {/* Progress Bar with Seek */}
          {showSeekBar && (
            <div className="space-y-2">
              <div 
                ref={progressBarRef}
                onClick={handleSeek}
                className="relative w-full h-2 bg-gray-700 rounded-full overflow-hidden cursor-pointer group"
              >
                <div 
                  className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-200"
                  style={{ width: `${progressPercentage}%` }}
                />
                {/* Hover indicator */}
                <div className="absolute top-1/2 left-0 w-3 h-3 bg-white rounded-full shadow-md transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                     style={{ left: `${progressPercentage}%`, marginLeft: '-6px' }} />
              </div>
              
              {/* Time Display */}
              <div className="flex justify-between text-xs text-gray-400">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex items-center justify-center space-x-6">
            {/* Previous Button */}
            {showSkipButtons && (
              <button
                onClick={handlePrevious}
                disabled={!hasPrevious}
                className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <SkipBack className="w-5 h-5" />
              </button>
            )}

            {/* Play/Pause Button */}
            <button
              onClick={togglePlayPause}
              disabled={isLoading}
              className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 text-white animate-spin" />
              ) : isPlaying ? (
                <Pause className="w-5 h-5 text-white" />
              ) : (
                <Play className="w-5 h-5 text-white ml-0.5" />
              )}
            </button>

            {/* Next Button */}
            {showSkipButtons && (
              <button
                onClick={handleNext}
                disabled={!hasNext}
                className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <SkipForward className="w-5 h-5" />
              </button>
            )}
          </div>

          {/* Volume Control */}
          {showVolumeControl && (
            <div className="flex items-center justify-center space-x-3">
              <button
                onClick={toggleMute}
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                {isMuted || volume === 0 ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </button>
              
              {/* Volume Bar */}
              <div 
                ref={volumeBarRef}
                onClick={handleVolumeChange}
                className="relative w-20 h-1 bg-gray-700 rounded-full cursor-pointer group"
              >
                <div 
                  className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-200"
                  style={{ width: `${isMuted ? 0 : volume * 100}%` }}
                />
                {/* Volume indicator */}
                <div className="absolute top-1/2 left-0 w-2 h-2 bg-white rounded-full shadow-md transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                     style={{ left: `${(isMuted ? 0 : volume) * 100}%`, marginLeft: '-4px' }} />
              </div>
              
              <span className="text-xs text-gray-400 w-8 text-right">
                {Math.round((isMuted ? 0 : volume) * 100)}%
              </span>
            </div>
          )}
        </div>
      )}

      {/* Keyboard Shortcuts Info */}
      {enableKeyboardShortcuts && (
        <div className="mt-3 text-xs text-gray-500 text-center">
          Space: Play/Pause • ←/→: Seek • ↑/↓: Volume • M: Mute
        </div>
      )}
    </div>
  )
} 