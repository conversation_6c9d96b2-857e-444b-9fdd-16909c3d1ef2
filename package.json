{"name": "tunami", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3003", "build": "next build", "start": "next start --port 3003", "lint": "next lint", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true npm run build", "build:analyze": "cross-env ANALYZE=true npm run build", "perf:lighthouse": "lighthouse http://localhost:3003 --view --output=html --output-path=./performance-reports/lighthouse.html", "perf:bundle": "npm run analyze && open bundle-report.html", "test:safari": "npx playwright test --project=webkit", "test:chrome": "npx playwright test --project=chromium", "test:firefox": "npx playwright test --project=firefox", "test:mobile": "npx playwright test --project=mobile-chrome --project=mobile-safari", "test:cross-browser": "npx playwright test", "browser:compatibility": "node -e \"console.log('Run in browser: runBrowserTest()')\"", "setup:test": "npx playwright install"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.38.4", "clsx": "^2.0.0", "critters": "^0.0.20", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "framer-motion": "^12.14.0", "lucide-react": "^0.294.0", "music-metadata": "^11.2.3", "next": "^15.0.3", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "tailwind-merge": "^2.2.0", "tunami": "file:"}, "devDependencies": {"@playwright/test": "^1.52.0", "@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "^15.0.3", "file-loader": "^6.2.0", "lighthouse": "^11.7.1", "postcss": "^8", "tailwindcss": "^3.4.0", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}