import React from 'react'
import { Loader2 } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white' | 'gray'
  className?: string
  label?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = '',
  label
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'xs': return 'w-3 h-3'
      case 'sm': return 'w-4 h-4'
      case 'md': return 'w-6 h-6'
      case 'lg': return 'w-8 h-8'
      case 'xl': return 'w-12 h-12'
      default: return 'w-6 h-6'
    }
  }

  const getColorClass = () => {
    switch (color) {
      case 'primary': return 'text-purple-400'
      case 'secondary': return 'text-blue-400'
      case 'white': return 'text-white'
      case 'gray': return 'text-gray-400'
      default: return 'text-purple-400'
    }
  }

  return (
    <div className={`inline-flex items-center gap-2 ${className}`}>
      <Loader2 
        className={`animate-spin ${getSizeClass()} ${getColorClass()}`}
        aria-hidden={!label}
      />
      {label && (
        <span className={`text-sm ${getColorClass()}`}>
          {label}
        </span>
      )}
      {label && <span className="sr-only">{label}</span>}
    </div>
  )
}

// Specialized loading components
export const PageLoading: React.FC<{ message?: string }> = ({ 
  message = "Loading..." 
}) => (
  <div className="flex items-center justify-center min-h-[60vh]">
    <LoadingSpinner size="lg" label={message} />
  </div>
)

export const ButtonLoading: React.FC<{ message?: string }> = ({ 
  message = "Loading..." 
}) => (
  <LoadingSpinner size="sm" label={message} />
)

export const InlineLoading: React.FC<{ message?: string }> = ({ 
  message 
}) => (
  <LoadingSpinner size="sm" label={message} />
)

export default LoadingSpinner 