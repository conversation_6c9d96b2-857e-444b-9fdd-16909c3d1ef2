'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Search, X, Clock, TrendingUp, Music, User, Mic } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { SearchService } from '@/lib/search-service'
import { SearchSuggestion, SearchHistory } from '@/types/search'
import { debounce } from '@/utils/debounce'

interface SearchBarProps {
  placeholder?: string
  showSuggestions?: boolean
  autoFocus?: boolean
  onSearch?: (query: string) => void
  className?: string
}

export default function SearchBar({
  placeholder = "Search tracks, artists, playlists...",
  showSuggestions = true,
  autoFocus = false,
  onSearch,
  className = ""
}: SearchBarProps) {
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])
  const [showDropdown, setShowDropdown] = useState(false)
  const [loading, setLoading] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  
  const router = useRouter()
  const { user } = useAuth()
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.trim().length < 2) {
        setSuggestions([])
        return
      }

      setLoading(true)
      try {
        const results = await SearchService.getSuggestions(searchQuery)
        setSuggestions(results)
      } catch (error) {
        console.error('Error fetching suggestions:', error)
        setSuggestions([])
      } finally {
        setLoading(false)
      }
    }, 300),
    []
  )

  // Load search history on mount
  useEffect(() => {
    if (user && showSuggestions) {
      loadSearchHistory()
    }
  }, [user, showSuggestions])

  // Handle input changes
  useEffect(() => {
    if (query.trim()) {
      debouncedSearch(query)
    } else {
      setSuggestions([])
      if (user && showSuggestions) {
        loadSearchHistory()
      }
    }
  }, [query, debouncedSearch, user, showSuggestions])

  // Auto focus
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  const loadSearchHistory = async () => {
    if (!user) return
    
    try {
      const history = await SearchService.getSearchHistory(user.id)
      setSearchHistory(history)
    } catch (error) {
      console.error('Error loading search history:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    setSelectedIndex(-1)
    
    if (value.trim() && showSuggestions) {
      setShowDropdown(true)
    }
  }

  const handleSearch = async (searchQuery?: string) => {
    const finalQuery = searchQuery || query
    if (!finalQuery.trim()) return

    // Save to search history
    if (user) {
      try {
        await SearchService.saveSearchHistory(user.id, finalQuery.trim())
      } catch (error) {
        console.error('Error saving search history:', error)
      }
    }

    // Close dropdown
    setShowDropdown(false)
    setSelectedIndex(-1)

    // Call onSearch callback or navigate to search page
    if (onSearch) {
      onSearch(finalQuery.trim())
    } else {
      router.push(`/search?q=${encodeURIComponent(finalQuery.trim())}`)
    }
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text)
    handleSearch(suggestion.text)
  }

  const handleHistoryClick = (historyItem: SearchHistory) => {
    setQuery(historyItem.query)
    handleSearch(historyItem.query)
  }

  const clearHistory = async () => {
    if (!user) return
    
    try {
      await SearchService.clearSearchHistory(user.id)
      setSearchHistory([])
    } catch (error) {
      console.error('Error clearing search history:', error)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showDropdown) return

    const totalItems = suggestions.length + (searchHistory.length > 0 ? searchHistory.length : 0)

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev < totalItems - 1 ? prev + 1 : -1))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => (prev > -1 ? prev - 1 : totalItems - 1))
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0) {
          if (selectedIndex < suggestions.length) {
            handleSuggestionClick(suggestions[selectedIndex])
          } else {
            const historyIndex = selectedIndex - suggestions.length
            handleHistoryClick(searchHistory[historyIndex])
          }
        } else {
          handleSearch()
        }
        break
      case 'Escape':
        setShowDropdown(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  const clearInput = () => {
    setQuery('')
    setSuggestions([])
    setShowDropdown(false)
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowDropdown(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'track':
        return <Music className="w-4 h-4 text-gray-400" />
      case 'artist':
        return <User className="w-4 h-4 text-gray-400" />
      case 'playlist':
        return <Music className="w-4 h-4 text-gray-400" />
      case 'ai_tool':
        return <Mic className="w-4 h-4 text-gray-400" />
      default:
        return <Search className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (showSuggestions && (suggestions.length > 0 || searchHistory.length > 0)) {
              setShowDropdown(true)
            }
          }}
          placeholder={placeholder}
          className="w-full bg-gray-800 border border-gray-700 rounded-lg py-3 pl-12 pr-12 
                   focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent
                   placeholder-gray-400 text-white transition-all"
        />
        
        {/* Clear Button */}
        {query && (
          <button
            onClick={clearInput}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        )}

        {/* Loading Indicator */}
        {loading && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"></div>
          </div>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showDropdown && showSuggestions && (
        <div
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 mt-2 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-96 overflow-y-auto"
        >
          {/* Search Suggestions */}
          {suggestions.length > 0 && (
            <div className="p-2">
              <div className="text-xs font-medium text-gray-400 px-3 py-2 flex items-center space-x-2">
                <TrendingUp className="w-3 h-3" />
                <span>Suggestions</span>
              </div>
              {suggestions.map((suggestion, index) => (
                <button
                  key={`suggestion-${index}`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className={`w-full text-left px-3 py-2 rounded-lg flex items-center space-x-3 transition-colors ${
                    selectedIndex === index
                      ? 'bg-purple-600 text-white'
                      : 'hover:bg-gray-700 text-gray-300'
                  }`}
                >
                  {getSuggestionIcon(suggestion.type)}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{suggestion.text}</div>
                    {suggestion.subtitle && (
                      <div className="text-sm text-gray-400 truncate">{suggestion.subtitle}</div>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 capitalize">{suggestion.type}</div>
                </button>
              ))}
            </div>
          )}

          {/* Search History */}
          {searchHistory.length > 0 && !query.trim() && (
            <div className="p-2 border-t border-gray-700">
              <div className="flex items-center justify-between px-3 py-2">
                <div className="text-xs font-medium text-gray-400 flex items-center space-x-2">
                  <Clock className="w-3 h-3" />
                  <span>Recent Searches</span>
                </div>
                <button
                  onClick={clearHistory}
                  className="text-xs text-gray-500 hover:text-gray-300 transition-colors"
                >
                  Clear
                </button>
              </div>
              {searchHistory.slice(0, 5).map((item, index) => (
                <button
                  key={`history-${item.id}`}
                  onClick={() => handleHistoryClick(item)}
                  className={`w-full text-left px-3 py-2 rounded-lg flex items-center space-x-3 transition-colors ${
                    selectedIndex === suggestions.length + index
                      ? 'bg-purple-600 text-white'
                      : 'hover:bg-gray-700 text-gray-300'
                  }`}
                >
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="flex-1 truncate">{item.query}</span>
                </button>
              ))}
            </div>
          )}

          {/* No Results */}
          {query.trim() && suggestions.length === 0 && !loading && (
            <div className="p-4 text-center text-gray-400">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No suggestions found</p>
              <p className="text-sm">Try a different search term</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
} 