{"name": "Tunami - AI Music Platform", "short_name": "<PERSON><PERSON><PERSON>", "description": "Discover, share, and enjoy AI-generated music with Tunami's intelligent platform", "start_url": "/", "display": "standalone", "background_color": "#111827", "theme_color": "#7c3aed", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "ltr", "categories": ["music", "entertainment", "multimedia"], "screenshots": [{"src": "/screenshots/desktop-home.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Tunami desktop home screen"}, {"src": "/screenshots/mobile-player.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Tunami mobile music player"}], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}], "shortcuts": [{"name": "Upload Music", "short_name": "Upload", "description": "Upload new AI music tracks", "url": "/upload", "icons": [{"src": "/icons/upload-shortcut.png", "sizes": "192x192"}]}, {"name": "Browse Music", "short_name": "Browse", "description": "Discover new AI music", "url": "/browse", "icons": [{"src": "/icons/browse-shortcut.png", "sizes": "192x192"}]}, {"name": "My Playlists", "short_name": "Playlists", "description": "View your music playlists", "url": "/playlists", "icons": [{"src": "/icons/playlist-shortcut.png", "sizes": "192x192"}]}], "share_target": {"action": "/share", "method": "POST", "enctype": "multipart/form-data", "params": {"title": "title", "text": "text", "url": "url", "files": [{"name": "audio", "accept": ["audio/*"]}]}}, "protocol_handlers": [{"protocol": "web+tunami", "url": "/play?track=%s"}], "edge_side_panel": {"preferred_width": 400}, "file_handlers": [{"action": "/upload", "accept": {"audio/*": [".mp3", ".wav", ".flac", ".ogg", ".m4a"]}}], "prefer_related_applications": false, "related_applications": [], "iarc_rating_id": "e84b072d-71b3-4d3e-86ae-31a8ce4e53b7", "permissions": ["audioCapture", "mediaDevices", "storage", "notifications"], "display_override": ["standalone", "minimal-ui", "browser"], "launch_handler": {"client_mode": "navigate-existing"}}