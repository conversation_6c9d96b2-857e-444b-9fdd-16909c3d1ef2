'use client'

import React, { memo, useCallback } from 'react'
import { <PERSON>, Pause, Heart, MoreHorizontal, Clock, Music } from 'lucide-react'
import { Track } from '@/types/database'
import { AlbumArt } from '@/components/ui/OptimizedImage'
import { cn } from '@/lib/utils'

interface TrackCardProps {
  track: Track
  isPlaying?: boolean
  onPlay?: (track: Track) => void
  onPause?: () => void
  onLike?: (trackId: string) => void
  onAddToPlaylist?: (track: Track) => void
  variant?: 'default' | 'compact' | 'featured'
  className?: string
  showArtist?: boolean
  showDuration?: boolean
  priority?: boolean
}

const TrackCard = memo<TrackCardProps>(({
  track,
  isPlaying = false,
  onPlay,
  onPause,
  onLike,
  onAddToPlaylist,
  variant = 'default',
  className = '',
  showArtist = true,
  showDuration = true,
  priority = false
}) => {
  const handlePlayPause = useCallback(() => {
    if (isPlaying) {
      onPause?.()
    } else {
      onPlay?.(track)
    }
  }, [isPlaying, onPlay, onPause, track])

  const handleLike = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    onLike?.(track.id)
  }, [onLike, track.id])

  const handleAddToPlaylist = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    onAddToPlaylist?.(track)
  }, [onAddToPlaylist, track])

  const formatDuration = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }, [])

  if (variant === 'compact') {
    return (
      <div className={cn(
        'flex items-center gap-3 p-3 rounded-lg bg-gray-800/50 hover:bg-gray-800 transition-all duration-200 group cursor-pointer',
        className
      )}>
        <div className="relative">
          <AlbumArt
            src={track.artwork || ''}
            alt={track.title}
            size="sm"
            priority={priority}
            className="rounded"
          />
          <button
            onClick={handlePlayPause}
            className="absolute inset-0 bg-black/50 text-white rounded opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
          >
            {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </button>
        </div>

        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-white truncate">{track.title}</h4>
          {showArtist && (
            <p className="text-sm text-gray-400 truncate">{track.artist}</p>
          )}
        </div>

        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={handleLike}
            className="p-1 text-gray-400 hover:text-pink-400 transition-colors"
          >
            <Heart className="w-4 h-4" />
          </button>
          {showDuration && (
            <span className="text-xs text-gray-500 min-w-[2.5rem]">
              {formatDuration(track.duration || 0)}
            </span>
          )}
        </div>
      </div>
    )
  }

  if (variant === 'featured') {
    return (
      <div className={cn(
        'relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 group cursor-pointer overflow-hidden',
        'hover:scale-[1.02] transition-all duration-300 shadow-lg hover:shadow-xl',
        className
      )}>
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-blue-400/20" />
        </div>

        <div className="relative z-10">
          <div className="flex items-start gap-4 mb-4">
            <div className="relative">
              <AlbumArt
                src={track.artwork || ''}
                alt={track.title}
                size="lg"
                priority={priority}
                className="rounded-lg shadow-lg"
              />
              <button
                onClick={handlePlayPause}
                className="absolute inset-0 bg-black/50 text-white rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center transform scale-95 group-hover:scale-100"
              >
                {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
              </button>
            </div>

            <div className="flex-1 min-w-0">
              <h3 className="text-xl font-bold text-white mb-2 line-clamp-2">{track.title}</h3>
              {showArtist && (
                <p className="text-gray-300 mb-3">{track.artist}</p>
              )}
              
              <div className="flex items-center gap-4 text-sm text-gray-400">
                {showDuration && (
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {formatDuration(track.duration || 0)}
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <Music className="w-4 h-4" />
                  {track.genre || 'Unknown'}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <button
                onClick={handleLike}
                className="p-2 text-gray-400 hover:text-pink-400 transition-colors rounded-full hover:bg-gray-700/50"
              >
                <Heart className="w-5 h-5" />
              </button>
              <button
                onClick={handleAddToPlaylist}
                className="p-2 text-gray-400 hover:text-white transition-colors rounded-full hover:bg-gray-700/50"
              >
                <MoreHorizontal className="w-5 h-5" />
              </button>
            </div>
            
            <div className="text-xs text-gray-500">
              {new Date(track.created_at).toLocaleDateString()}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Default variant
  return (
    <div className={cn(
      'bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800 transition-all duration-200 group cursor-pointer',
      'hover:shadow-lg transform hover:-translate-y-1',
      className
    )}>
      <div className="relative mb-4">
        <AlbumArt
          src={track.artwork || ''}
          alt={track.title}
          size="md"
          priority={priority}
          className="rounded-lg"
        />
        
        <button
          onClick={handlePlayPause}
          className="absolute inset-0 bg-black/50 text-white rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
        >
          {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
        </button>

        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={handleLike}
            className="p-1 bg-black/50 text-white rounded-full hover:bg-pink-500/80 transition-colors"
          >
            <Heart className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="space-y-1">
        <h4 className="font-medium text-white truncate">{track.title}</h4>
        {showArtist && (
          <p className="text-sm text-gray-400 truncate">{track.artist}</p>
        )}
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          {showDuration && (
            <span>{formatDuration(track.duration || 0)}</span>
          )}
          <button
            onClick={handleAddToPlaylist}
            className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:text-white"
          >
            <MoreHorizontal className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
})

TrackCard.displayName = 'TrackCard'

export default TrackCard 