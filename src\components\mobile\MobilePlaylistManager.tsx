'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import {
  Play,
  Pause,
  MoreVertical,
  Heart,
  HeartOff,
  Download,
  DownloadOff,
  Share2,
  Trash2,
  Plus,
  Edit3,
  Music,
  Clock,
  Users,
  Lock,
  Unlock,
  DragHandleDots2,
  Arrow<PERSON>p,
  ArrowDown,
  Check,
  X,
  Copy,
  ExternalLink
} from 'lucide-react'
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd'
import { useAuth } from '@/contexts/AuthContext'
import { useAudio } from '@/contexts/AudioContext'
import { usePlaylist } from '@/hooks/usePlaylist'
import { useSwipeGestures } from '@/hooks/useSwipeGestures'
import { useMobileDetection } from '@/hooks/useMobileDetection'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'
import { Playlist, PlaylistTrack } from '@/types/playlist'
import { Track } from '@/types/audio'

interface MobilePlaylistManagerProps {
  playlist: Playlist
  onUpdate?: (playlist: Playlist) => void
  onClose?: () => void
  className?: string
}

interface SwipeAction {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  color: string
  action: (track: PlaylistTrack) => void
}

const leftSwipeActions: SwipeAction[] = [
  {
    id: 'like',
    label: 'Like',
    icon: Heart,
    color: 'bg-red-500',
    action: (track) => console.log('Like track:', track.track.title)
  },
  {
    id: 'download',
    label: 'Download',
    icon: Download,
    color: 'bg-blue-500',
    action: (track) => console.log('Download track:', track.track.title)
  }
]

const rightSwipeActions: SwipeAction[] = [
  {
    id: 'share',
    label: 'Share',
    icon: Share2,
    color: 'bg-purple-500',
    action: (track) => console.log('Share track:', track.track.title)
  },
  {
    id: 'remove',
    label: 'Remove',
    icon: Trash2,
    color: 'bg-red-600',
    action: (track) => console.log('Remove track:', track.track.title)
  }
]

export default function MobilePlaylistManager({
  playlist,
  onUpdate,
  onClose,
  className = ''
}: MobilePlaylistManagerProps) {
  const router = useRouter()
  const { user } = useAuth()
  const { currentTrack, isPlaying, playTrack, pauseTrack, playPlaylist } = useAudio()
  const { updatePlaylist, reorderPlaylistTracks, removeTrackFromPlaylist, loading } = usePlaylist()
  const mobileDetection = useMobileDetection()

  const [tracks, setTracks] = useState<PlaylistTrack[]>(playlist.tracks || [])
  const [selectedTracks, setSelectedTracks] = useState<Set<string>>(new Set())
  const [isReordering, setIsReordering] = useState(false)
  const [isSelecting, setIsSelecting] = useState(false)
  const [swipeActiveId, setSwipeActiveId] = useState<string | null>(null)
  const [showPlaylistActions, setShowPlaylistActions] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)

  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    setTracks(playlist.tracks || [])
  }, [playlist.tracks])

  const isOwner = user?.id === playlist.user_id

  const handlePlayTrack = (track: Track, index: number) => {
    if (currentTrack?.id === track.id && isPlaying) {
      pauseTrack()
    } else {
      playPlaylist(playlist, index)
    }
  }

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination || !isOwner) return

    const { source, destination } = result
    if (source.index === destination.index) return

    // Optimistic update
    const newTracks = Array.from(tracks)
    const [reorderedTrack] = newTracks.splice(source.index, 1)
    newTracks.splice(destination.index, 0, reorderedTrack)
    setTracks(newTracks)

    // Prepare reorder data
    const reorderData = newTracks.map((track, index) => ({
      playlist_id: playlist.id,
      track_id: track.track_id,
      new_position: index + 1
    }))

    try {
      await reorderPlaylistTracks(playlist.id, reorderData)
      // Update parent component
      const updatedPlaylist = { ...playlist, tracks: newTracks }
      onUpdate?.(updatedPlaylist)
    } catch (error) {
      console.error('Failed to reorder tracks:', error)
      // Revert on error
      setTracks(playlist.tracks || [])
    }
  }

  const handleRemoveTrack = async (trackId: string) => {
    if (!isOwner) return

    try {
      await removeTrackFromPlaylist(playlist.id, trackId)
      const newTracks = tracks.filter(t => t.track_id !== trackId)
      setTracks(newTracks)
      
      const updatedPlaylist = { ...playlist, tracks: newTracks }
      onUpdate?.(updatedPlaylist)
    } catch (error) {
      console.error('Failed to remove track:', error)
    }
  }

  const handleBulkRemove = async () => {
    if (!isOwner || selectedTracks.size === 0) return

    try {
      const removePromises = Array.from(selectedTracks).map(trackId =>
        removeTrackFromPlaylist(playlist.id, trackId)
      )
      
      await Promise.all(removePromises)
      
      const newTracks = tracks.filter(t => !selectedTracks.has(t.track_id))
      setTracks(newTracks)
      setSelectedTracks(new Set())
      setIsSelecting(false)
      
      const updatedPlaylist = { ...playlist, tracks: newTracks }
      onUpdate?.(updatedPlaylist)
    } catch (error) {
      console.error('Failed to remove tracks:', error)
    }
  }

  const toggleTrackSelection = (trackId: string) => {
    const newSelected = new Set(selectedTracks)
    if (newSelected.has(trackId)) {
      newSelected.delete(trackId)
    } else {
      newSelected.add(trackId)
    }
    setSelectedTracks(newSelected)
  }

  const selectAllTracks = () => {
    setSelectedTracks(new Set(tracks.map(t => t.track_id)))
  }

  const clearSelection = () => {
    setSelectedTracks(new Set())
    setIsSelecting(false)
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatTrackCount = (count: number) => {
    return count === 1 ? '1 track' : `${count} tracks`
  }

  // Don't render on desktop - use the regular playlist component
  if (mobileDetection.isDesktop) {
    return null
  }

  return (
    <ComponentErrorBoundary>
      <div className={`bg-gray-900 min-h-screen ${className}`} ref={containerRef}>
        {/* Header */}
        <div className="sticky top-0 bg-gray-900/95 backdrop-blur-xl border-b border-gray-800 z-40 safe-area-top">
          <div className="px-4 py-3">
            {/* Main header */}
            <div className="flex items-center justify-between mb-3">
              <button
                onClick={onClose}
                className="p-2 -ml-2 rounded-full hover:bg-gray-800 transition-colors"
              >
                <ArrowDown className="w-5 h-5" />
              </button>

              <div className="flex items-center space-x-2">
                {isSelecting ? (
                  <>
                    <span className="text-sm text-gray-400">
                      {selectedTracks.size} selected
                    </span>
                    <button
                      onClick={selectAllTracks}
                      className="px-3 py-1.5 bg-purple-600 text-white text-xs rounded-full hover:bg-purple-700 transition-colors"
                    >
                      All
                    </button>
                    <button
                      onClick={clearSelection}
                      className="p-1.5 rounded-full hover:bg-gray-800 transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => setShowPlaylistActions(true)}
                    className="p-2 rounded-full hover:bg-gray-800 transition-colors"
                  >
                    <MoreVertical className="w-5 h-5" />
                  </button>
                )}
              </div>
            </div>

            {/* Playlist info */}
            <div className="flex items-start space-x-4 mb-4">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                {playlist.cover_image_url ? (
                  <img
                    src={playlist.cover_image_url}
                    alt={playlist.name}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <Music className="w-8 h-8 text-white" />
                )}
              </div>

              <div className="flex-1 min-w-0">
                <h1 className="text-xl font-bold text-white truncate mb-1">
                  {playlist.name}
                </h1>
                <p className="text-gray-400 text-sm mb-2 line-clamp-2">
                  {playlist.description || 'No description'}
                </p>
                <div className="flex items-center space-x-3 text-xs text-gray-500">
                  <span className="flex items-center space-x-1">
                    <Users className="w-3 h-3" />
                    <span>{playlist.user?.email || 'Unknown'}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Music className="w-3 h-3" />
                    <span>{formatTrackCount(tracks.length)}</span>
                  </span>
                  {playlist.is_public ? (
                    <Unlock className="w-3 h-3" />
                  ) : (
                    <Lock className="w-3 h-3" />
                  )}
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => tracks.length > 0 && playPlaylist(playlist, 0)}
                disabled={tracks.length === 0}
                className="flex items-center space-x-2 px-6 py-3 bg-purple-600 text-white rounded-full hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex-1"
              >
                <Play className="w-5 h-5" />
                <span className="font-medium">Play</span>
              </button>

              {isOwner && (
                <>
                  <button
                    onClick={() => setIsReordering(!isReordering)}
                    className={`p-3 rounded-full transition-colors ${
                      isReordering
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    <DragHandleDots2 className="w-5 h-5" />
                  </button>

                  <button
                    onClick={() => setIsSelecting(!isSelecting)}
                    className={`p-3 rounded-full transition-colors ${
                      isSelecting
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    <Check className="w-5 h-5" />
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Track list */}
        <div className="pb-safe-bottom">
          {tracks.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 px-4">
              <Music className="w-16 h-16 text-gray-600 mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">No tracks yet</h3>
              <p className="text-gray-400 text-center mb-6">
                This playlist is empty. Add some tracks to get started.
              </p>
              {isOwner && (
                <button
                  onClick={() => router.push('/browse')}
                  className="px-6 py-3 bg-purple-600 text-white rounded-full hover:bg-purple-700 transition-colors"
                >
                  Browse Music
                </button>
              )}
            </div>
          ) : (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="playlist-tracks" isDropDisabled={!isReordering}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={`px-4 space-y-1 ${
                      snapshot.isDraggingOver ? 'bg-gray-800/50' : ''
                    }`}
                  >
                    {tracks.map((playlistTrack, index) => (
                      <MobileTrackItem
                        key={playlistTrack.track_id}
                        playlistTrack={playlistTrack}
                        index={index}
                        isOwner={isOwner}
                        isReordering={isReordering}
                        isSelecting={isSelecting}
                        isSelected={selectedTracks.has(playlistTrack.track_id)}
                        isCurrentTrack={currentTrack?.id === playlistTrack.track.id}
                        isPlaying={currentTrack?.id === playlistTrack.track.id && isPlaying}
                        onPlay={() => handlePlayTrack(playlistTrack.track, index)}
                        onSelect={() => toggleTrackSelection(playlistTrack.track_id)}
                        onRemove={() => handleRemoveTrack(playlistTrack.track_id)}
                      />
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </div>

        {/* Selection actions bar */}
        {isSelecting && selectedTracks.size > 0 && (
          <div className="fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700 p-4 safe-area-bottom">
            <div className="flex items-center justify-between">
              <span className="text-white font-medium">
                {selectedTracks.size} track{selectedTracks.size !== 1 ? 's' : ''} selected
              </span>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleBulkRemove}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Remove
                </button>
                <button
                  onClick={clearSelection}
                  className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Playlist actions modal */}
        {showPlaylistActions && (
          <PlaylistActionsModal
            playlist={playlist}
            isOwner={isOwner}
            onClose={() => setShowPlaylistActions(false)}
            onEdit={() => {
              setShowPlaylistActions(false)
              setShowEditModal(true)
            }}
          />
        )}
      </div>
    </ComponentErrorBoundary>
  )
}

// Mobile Track Item Component with swipe actions
interface MobileTrackItemProps {
  playlistTrack: PlaylistTrack
  index: number
  isOwner: boolean
  isReordering: boolean
  isSelecting: boolean
  isSelected: boolean
  isCurrentTrack: boolean
  isPlaying: boolean
  onPlay: () => void
  onSelect: () => void
  onRemove: () => void
}

function MobileTrackItem({
  playlistTrack,
  index,
  isOwner,
  isReordering,
  isSelecting,
  isSelected,
  isCurrentTrack,
  isPlaying,
  onPlay,
  onSelect,
  onRemove
}: MobileTrackItemProps) {
  const [swipeOffset, setSwipeOffset] = useState(0)
  const [swipeAction, setSwipeAction] = useState<SwipeAction | null>(null)
  const track = playlistTrack.track

  const swipeHandlers = useSwipeGestures({
    onSwipeStart: () => {
      if (isReordering || isSelecting) return false
      return true
    },
    onSwipeMove: (direction, distance) => {
      if (direction === 'left' || direction === 'right') {
        const maxOffset = 80
        const offset = Math.min(Math.abs(distance), maxOffset) * (direction === 'left' ? 1 : -1)
        setSwipeOffset(offset)

        // Determine action based on offset
        if (Math.abs(offset) > 40) {
          const actions = direction === 'left' ? leftSwipeActions : rightSwipeActions
          const actionIndex = Math.floor((Math.abs(offset) - 40) / 20)
          setSwipeAction(actions[Math.min(actionIndex, actions.length - 1)] || null)
        } else {
          setSwipeAction(null)
        }
      }
    },
    onSwipeEnd: (direction, distance) => {
      if (Math.abs(distance) > 60 && swipeAction) {
        swipeAction.action(playlistTrack)
      }
      setSwipeOffset(0)
      setSwipeAction(null)
    },
    threshold: 20,
    enabled: !isReordering && !isSelecting
  })

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <Draggable
      draggableId={playlistTrack.track_id}
      index={index}
      isDragDisabled={!isReordering}
    >
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`relative ${snapshot.isDragging ? 'z-50' : ''}`}
        >
          {/* Swipe actions background */}
          <div className="absolute inset-0 flex items-center justify-between px-4">
            {/* Left actions */}
            <div className="flex items-center space-x-2">
              {leftSwipeActions.map((action) => {
                const Icon = action.icon
                return (
                  <div
                    key={action.id}
                    className={`w-12 h-12 rounded-full flex items-center justify-center ${action.color} ${
                      swipeAction?.id === action.id ? 'scale-110' : 'scale-100'
                    } transition-transform`}
                  >
                    <Icon className="w-5 h-5 text-white" />
                  </div>
                )
              })}
            </div>

            {/* Right actions */}
            <div className="flex items-center space-x-2">
              {rightSwipeActions.map((action) => {
                const Icon = action.icon
                return (
                  <div
                    key={action.id}
                    className={`w-12 h-12 rounded-full flex items-center justify-center ${action.color} ${
                      swipeAction?.id === action.id ? 'scale-110' : 'scale-100'
                    } transition-transform`}
                  >
                    <Icon className="w-5 h-5 text-white" />
                  </div>
                )
              })}
            </div>
          </div>

          {/* Track item */}
          <div
            className={`bg-gray-900 border-b border-gray-800 p-4 transition-transform ${
              snapshot.isDragging ? 'shadow-lg rotate-2' : ''
            }`}
            style={{ transform: `translateX(${swipeOffset}px)` }}
            {...swipeHandlers}
          >
            <div className="flex items-center space-x-3">
              {/* Drag handle (reordering mode) */}
              {isReordering && (
                <div {...provided.dragHandleProps} className="p-1">
                  <DragHandleDots2 className="w-5 h-5 text-gray-400" />
                </div>
              )}

              {/* Selection checkbox (selection mode) */}
              {isSelecting && (
                <button
                  onClick={onSelect}
                  className={`w-6 h-6 rounded border-2 flex items-center justify-center transition-colors ${
                    isSelected
                      ? 'bg-purple-600 border-purple-600'
                      : 'border-gray-400 hover:border-purple-400'
                  }`}
                >
                  {isSelected && <Check className="w-4 h-4 text-white" />}
                </button>
              )}

              {/* Track number / Play button */}
              {!isReordering && !isSelecting && (
                <button
                  onClick={onPlay}
                  className="w-12 h-12 rounded-full bg-gray-800 hover:bg-purple-600 flex items-center justify-center transition-colors group"
                >
                  {isCurrentTrack && isPlaying ? (
                    <Pause className="w-5 h-5 text-white" />
                  ) : (
                    <Play className="w-5 h-5 text-white group-hover:scale-110 transition-transform" />
                  )}
                </button>
              )}

              {/* Track info */}
              <div className="flex-1 min-w-0">
                <h4 className={`font-medium truncate ${
                  isCurrentTrack ? 'text-purple-400' : 'text-white'
                }`}>
                  {track.title}
                </h4>
                <p className="text-gray-400 text-sm truncate">
                  {track.artist} • {track.ai_tool && `${track.ai_tool} • `}
                  {formatDuration(track.duration || 0)}
                </p>
              </div>

              {/* Track actions */}
              <div className="flex items-center space-x-1">
                {track.ai_tool && (
                  <div className="px-2 py-1 bg-purple-600/20 border border-purple-500/30 rounded text-xs text-purple-300">
                    AI
                  </div>
                )}
                
                {!isReordering && !isSelecting && (
                  <button className="p-2 rounded-full hover:bg-gray-800 transition-colors">
                    <MoreVertical className="w-4 h-4 text-gray-400" />
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </Draggable>
  )
}

// Playlist Actions Modal
interface PlaylistActionsModalProps {
  playlist: Playlist
  isOwner: boolean
  onClose: () => void
  onEdit: () => void
}

function PlaylistActionsModal({
  playlist,
  isOwner,
  onClose,
  onEdit
}: PlaylistActionsModalProps) {
  const handleShare = async () => {
    if (navigator.share && playlist.is_public) {
      try {
        await navigator.share({
          title: playlist.name,
          text: playlist.description || `Check out this playlist: ${playlist.name}`,
          url: `${window.location.origin}/playlist/${playlist.id}`
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      const url = `${window.location.origin}/playlist/${playlist.id}`
      await navigator.clipboard.writeText(url)
      // TODO: Show toast notification
    }
    onClose()
  }

  const actions = [
    ...(isOwner ? [
      {
        id: 'edit',
        label: 'Edit Playlist',
        icon: Edit3,
        action: onEdit
      }
    ] : []),
    {
      id: 'share',
      label: 'Share',
      icon: Share2,
      action: handleShare
    },
    {
      id: 'copy-link',
      label: 'Copy Link',
      icon: Copy,
      action: async () => {
        const url = `${window.location.origin}/playlist/${playlist.id}`
        await navigator.clipboard.writeText(url)
        onClose()
      }
    }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
      <div className="bg-gray-800 rounded-t-xl w-full max-w-md p-4 safe-area-bottom">
        <div className="w-12 h-1 bg-gray-600 rounded-full mx-auto mb-4" />
        
        <h3 className="text-lg font-semibold text-white mb-4">Playlist Actions</h3>
        
        <div className="space-y-2">
          {actions.map((action) => {
            const Icon = action.icon
            return (
              <button
                key={action.id}
                onClick={action.action}
                className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-700 transition-colors text-left"
              >
                <Icon className="w-5 h-5 text-gray-400" />
                <span className="text-white font-medium">{action.label}</span>
              </button>
            )
          })}
        </div>
        
        <button
          onClick={onClose}
          className="w-full mt-4 p-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
        >
          Cancel
        </button>
      </div>
    </div>
  )
} 