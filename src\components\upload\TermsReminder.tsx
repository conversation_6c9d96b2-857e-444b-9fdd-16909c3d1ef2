'use client'

import { useState } from 'react'
import { AlertTriangle, Info, Check, X, ExternalLink, Shield, Copyright, Scale } from 'lucide-react'

interface TermsReminderProps {
  onAccept: () => void
  onDecline: () => void
  isVisible: boolean
  className?: string
}

export default function TermsReminder({ 
  onAccept, 
  onDecline, 
  isVisible, 
  className = '' 
}: TermsReminderProps) {
  const [hasReadTerms, setHasReadTerms] = useState(false)

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4">
      <div className={`bg-gray-900 rounded-xl border border-gray-700 max-w-2xl w-full max-h-[90vh] overflow-y-auto ${className}`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-amber-500 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-white" />
            </div>
            <h2 className="text-xl font-bold text-white">AI-Generated Content Guidelines</h2>
          </div>
          <p className="text-gray-400 text-sm">
            Please review these important guidelines before uploading AI-generated music
          </p>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Copyright & Ownership */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-red-400">
              <Copyright className="w-5 h-5" />
              <h3 className="font-semibold">Copyright & Ownership</h3>
            </div>
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <ul className="space-y-2 text-sm text-gray-300">
                <li className="flex items-start gap-2">
                  <span className="text-red-400 mt-1">•</span>
                  <span>You must own or have proper licensing for all AI-generated content</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-red-400 mt-1">•</span>
                  <span>Some AI tools may have specific licensing requirements - check your tool&apos;s terms</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-red-400 mt-1">•</span>
                  <span>You are responsible for ensuring no copyrighted material was used in training data</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Platform Guidelines */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-blue-400">
              <Shield className="w-5 h-5" />
              <h3 className="font-semibold">Platform Guidelines</h3>
            </div>
            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
              <ul className="space-y-2 text-sm text-gray-300">
                <li className="flex items-start gap-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>All uploads must be original AI-generated music</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>Clearly indicate the AI tool used for transparency</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>No explicit, hateful, or harmful content allowed</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>Respect community guidelines and other users</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Legal Considerations */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-purple-400">
              <Scale className="w-5 h-5" />
              <h3 className="font-semibold">Legal Considerations</h3>
            </div>
            <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-4">
              <ul className="space-y-2 text-sm text-gray-300">
                <li className="flex items-start gap-2">
                  <span className="text-purple-400 mt-1">•</span>
                  <span>AI-generated music may have different copyright implications than human-created music</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-purple-400 mt-1">•</span>
                  <span>Some jurisdictions may not recognize AI-generated works as copyrightable</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-purple-400 mt-1">•</span>
                  <span>Consider consulting legal advice for commercial use of AI-generated content</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Best Practices */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-green-400">
              <Info className="w-5 h-5" />
              <h3 className="font-semibold">Best Practices</h3>
            </div>
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
              <ul className="space-y-2 text-sm text-gray-300">
                <li className="flex items-start gap-2">
                  <span className="text-green-400 mt-1">•</span>
                  <span>Document your creative process and AI prompts used</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-400 mt-1">•</span>
                  <span>Give proper attribution to AI tools and any inspiration sources</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-400 mt-1">•</span>
                  <span>Be transparent about AI generation in your track descriptions</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-400 mt-1">•</span>
                  <span>Respect the terms of service of the AI tools you use</span>
                </li>
              </ul>
            </div>
          </div>

          {/* External Resources */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-300 flex items-center gap-2">
              <ExternalLink className="w-4 h-4" />
              Learn More
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <a 
                href="https://www.copyright.gov/ai/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-sm text-blue-400 hover:text-blue-300 underline flex items-center gap-1"
              >
                US Copyright Office on AI
                <ExternalLink className="w-3 h-3" />
              </a>
              <a 
                href="https://creativecommons.org/licenses/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-sm text-blue-400 hover:text-blue-300 underline flex items-center gap-1"
              >
                Creative Commons Licenses
                <ExternalLink className="w-3 h-3" />
              </a>
            </div>
          </div>

          {/* Acknowledgment Checkbox */}
          <div className="border-t border-gray-700 pt-6">
            <label className="flex items-start gap-3 cursor-pointer">
              <button
                type="button"
                onClick={() => setHasReadTerms(!hasReadTerms)}
                className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center mt-0.5 transition-colors
                  ${hasReadTerms 
                    ? 'bg-purple-600 border-purple-600' 
                    : 'border-gray-500 hover:border-purple-400'
                  }
                `}
              >
                {hasReadTerms && <Check className="w-3 h-3 text-white" />}
              </button>
              <div className="text-sm text-gray-300">
                <p className="font-medium mb-1">I acknowledge that:</p>
                <ul className="space-y-1 text-xs text-gray-400">
                  <li>• I have read and understood these guidelines</li>
                  <li>• I own or have proper licensing for the content I&apos;m uploading</li>
                  <li>• I will comply with all platform terms and community guidelines</li>
                  <li>• I understand the legal implications of AI-generated content</li>
                </ul>
              </div>
            </label>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700 flex gap-3 justify-end">
          <button
            onClick={onDecline}
            className="btn-secondary flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Cancel Upload
          </button>
          <button
            onClick={onAccept}
            disabled={!hasReadTerms}
            className="btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Check className="w-4 h-4" />
            Continue Upload
          </button>
        </div>
      </div>
    </div>
  )
} 