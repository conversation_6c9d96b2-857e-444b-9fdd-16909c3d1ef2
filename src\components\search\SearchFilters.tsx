'use client'

import React, { useState, useEffect } from 'react'
import { Filter, X, Calendar, Clock, SlidersHorizontal, ChevronDown } from 'lucide-react'
import { SearchFilters as SearchFiltersType } from '@/types/search'

interface SearchFiltersProps {
  filters: SearchFiltersType
  onFiltersChange: (filters: SearchFiltersType) => void
  onClearFilters: () => void
  className?: string
}

interface FilterOption {
  value: string
  label: string
  count?: number
}

export default function SearchFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  className = ""
}: SearchFiltersProps) {
  const [showFilters, setShowFilters] = useState(false)
  const [activeFiltersCount, setActiveFiltersCount] = useState(0)
  const [isMobile, setIsMobile] = useState(false)

  // AI Tools options - Updated with more options
  const aiToolOptions: FilterOption[] = [
    { value: 'Suno', label: 'Suno AI', count: 1250 },
    { value: 'Udio', label: 'Udio', count: 890 },
    { value: 'MusicGen', label: 'MusicGen', count: 650 },
    { value: 'AIVA', label: 'AIVA', count: 320 },
    { value: 'Amper', label: 'Amper Music', count: 180 },
    { value: 'Mubert', label: 'Mubert', count: 150 },
    { value: 'Soundraw', label: 'Soundraw', count: 120 },
    { value: 'Custom', label: 'Custom Models', count: 95 },
    { value: 'Other', label: 'Other AI Tools', count: 75 }
  ]

  // Genre options
  const genreOptions: FilterOption[] = [
    { value: 'electronic', label: 'Electronic', count: 2100 },
    { value: 'ambient', label: 'Ambient', count: 1800 },
    { value: 'classical', label: 'Classical', count: 1200 },
    { value: 'jazz', label: 'Jazz', count: 950 },
    { value: 'rock', label: 'Rock', count: 800 },
    { value: 'pop', label: 'Pop', count: 750 },
    { value: 'experimental', label: 'Experimental', count: 600 },
    { value: 'hip-hop', label: 'Hip Hop', count: 450 },
    { value: 'folk', label: 'Folk', count: 300 },
    { value: 'world', label: 'World Music', count: 250 }
  ]

  // Sort options - Enhanced with more options
  const sortOptions: FilterOption[] = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'created_at', label: 'Date Uploaded' },
    { value: 'title', label: 'Title (A-Z)' },
    { value: 'artist_name', label: 'Artist Name' },
    { value: 'duration', label: 'Duration' },
    { value: 'popularity', label: 'Popularity' },
    { value: 'play_count', label: 'Play Count' }
  ]

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Count active filters
  useEffect(() => {
    let count = 0
    if (filters.ai_tool && filters.ai_tool.length > 0) count++
    if (filters.genre && filters.genre.length > 0) count++
    if (filters.duration_min !== undefined || filters.duration_max !== undefined) count++
    if (filters.date_from || filters.date_to) count++
    if (filters.is_public !== undefined) count++
    if (filters.sort_by && filters.sort_by !== 'relevance') count++
    setActiveFiltersCount(count)
  }, [filters])

  const updateFilters = (newFilters: Partial<SearchFiltersType>) => {
    onFiltersChange({ ...filters, ...newFilters })
  }

  const toggleArrayFilter = (filterKey: 'ai_tool' | 'genre', value: string) => {
    const currentArray = filters[filterKey] || []
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    
    updateFilters({ [filterKey]: newArray.length > 0 ? newArray : undefined })
  }

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const parseDuration = (timeString: string): number => {
    const [minutes, seconds] = timeString.split(':').map(Number)
    return (minutes || 0) * 60 + (seconds || 0)
  }

  return (
    <div className={`relative ${className}`}>
      {/* Filter Toggle Button */}
      <button
        onClick={() => setShowFilters(!showFilters)}
        className="flex items-center space-x-2 bg-gray-800 hover:bg-gray-700 border border-gray-700 rounded-lg px-4 py-2 text-white transition-colors w-full sm:w-auto justify-center sm:justify-start"
      >
        <Filter className="w-4 h-4" />
        <span className="hidden sm:inline">Filters</span>
        <span className="sm:hidden">Filter</span>
        {activeFiltersCount > 0 && (
          <span className="bg-purple-600 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
            {activeFiltersCount}
          </span>
        )}
        <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
      </button>

      {/* Filters Panel */}
      {showFilters && (
        <>
          {/* Mobile Overlay */}
          {isMobile && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={() => setShowFilters(false)} />
          )}
          
          {/* Filter Panel */}
          <div className={`
            ${isMobile 
              ? 'fixed inset-x-4 top-20 bottom-4 bg-gray-900 rounded-lg shadow-2xl z-50 overflow-y-auto' 
              : 'absolute top-full right-0 mt-2 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 w-[600px] max-w-[90vw]'
            } p-6
          `}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                <SlidersHorizontal className="w-5 h-5" />
                <span>Search Filters</span>
              </h3>
              <div className="flex items-center space-x-2">
                {activeFiltersCount > 0 && (
                  <button
                    onClick={onClearFilters}
                    className="text-gray-400 hover:text-white text-sm flex items-center space-x-1 px-3 py-1 rounded-md hover:bg-gray-700 transition-colors"
                  >
                    <X className="w-4 h-4" />
                    <span>Clear All</span>
                  </button>
                )}
                <button
                  onClick={() => setShowFilters(false)}
                  className="text-gray-400 hover:text-white p-1 rounded-md hover:bg-gray-700 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className={`grid grid-cols-1 ${isMobile ? 'gap-6' : 'md:grid-cols-2 gap-6'}`}>
              {/* AI Tools Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  AI Tools ({filters.ai_tool?.length || 0} selected)
                </label>
                <div className="space-y-2 max-h-48 overflow-y-auto bg-gray-700/30 rounded-lg p-3">
                  {aiToolOptions.map((option) => (
                    <label key={option.value} className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded">
                      <input
                        type="checkbox"
                        checked={filters.ai_tool?.includes(option.value) || false}
                        onChange={() => toggleArrayFilter('ai_tool', option.value)}
                        className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                      />
                      <span className="text-gray-300 flex-1">{option.label}</span>
                      {option.count && (
                        <span className="text-xs text-gray-500 bg-gray-600 px-2 py-0.5 rounded">
                          {option.count}
                        </span>
                      )}
                    </label>
                  ))}
                </div>
              </div>

              {/* Genres Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Genres ({filters.genre?.length || 0} selected)
                </label>
                <div className="space-y-2 max-h-48 overflow-y-auto bg-gray-700/30 rounded-lg p-3">
                  {genreOptions.map((option) => (
                    <label key={option.value} className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded">
                      <input
                        type="checkbox"
                        checked={filters.genre?.includes(option.value) || false}
                        onChange={() => toggleArrayFilter('genre', option.value)}
                        className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                      />
                      <span className="text-gray-300 flex-1">{option.label}</span>
                      {option.count && (
                        <span className="text-xs text-gray-500 bg-gray-600 px-2 py-0.5 rounded">
                          {option.count}
                        </span>
                      )}
                    </label>
                  ))}
                </div>
              </div>

              {/* Duration Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>Duration</span>
                </label>
                <div className="space-y-3 bg-gray-700/30 rounded-lg p-3">
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Minimum Duration</label>
                    <input
                      type="time"
                      step="1"
                      value={filters.duration_min ? formatDuration(filters.duration_min) : ''}
                      onChange={(e) => {
                        const value = e.target.value
                        updateFilters({
                          duration_min: value ? parseDuration(value) : undefined
                        })
                      }}
                      className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Maximum Duration</label>
                    <input
                      type="time"
                      step="1"
                      value={filters.duration_max ? formatDuration(filters.duration_max) : ''}
                      onChange={(e) => {
                        const value = e.target.value
                        updateFilters({
                          duration_max: value ? parseDuration(value) : undefined
                        })
                      }}
                      className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                </div>
              </div>

              {/* Date Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3 flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>Upload Date Range</span>
                </label>
                <div className="space-y-3 bg-gray-700/30 rounded-lg p-3">
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">From Date</label>
                    <input
                      type="date"
                      value={filters.date_from || ''}
                      onChange={(e) => updateFilters({ date_from: e.target.value || undefined })}
                      className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">To Date</label>
                    <input
                      type="date"
                      value={filters.date_to || ''}
                      onChange={(e) => updateFilters({ date_to: e.target.value || undefined })}
                      className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                </div>
              </div>

              {/* Visibility Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Track Visibility
                </label>
                <div className="space-y-2 bg-gray-700/30 rounded-lg p-3">
                  <label className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded">
                    <input
                      type="radio"
                      name="visibility"
                      checked={filters.is_public === undefined}
                      onChange={() => updateFilters({ is_public: undefined })}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500"
                    />
                    <span className="text-gray-300">All Tracks</span>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded">
                    <input
                      type="radio"
                      name="visibility"
                      checked={filters.is_public === true}
                      onChange={() => updateFilters({ is_public: true })}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500"
                    />
                    <span className="text-gray-300">Public Only</span>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer hover:bg-gray-700/50 p-2 rounded">
                    <input
                      type="radio"
                      name="visibility"
                      checked={filters.is_public === false}
                      onChange={() => updateFilters({ is_public: false })}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500"
                    />
                    <span className="text-gray-300">Private Only</span>
                  </label>
                </div>
              </div>

              {/* Sort Options */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Sort & Order
                </label>
                <div className="space-y-3 bg-gray-700/30 rounded-lg p-3">
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Sort By</label>
                    <select
                      value={filters.sort_by || 'relevance'}
                      onChange={(e) => updateFilters({ sort_by: e.target.value as any })}
                      className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      {sortOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex space-x-2">
                    <label className="flex items-center space-x-2 cursor-pointer flex-1 hover:bg-gray-700/50 p-2 rounded">
                      <input
                        type="radio"
                        name="sort_order"
                        checked={filters.sort_order !== 'asc'}
                        onChange={() => updateFilters({ sort_order: 'desc' })}
                        className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="text-gray-300 text-sm">Descending</span>
                    </label>
                    <label className="flex items-center space-x-2 cursor-pointer flex-1 hover:bg-gray-700/50 p-2 rounded">
                      <input
                        type="radio"
                        name="sort_order"
                        checked={filters.sort_order === 'asc'}
                        onChange={() => updateFilters({ sort_order: 'asc' })}
                        className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500"
                      />
                      <span className="text-gray-300 text-sm">Ascending</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Apply/Close Buttons - Mobile optimized */}
            <div className={`flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-700 ${isMobile ? 'sticky bottom-0 bg-gray-900 -mx-6 -mb-6 px-6 pb-6' : ''}`}>
              <button
                onClick={() => setShowFilters(false)}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                {isMobile ? 'Cancel' : 'Close'}
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium"
              >
                {isMobile ? 'Apply Filters' : 'Apply'}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
} 