'use client'

import { useEffect, useState } from 'react'
import { Search, Users, TrendingUp, Music } from 'lucide-react'
import { searchPublicUsers, getPopularUsers } from '@/lib/social'
import type { Profile } from '@/types/database'
import { Avatar } from '@/components/ui/Avatar'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'
import Link from 'next/link'

export default function UsersDirectoryPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<Profile[]>([])
  const [popularUsers, setPopularUsers] = useState<Profile[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadPopularUsers()
  }, [])

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchQuery.trim()) {
        handleSearch(searchQuery)
      } else {
        setSearchResults([])
      }
    }, 300)

    return () => clearTimeout(delayedSearch)
  }, [searchQuery])

  async function loadPopularUsers() {
    try {
      setIsLoading(true)
      const { data } = await getPopularUsers(0, 50)
      setPopularUsers(data || [])
    } catch (error) {
      console.error('Error loading popular users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  async function handleSearch(query: string) {
    if (!query.trim()) return

    try {
      setIsSearching(true)
      const { data } = await searchPublicUsers(query)
      setSearchResults(data || [])
    } catch (error) {
      console.error('Error searching users:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const displayUsers = searchQuery.trim() ? searchResults : popularUsers

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Discover Users
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Find and connect with music creators from around the world
          </p>
        </div>

        {/* Search */}
        <Card className="bg-gray-800/50 backdrop-blur-sm border-gray-700 p-6 mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder="Search users by name or username..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-gray-700/50 border-gray-600 text-white placeholder-gray-400"
            />
          </div>
        </Card>

        {/* Content */}
        <Tabs defaultValue="grid" className="space-y-6">
          <div className="flex items-center justify-between">
            <TabsList className="bg-gray-800/50 border-gray-700">
              <TabsTrigger value="grid" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Grid View
              </TabsTrigger>
              <TabsTrigger value="list" className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                List View
              </TabsTrigger>
            </TabsList>

            <div className="text-gray-400 text-sm">
              {searchQuery.trim() ? (
                <>Found {searchResults.length} users</>
              ) : (
                <>Showing {popularUsers.length} popular users</>
              )}
            </div>
          </div>

          <TabsContent value="grid" className="space-y-6">
            {isLoading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {Array.from({ length: 8 }).map((_, i) => (
                  <Card key={i} className="bg-gray-800/50 border-gray-700 p-6 animate-pulse">
                    <div className="w-16 h-16 bg-gray-600 rounded-full mx-auto mb-4"></div>
                    <div className="h-4 bg-gray-600 rounded w-3/4 mx-auto mb-2"></div>
                    <div className="h-3 bg-gray-600 rounded w-1/2 mx-auto"></div>
                  </Card>
                ))}
              </div>
            ) : displayUsers.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {displayUsers.map((user) => (
                  <UserGridCard key={user.id} user={user} />
                ))}
              </div>
            ) : (
              <Card className="bg-gray-800/50 border-gray-700 p-12 text-center">
                <Users className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  {searchQuery.trim() ? 'No users found' : 'No users available'}
                </h3>
                <p className="text-gray-400">
                  {searchQuery.trim() 
                    ? 'Try adjusting your search terms'
                    : 'Be the first to create a public profile!'
                  }
                </p>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="list" className="space-y-4">
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 10 }).map((_, i) => (
                  <Card key={i} className="bg-gray-800/50 border-gray-700 p-4 animate-pulse">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gray-600 rounded-full"></div>
                      <div className="flex-1">
                        <div className="h-4 bg-gray-600 rounded w-1/3 mb-2"></div>
                        <div className="h-3 bg-gray-600 rounded w-1/2"></div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : displayUsers.length > 0 ? (
              <div className="space-y-3">
                {displayUsers.map((user) => (
                  <UserListCard key={user.id} user={user} />
                ))}
              </div>
            ) : (
              <Card className="bg-gray-800/50 border-gray-700 p-12 text-center">
                <Users className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  {searchQuery.trim() ? 'No users found' : 'No users available'}
                </h3>
                <p className="text-gray-400">
                  {searchQuery.trim() 
                    ? 'Try adjusting your search terms'
                    : 'Be the first to create a public profile!'
                  }
                </p>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function UserGridCard({ user }: { user: Profile }) {
  return (
    <Link href={`/users/${user.username}`}>
      <Card className="bg-gray-800/50 border-gray-700 p-6 text-center hover:bg-gray-700/50 transition-colors cursor-pointer">
        <Avatar
          src={user.avatar_url}
          alt={user.full_name || user.username || ''}
          size="lg"
          className="mx-auto mb-4"
        />
        
        <h3 className="font-semibold text-white mb-1">
          {user.full_name || user.username}
        </h3>
        
        {user.username && user.full_name && (
          <p className="text-gray-400 text-sm mb-2">@{user.username}</p>
        )}
        
        {user.bio && (
          <p className="text-gray-500 text-sm mb-3 line-clamp-2">{user.bio}</p>
        )}
        
        <div className="flex items-center justify-center gap-4 text-sm text-gray-400">
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            {user.followers_count}
          </div>
          <div className="flex items-center gap-1">
            <Music className="w-4 h-4" />
            {user.total_tracks_generated || 0}
          </div>
        </div>
      </Card>
    </Link>
  )
}

function UserListCard({ user }: { user: Profile }) {
  return (
    <Link href={`/users/${user.username}`}>
      <Card className="bg-gray-800/50 border-gray-700 p-4 hover:bg-gray-700/50 transition-colors cursor-pointer">
        <div className="flex items-center gap-4">
          <Avatar
            src={user.avatar_url}
            alt={user.full_name || user.username || ''}
            size="md"
          />
          
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-white">
                {user.full_name || user.username}
              </h3>
              {user.username && user.full_name && (
                <span className="text-gray-400 text-sm">@{user.username}</span>
              )}
            </div>
            
            {user.bio && (
              <p className="text-gray-500 text-sm mb-2 line-clamp-1">{user.bio}</p>
            )}
            
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                {user.followers_count} followers
              </div>
              <div className="flex items-center gap-1">
                <Music className="w-4 h-4" />
                {user.total_tracks_generated || 0} tracks
              </div>
            </div>
          </div>
          
          <Button variant="outline" size="sm">
            View Profile
          </Button>
        </div>
      </Card>
    </Link>
  )
} 