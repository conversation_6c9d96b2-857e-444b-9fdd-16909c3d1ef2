// Tunami AI Music Platform Database Types
// Generated TypeScript types for Supabase database schema

export type AIToolType = 
  | 'suno'
  | 'udio'
  | 'mubert'
  | 'soundraw'
  | 'aiva'
  | 'amper'
  | 'jukedeck'
  | 'endlesss'
  | 'custom'
  | 'other'

export type GenreType = 
  | 'pop'
  | 'rock'
  | 'hip_hop'
  | 'electronic'
  | 'jazz'
  | 'classical'
  | 'country'
  | 'folk'
  | 'r_and_b'
  | 'reggae'
  | 'blues'
  | 'metal'
  | 'punk'
  | 'indie'
  | 'ambient'
  | 'experimental'
  | 'world'
  | 'other'

export type MoodType = 
  | 'happy'
  | 'sad'
  | 'energetic'
  | 'calm'
  | 'aggressive'
  | 'romantic'
  | 'mysterious'
  | 'epic'
  | 'chill'
  | 'uplifting'
  | 'dark'
  | 'nostalgic'
  | 'dreamy'
  | 'intense'
  | 'playful'
  | 'serious'

export type AgreementVersionType = 'v1.0' | 'v1.1' | 'v2.0' | 'v2.1'

// New social features types
export type ActivityType = 
  | 'track_upload'
  | 'playlist_create'
  | 'playlist_update'
  | 'track_like'
  | 'user_follow'
  | 'track_mention'
  | 'playlist_share'

export interface Profile {
  id: string
  email: string
  full_name: string | null
  username: string | null
  avatar_url: string | null
  bio: string | null
  is_premium: boolean
  subscription_tier: string
  ai_generation_credits: number
  total_tracks_generated: number
  // New social fields
  is_public: boolean
  followers_count: number
  following_count: number
  social_links: Record<string, string> | null
  created_at: string
  updated_at: string
}

export interface Track {
  id: string
  title: string
  artist_name: string
  file_url: string | null
  file_path: string | null
  file_size: number | null
  ai_tool: AIToolType
  duration: number | null
  genre: GenreType | null
  mood: MoodType | null
  tempo: number | null
  key_signature: string | null
  ai_prompt: string | null
  ai_parameters: Record<string, any> | null
  is_public: boolean
  is_featured: boolean
  play_count: number
  like_count: number
  upload_status: string
  uploaded_by: string
  // New social fields
  description: string | null
  mentions_count: number
  created_at: string
  updated_at: string
}

export interface Playlist {
  id: string
  name: string
  description: string | null
  cover_image_url: string | null
  user_id: string
  is_public: boolean
  is_collaborative: boolean
  track_count: number
  total_duration: number
  created_at: string
  updated_at: string
}

// New social features interfaces
export interface UserFollow {
  id: string
  follower_id: string
  following_id: string
  created_at: string
}

export interface ActivityFeed {
  id: string
  user_id: string
  activity_type: ActivityType
  target_user_id: string | null
  track_id: string | null
  playlist_id: string | null
  metadata: Record<string, any> | null
  is_public: boolean
  created_at: string
}

export interface TrackMention {
  id: string
  track_id: string
  mentioned_user_id: string
  mentioned_by: string
  position: number | null
  created_at: string
}

export interface ShareLink {
  id: string
  share_code: string
  user_id: string
  track_id: string | null
  playlist_id: string | null
  access_count: number
  expires_at: string | null
  is_active: boolean
  created_at: string
}

export interface PlaylistTrack {
  id: string
  playlist_id: string
  track_id: string
  position: number
  added_by: string | null
  added_at: string
}

export interface UserAgreement {
  id: string
  user_id: string
  agreement_type: string
  agreement_version: AgreementVersionType
  accepted_at: string
  ip_address: string | null
  user_agent: string | null
}

export interface TrackLike {
  id: string
  user_id: string
  track_id: string
  created_at: string
}

export interface ListeningHistory {
  id: string
  user_id: string
  track_id: string
  listened_at: string
  duration_listened: number | null
  completed: boolean
  source: string | null
  playlist_id: string | null
}

export interface AIGenerationJob {
  id: string
  user_id: string
  ai_tool: AIToolType
  prompt: string
  parameters: Record<string, any> | null
  status: string
  progress: number
  result_track_id: string | null
  error_message: string | null
  credits_used: number
  processing_time: number | null
  created_at: string
  completed_at: string | null
}

// Enhanced view types with social features
export interface PopularTrack extends Track {
  uploader_username: string | null
  uploader_name: string | null
  uploader_avatar_url: string | null
}

export interface UserPlaylistWithStats extends Playlist {
  username: string | null
  full_name: string | null
  avatar_url: string | null
  actual_track_count: number
}

export interface PublicProfile extends Profile {
  is_following?: boolean
  is_followed_by?: boolean
}

export interface ActivityWithDetails extends ActivityFeed {
  user_name: string | null
  user_username: string | null
  user_avatar_url: string | null
  target_user_name: string | null
  target_user_username: string | null
  track_title: string | null
  playlist_name: string | null
}

// Database interface for Supabase client
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: Profile
        Insert: Omit<Profile, 'id' | 'created_at' | 'updated_at'> & {
          id: string
        }
        Update: Partial<Omit<Profile, 'id' | 'created_at' | 'updated_at'>>
      }
      tracks: {
        Row: Track
        Insert: Omit<Track, 'id' | 'created_at' | 'updated_at' | 'play_count' | 'like_count' | 'mentions_count'>
        Update: Partial<Omit<Track, 'id' | 'created_at' | 'updated_at'>>
      }
      playlists: {
        Row: Playlist
        Insert: Omit<Playlist, 'id' | 'created_at' | 'updated_at' | 'track_count' | 'total_duration'>
        Update: Partial<Omit<Playlist, 'id' | 'created_at' | 'updated_at'>>
      }
      playlist_tracks: {
        Row: PlaylistTrack
        Insert: Omit<PlaylistTrack, 'id' | 'added_at'>
        Update: Partial<Omit<PlaylistTrack, 'id' | 'added_at'>>
      }
      user_follows: {
        Row: UserFollow
        Insert: Omit<UserFollow, 'id' | 'created_at'>
        Update: Partial<Omit<UserFollow, 'id' | 'created_at'>>
      }
      activity_feed: {
        Row: ActivityFeed
        Insert: Omit<ActivityFeed, 'id' | 'created_at'>
        Update: Partial<Omit<ActivityFeed, 'id' | 'created_at'>>
      }
      track_mentions: {
        Row: TrackMention
        Insert: Omit<TrackMention, 'id' | 'created_at'>
        Update: Partial<Omit<TrackMention, 'id' | 'created_at'>>
      }
      share_links: {
        Row: ShareLink
        Insert: Omit<ShareLink, 'id' | 'created_at' | 'access_count'>
        Update: Partial<Omit<ShareLink, 'id' | 'created_at'>>
      }
      user_agreements: {
        Row: UserAgreement
        Insert: Omit<UserAgreement, 'id' | 'accepted_at'>
        Update: Partial<Omit<UserAgreement, 'id' | 'accepted_at'>>
      }
      track_likes: {
        Row: TrackLike
        Insert: Omit<TrackLike, 'id' | 'created_at'>
        Update: Partial<Omit<TrackLike, 'id' | 'created_at'>>
      }
      listening_history: {
        Row: ListeningHistory
        Insert: Omit<ListeningHistory, 'id' | 'listened_at'>
        Update: Partial<Omit<ListeningHistory, 'id' | 'listened_at'>>
      }
      ai_generation_jobs: {
        Row: AIGenerationJob
        Insert: Omit<AIGenerationJob, 'id' | 'created_at' | 'completed_at'>
        Update: Partial<Omit<AIGenerationJob, 'id' | 'created_at'>>
      }
    }
    Views: {
      popular_tracks: {
        Row: PopularTrack
      }
      user_playlists_with_stats: {
        Row: UserPlaylistWithStats
      }
    }
    Functions: {
      [_: string]: never
    }
    Enums: {
      ai_tool_type: AIToolType
      genre_type: GenreType
      mood_type: MoodType
      agreement_version_type: AgreementVersionType
    }
  }
}

// Utility types for common operations
export type CreateTrack = Database['public']['Tables']['tracks']['Insert']
export type UpdateTrack = Database['public']['Tables']['tracks']['Update']
export type CreatePlaylist = Database['public']['Tables']['playlists']['Insert']
export type UpdatePlaylist = Database['public']['Tables']['playlists']['Update']
export type CreateProfile = Database['public']['Tables']['profiles']['Insert']
export type UpdateProfile = Database['public']['Tables']['profiles']['Update']

 