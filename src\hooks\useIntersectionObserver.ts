import { useEffect, useState, RefObject } from 'react'

interface UseIntersectionObserverOptions {
  root?: Element | null
  rootMargin?: string
  threshold?: number | number[]
  triggerOnce?: boolean
  skip?: boolean
  initialIsIntersecting?: boolean
}

export function useIntersectionObserver(
  elementRef: RefObject<Element>,
  options: UseIntersectionObserverOptions = {}
): IntersectionObserverEntry | null {
  const {
    root = null,
    rootMargin = '0px',
    threshold = 0,
    triggerOnce = false,
    skip = false,
    initialIsIntersecting = false
  } = options

  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const [hasTriggered, setHasTriggered] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    
    if (!element || skip || (triggerOnce && hasTriggered)) {
      return
    }

    // Check if IntersectionObserver is supported
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      // Fallback for unsupported browsers
      setEntry({
        isIntersecting: initialIsIntersecting,
        target: element,
        intersectionRatio: initialIsIntersecting ? 1 : 0,
        time: Date.now(),
        rootBounds: null,
        boundingClientRect: element.getBoundingClientRect(),
        intersectionRect: initialIsIntersecting ? element.getBoundingClientRect() : new DOMRect()
      } as IntersectionObserverEntry)
      return
    }

    const observer = new IntersectionObserver(
      ([entry]: IntersectionObserverEntry[]) => {
        setEntry(entry)
        
        if (entry.isIntersecting && triggerOnce) {
          setHasTriggered(true)
        }
      },
      {
        root,
        rootMargin,
        threshold
      }
    )

    observer.observe(element)

    return () => {
      observer.disconnect()
    }
  }, [
    elementRef,
    root,
    rootMargin,
    threshold,
    triggerOnce,
    skip,
    hasTriggered,
    initialIsIntersecting
  ])

  return entry
}

// Hook for multiple elements
export function useIntersectionObserverMultiple(
  elementRefs: RefObject<Element>[],
  options: UseIntersectionObserverOptions = {}
): Map<Element, IntersectionObserverEntry> {
  const [entries, setEntries] = useState<Map<Element, IntersectionObserverEntry>>(new Map())
  const {
    root = null,
    rootMargin = '0px',
    threshold = 0,
    skip = false
  } = options

  useEffect(() => {
    if (skip) return

    const elements = elementRefs.map(ref => ref.current).filter(Boolean) as Element[]
    
    if (elements.length === 0) return

    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return
    }

    const observer = new IntersectionObserver(
      (observerEntries: IntersectionObserverEntry[]) => {
        setEntries(prev => {
          const newEntries = new Map(prev)
          observerEntries.forEach(entry => {
            newEntries.set(entry.target, entry)
          })
          return newEntries
        })
      },
      {
        root,
        rootMargin,
        threshold
      }
    )

    elements.forEach(element => observer.observe(element))

    return () => {
      observer.disconnect()
    }
  }, [elementRefs, root, rootMargin, threshold, skip])

  return entries
}

// Hook for lazy loading with visibility percentage
export function useVisibilityPercentage(
  elementRef: RefObject<Element>,
  options: Omit<UseIntersectionObserverOptions, 'threshold'> = {}
): number {
  const [visibilityPercentage, setVisibilityPercentage] = useState(0)

  const entry = useIntersectionObserver(elementRef, {
    ...options,
    threshold: Array.from({ length: 101 }, (_, i) => i / 100) // 0% to 100% in 1% increments
  })

  useEffect(() => {
    if (entry) {
      setVisibilityPercentage(Math.round(entry.intersectionRatio * 100))
    }
  }, [entry])

  return visibilityPercentage
}

// Hook for scroll-based animations
export function useScrollAnimation(
  elementRef: RefObject<Element>,
  animationOptions: {
    triggerOnce?: boolean
    threshold?: number
    rootMargin?: string
    onEnter?: () => void
    onExit?: () => void
  } = {}
) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasAnimated, setHasAnimated] = useState(false)

  const {
    triggerOnce = true,
    threshold = 0.1,
    rootMargin = '0px',
    onEnter,
    onExit
  } = animationOptions

  const entry = useIntersectionObserver(elementRef, {
    threshold,
    rootMargin,
    triggerOnce
  })

  useEffect(() => {
    if (entry) {
      const wasVisible = isVisible
      const nowVisible = entry.isIntersecting

      setIsVisible(nowVisible)

      if (!wasVisible && nowVisible) {
        // Entering
        onEnter?.()
        if (triggerOnce) {
          setHasAnimated(true)
        }
      } else if (wasVisible && !nowVisible && !triggerOnce) {
        // Exiting
        onExit?.()
      }
    }
  }, [entry, isVisible, onEnter, onExit, triggerOnce])

  return {
    isVisible,
    hasAnimated,
    entry
  }
} 