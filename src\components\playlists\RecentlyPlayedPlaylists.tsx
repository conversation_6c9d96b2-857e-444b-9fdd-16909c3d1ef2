'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Play, Music, Clock, Users } from 'lucide-react'
import { useAudio } from '@/contexts/AudioContext'
import { usePlaylist } from '@/contexts/PlaylistContext'
import { Playlist } from '@/types/playlist'
import { formatDate } from '@/utils/format'

interface RecentlyPlayedPlaylistsProps {
  limit?: number
  className?: string
}

export default function RecentlyPlayedPlaylists({ 
  limit = 6, 
  className = '' 
}: RecentlyPlayedPlaylistsProps) {
  const { playPlaylist, currentPlaylist: audioCurrentPlaylist, isPlaying } = useAudio()
  const { getUserPlaylists, playlists, loading } = usePlaylist()
  const [recentPlaylists, setRecentPlaylists] = useState<Playlist[]>([])

  useEffect(() => {
    getUserPlaylists()
  }, [getUserPlaylists])

  useEffect(() => {
    // For now, we'll show the most recently updated playlists
    // In a real app, you'd track actual play history
    const sortedPlaylists = [...playlists]
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, limit)
    
    setRecentPlaylists(sortedPlaylists)
  }, [playlists, limit])

  const handlePlayPlaylist = (playlist: Playlist) => {
    // Convert to PlaylistWithTracks format (simplified for demo)
    const playlistWithTracks = {
      ...playlist,
      tracks: [] // In real app, you'd fetch the tracks
    }
    playPlaylist(playlistWithTracks, 0)
  }

  const isCurrentlyPlaying = (playlistId: string) => {
    return audioCurrentPlaylist?.id === playlistId && isPlaying
  }

  if (loading) {
    return (
      <div className={`${className}`}>
        <h2 className="text-xl font-bold text-white mb-6">Recently Played</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: limit }).map((_, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-4 animate-pulse">
              <div className="w-full aspect-square bg-gray-700 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-700 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (recentPlaylists.length === 0) {
    return (
      <div className={`${className}`}>
        <h2 className="text-xl font-bold text-white mb-6">Recently Played</h2>
        <div className="text-center py-8">
          <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-400">No recently played playlists</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">Recently Played</h2>
        <Link 
          href="/playlists" 
          className="text-purple-400 hover:text-purple-300 text-sm transition-colors"
        >
          View all
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {recentPlaylists.map((playlist) => {
          const isPlaying = isCurrentlyPlaying(playlist.id)
          
          return (
            <div
              key={playlist.id}
              className="group bg-gray-800 hover:bg-gray-750 rounded-lg p-4 transition-all duration-200 border border-gray-700 hover:border-gray-600"
            >
              {/* Playlist Cover */}
              <div className="relative mb-4">
                <div className="w-full aspect-square bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center relative overflow-hidden">
                  {playlist.cover_image_url ? (
                    <img
                      src={playlist.cover_image_url}
                      alt={playlist.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Music className="w-12 h-12 text-white opacity-80" />
                  )}
                  
                  {isPlaying && (
                    <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                      <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                    </div>
                  )}
                </div>

                {/* Play Button Overlay */}
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                  <button
                    onClick={() => handlePlayPlaylist(playlist)}
                    className="w-12 h-12 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-all duration-200 transform hover:scale-105"
                  >
                    <Play className="w-5 h-5 text-white ml-0.5" />
                  </button>
                </div>
              </div>

              {/* Playlist Info */}
              <div className="space-y-2">
                <Link 
                  href={`/playlist/${playlist.id}`}
                  className="block"
                >
                  <h3 className="font-semibold text-white truncate group-hover:text-purple-300 transition-colors">
                    {playlist.name}
                  </h3>
                </Link>
                
                {playlist.description && (
                  <p className="text-gray-400 text-sm line-clamp-2">
                    {playlist.description}
                  </p>
                )}

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <Music className="w-3 h-3" />
                      {playlist.track_count || 0}
                    </div>
                    {playlist.is_public && (
                      <div className="flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        Public
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {formatDate(playlist.updated_at)}
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
} 