'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Music, CheckCircle, XCircle } from 'lucide-react'

export default function AuthCallbackPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')
  const router = useRouter()

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the URL hash parameters
        const hashParams = new URLSearchParams(window.location.hash.substring(1))
        const accessToken = hashParams.get('access_token')
        const refreshToken = hashParams.get('refresh_token')
        const error = hashParams.get('error')
        const errorDescription = hashParams.get('error_description')

        // Check for OAuth errors
        if (error) {
          console.error('OAuth error:', error, errorDescription)
          setStatus('error')
          setMessage(errorDescription || 'Authentication failed')
          
          // Redirect to login after showing error
          setTimeout(() => {
            router.replace('/auth/login')
          }, 3000)
          return
        }

        // Handle the authentication callback
        const { data, error: authError } = await supabase.auth.getSession()
        
        if (authError) {
          console.error('Session error:', authError)
          setStatus('error')
          setMessage('Failed to establish session')
          
          setTimeout(() => {
            router.replace('/auth/login')
          }, 3000)
          return
        }

        if (data.session) {
          setStatus('success')
          setMessage('Authentication successful! Redirecting...')
          
          // Redirect to dashboard after successful authentication
          setTimeout(() => {
            router.replace('/dashboard')
          }, 1500)
        } else {
          setStatus('error')
          setMessage('No session found')
          
          setTimeout(() => {
            router.replace('/auth/login')
          }, 3000)
        }
      } catch (err) {
        console.error('Callback error:', err)
        setStatus('error')
        setMessage('An unexpected error occurred')
        
        setTimeout(() => {
          router.replace('/auth/login')
        }, 3000)
      }
    }

    handleAuthCallback()
  }, [router])

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="glass-dark p-8 rounded-2xl text-center">
          {/* Header */}
          <div className="flex justify-center mb-6">
            <Music className="w-12 h-12 text-primary-400" />
          </div>
          
          <h1 className="text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-6">
            Tunami
          </h1>

          {/* Status Content */}
          {status === 'loading' && (
            <div className="space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-400 mx-auto"></div>
              <p className="text-gray-300">Completing authentication...</p>
            </div>
          )}

          {status === 'success' && (
            <div className="space-y-4">
              <CheckCircle className="w-12 h-12 text-green-400 mx-auto" />
              <p className="text-green-400 font-medium">{message}</p>
            </div>
          )}

          {status === 'error' && (
            <div className="space-y-4">
              <XCircle className="w-12 h-12 text-red-400 mx-auto" />
              <p className="text-red-400 font-medium">{message}</p>
              <p className="text-gray-400 text-sm">Redirecting to login...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 