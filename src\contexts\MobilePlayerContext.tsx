'use client'

import React, { createContext, useContext, useState, useCallback, useRef } from 'react'
import { AudioTrack } from '@/types/audio'

interface MobilePlayerState {
  currentTrack?: AudioTrack
  playlist: AudioTrack[]
  currentIndex: number
  isPlaying: boolean
  isExpanded: boolean
  isVisible: boolean
  volume: number
  isMuted: boolean
  shuffleMode: boolean
  repeatMode: 'none' | 'all' | 'one'
  isLoading: boolean
}

interface MobilePlayerActions {
  setCurrentTrack: (track: AudioTrack, playlist?: AudioTrack[], index?: number) => void
  playTrack: (track: AudioTrack) => void
  pauseTrack: () => void
  nextTrack: () => void
  previousTrack: () => void
  toggleExpanded: () => void
  toggleShuffle: () => void
  toggleRepeat: () => void
  setVolume: (volume: number) => void
  toggleMute: () => void
  showPlayer: () => void
  hidePlayer: () => void
  addToQueue: (track: AudioTrack) => void
  removeFromQueue: (index: number) => void
  clearQueue: () => void
}

interface MobilePlayerContextType extends MobilePlayerState, MobilePlayerActions {}

const MobilePlayerContext = createContext<MobilePlayerContextType | undefined>(undefined)

export function MobilePlayerProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<MobilePlayerState>({
    currentTrack: undefined,
    playlist: [],
    currentIndex: 0,
    isPlaying: false,
    isExpanded: false,
    isVisible: false,
    volume: 1,
    isMuted: false,
    shuffleMode: false,
    repeatMode: 'none',
    isLoading: false
  })

  const shuffledPlaylistRef = useRef<number[]>([])

  // Generate shuffled indices
  const generateShuffledPlaylist = useCallback((length: number, currentIndex: number) => {
    const indices = Array.from({ length }, (_, i) => i).filter(i => i !== currentIndex)
    
    // Fisher-Yates shuffle
    for (let i = indices.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[indices[i], indices[j]] = [indices[j], indices[i]]
    }
    
    // Insert current index at the beginning
    return [currentIndex, ...indices]
  }, [])

  // Get next track index based on shuffle and repeat modes
  const getNextIndex = useCallback(() => {
    const { playlist, currentIndex, shuffleMode, repeatMode } = state
    
    if (playlist.length === 0) return 0
    if (playlist.length === 1) return repeatMode === 'none' ? -1 : 0

    if (shuffleMode) {
      if (shuffledPlaylistRef.current.length === 0) {
        shuffledPlaylistRef.current = generateShuffledPlaylist(playlist.length, currentIndex)
      }
      
      const currentShuffleIndex = shuffledPlaylistRef.current.indexOf(currentIndex)
      const nextShuffleIndex = currentShuffleIndex + 1
      
      if (nextShuffleIndex >= shuffledPlaylistRef.current.length) {
        if (repeatMode === 'all') {
          return shuffledPlaylistRef.current[0]
        }
        return -1 // End of playlist
      }
      
      return shuffledPlaylistRef.current[nextShuffleIndex]
    } else {
      // Sequential playback
      if (repeatMode === 'one') {
        return currentIndex
      }
      
      const nextIndex = currentIndex + 1
      if (nextIndex >= playlist.length) {
        return repeatMode === 'all' ? 0 : -1
      }
      
      return nextIndex
    }
  }, [state, generateShuffledPlaylist])

  // Get previous track index
  const getPreviousIndex = useCallback(() => {
    const { playlist, currentIndex, shuffleMode } = state
    
    if (playlist.length === 0) return 0
    if (playlist.length === 1) return 0

    if (shuffleMode) {
      if (shuffledPlaylistRef.current.length === 0) {
        shuffledPlaylistRef.current = generateShuffledPlaylist(playlist.length, currentIndex)
      }
      
      const currentShuffleIndex = shuffledPlaylistRef.current.indexOf(currentIndex)
      const prevShuffleIndex = currentShuffleIndex - 1
      
      if (prevShuffleIndex < 0) {
        return shuffledPlaylistRef.current[shuffledPlaylistRef.current.length - 1]
      }
      
      return shuffledPlaylistRef.current[prevShuffleIndex]
    } else {
      // Sequential playback
      const prevIndex = currentIndex - 1
      return prevIndex < 0 ? playlist.length - 1 : prevIndex
    }
  }, [state, generateShuffledPlaylist])

  // Actions
  const setCurrentTrack = useCallback((track: AudioTrack, playlist?: AudioTrack[], index?: number) => {
    setState(prev => ({
      ...prev,
      currentTrack: track,
      playlist: playlist || prev.playlist,
      currentIndex: index !== undefined ? index : prev.currentIndex,
      isVisible: true,
      isLoading: false
    }))

    // Reset shuffle playlist when new playlist is set
    if (playlist) {
      shuffledPlaylistRef.current = []
    }
  }, [])

  const playTrack = useCallback((track: AudioTrack) => {
    setState(prev => ({
      ...prev,
      currentTrack: track,
      isPlaying: true,
      isVisible: true,
      isLoading: false
    }))
  }, [])

  const pauseTrack = useCallback(() => {
    setState(prev => ({
      ...prev,
      isPlaying: false
    }))
  }, [])

  const nextTrack = useCallback(() => {
    const nextIndex = getNextIndex()
    
    if (nextIndex === -1) {
      // End of playlist
      setState(prev => ({ ...prev, isPlaying: false }))
      return
    }

    const nextTrack = state.playlist[nextIndex]
    if (nextTrack) {
      setState(prev => ({
        ...prev,
        currentTrack: nextTrack,
        currentIndex: nextIndex,
        isLoading: true
      }))
    }
  }, [getNextIndex, state.playlist])

  const previousTrack = useCallback(() => {
    const prevIndex = getPreviousIndex()
    const prevTrack = state.playlist[prevIndex]
    
    if (prevTrack) {
      setState(prev => ({
        ...prev,
        currentTrack: prevTrack,
        currentIndex: prevIndex,
        isLoading: true
      }))
    }
  }, [getPreviousIndex, state.playlist])

  const toggleExpanded = useCallback(() => {
    setState(prev => ({
      ...prev,
      isExpanded: !prev.isExpanded
    }))
  }, [])

  const toggleShuffle = useCallback(() => {
    setState(prev => ({
      ...prev,
      shuffleMode: !prev.shuffleMode
    }))
    
    // Reset shuffled playlist
    shuffledPlaylistRef.current = []
  }, [])

  const toggleRepeat = useCallback(() => {
    setState(prev => ({
      ...prev,
      repeatMode: prev.repeatMode === 'none' ? 'all' : 
                  prev.repeatMode === 'all' ? 'one' : 'none'
    }))
  }, [])

  const setVolume = useCallback((volume: number) => {
    setState(prev => ({
      ...prev,
      volume: Math.max(0, Math.min(1, volume)),
      isMuted: volume === 0
    }))
  }, [])

  const toggleMute = useCallback(() => {
    setState(prev => ({
      ...prev,
      isMuted: !prev.isMuted
    }))
  }, [])

  const showPlayer = useCallback(() => {
    setState(prev => ({
      ...prev,
      isVisible: true
    }))
  }, [])

  const hidePlayer = useCallback(() => {
    setState(prev => ({
      ...prev,
      isVisible: false,
      isExpanded: false,
      isPlaying: false
    }))
  }, [])

  const addToQueue = useCallback((track: AudioTrack) => {
    setState(prev => ({
      ...prev,
      playlist: [...prev.playlist, track]
    }))
  }, [])

  const removeFromQueue = useCallback((index: number) => {
    setState(prev => {
      const newPlaylist = prev.playlist.filter((_, i) => i !== index)
      let newCurrentIndex = prev.currentIndex
      
      if (index < prev.currentIndex) {
        newCurrentIndex = prev.currentIndex - 1
      } else if (index === prev.currentIndex) {
        // Current track is being removed
        if (newPlaylist.length === 0) {
          return {
            ...prev,
            playlist: [],
            currentTrack: undefined,
            currentIndex: 0,
            isVisible: false,
            isPlaying: false
          }
        }
        // Keep the same index if possible, otherwise go to previous
        newCurrentIndex = Math.min(prev.currentIndex, newPlaylist.length - 1)
      }
      
      return {
        ...prev,
        playlist: newPlaylist,
        currentIndex: newCurrentIndex,
        currentTrack: newPlaylist[newCurrentIndex]
      }
    })
  }, [])

  const clearQueue = useCallback(() => {
    setState(prev => ({
      ...prev,
      playlist: [],
      currentTrack: undefined,
      currentIndex: 0,
      isVisible: false,
      isExpanded: false,
      isPlaying: false
    }))
  }, [])

  const contextValue: MobilePlayerContextType = {
    ...state,
    setCurrentTrack,
    playTrack,
    pauseTrack,
    nextTrack,
    previousTrack,
    toggleExpanded,
    toggleShuffle,
    toggleRepeat,
    setVolume,
    toggleMute,
    showPlayer,
    hidePlayer,
    addToQueue,
    removeFromQueue,
    clearQueue
  }

  return (
    <MobilePlayerContext.Provider value={contextValue}>
      {children}
    </MobilePlayerContext.Provider>
  )
}

export function useMobilePlayer() {
  const context = useContext(MobilePlayerContext)
  if (context === undefined) {
    throw new Error('useMobilePlayer must be used within a MobilePlayerProvider')
  }
  return context
} 