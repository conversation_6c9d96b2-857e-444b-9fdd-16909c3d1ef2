'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { User } from '@supabase/supabase-js'
import { Upload, CheckCircle, AlertCircle, ArrowRight, ArrowLeft, Music, Sparkles, Clock, X } from 'lucide-react'
import MainLayout from '@/components/layout/MainLayout'
import FileUpload from '@/components/upload/FileUpload'
import UploadHistory from '@/components/upload/UploadHistory'
import TermsReminder from '@/components/upload/TermsReminder'
import { auth } from '@/lib/supabase'
import { UploadFile, UploadState, UploadStep } from '@/types/upload'
import { PageLoading } from '@/components/LoadingStates'
import { PageErrorBoundary } from '@/components/error/ErrorBoundary'
import UploadForm, { UploadMetadata } from '@/components/upload/UploadForm'
import { uploadService, UploadProgress, UploadResult } from '@/lib/upload-service'

const initialMetadata: UploadMetadata = {
  title: '',
  artist: '',
  aiTool: '',
  genre: '',
  description: '',
  tags: [],
  isPublic: true
}

export default function UploadPage() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [state, setState] = useState<UploadState>({
    files: [],
    metadata: initialMetadata,
    currentStep: 'files',
    uploadProgress: {},
    uploadResults: [],
    isSubmitting: false,
    error: null,
    hasAcceptedTerms: false,
    showTermsReminder: false
  })

  const checkAuth = useCallback(async () => {
    const { user, error } = await auth.getCurrentUser()
    
    if (error || !user) {
      router.push('/auth/login')
      return
    }
    
    setUser(user)
    setLoading(false)
  }, [router])

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  const handleFilesSelected = (newFiles: UploadFile[]) => {
    setState(prev => ({
      ...prev,
      files: [...prev.files, ...newFiles],
      error: null
    }))
  }

  const handleFileRemove = (fileId: string) => {
    setState(prev => ({
      ...prev,
      files: prev.files.filter(f => f.id !== fileId),
      uploadProgress: Object.fromEntries(
        Object.entries(prev.uploadProgress).filter(([id]) => id !== fileId)
      )
    }))
  }

  const handleMetadataChange = (metadata: UploadMetadata) => {
    setState(prev => ({
      ...prev,
      metadata,
      error: null
    }))
  }

  const handleUploadProgress = (progress: UploadProgress) => {
    setState(prev => ({
      ...prev,
      uploadProgress: {
        ...prev.uploadProgress,
        [progress.fileId]: progress
      }
    }))

    // Update file status in the files array
    setState(prev => ({
      ...prev,
      files: prev.files.map(file => 
        file.id === progress.fileId 
          ? { ...file, progress: progress.progress, status: progress.status, error: progress.error }
          : file
      )
    }))
  }

  const handleTermsAccept = () => {
    setState(prev => ({
      ...prev,
      hasAcceptedTerms: true,
      showTermsReminder: false,
      currentStep: 'metadata'
    }))
  }

  const handleTermsDecline = () => {
    setState(prev => ({
      ...prev,
      showTermsReminder: false,
      currentStep: 'files',
      error: 'You must accept the terms to continue with the upload'
    }))
  }

  const validateStep = (step: UploadStep): boolean => {
    switch (step) {
      case 'files':
        return state.files.length > 0 && state.files.some(f => f.status !== 'error')
      case 'terms':
        return true // Terms step just needs to be acknowledged
      case 'metadata':
        return !!(
          state.metadata.title.trim() &&
          state.metadata.artist.trim() &&
          state.metadata.aiTool &&
          state.metadata.genre
        )
      default:
        return true
    }
  }

  const nextStep = () => {
    if (!validateStep(state.currentStep)) {
      setState(prev => ({
        ...prev,
        error: 'Please complete all required fields before continuing'
      }))
      return
    }

    const steps: UploadStep[] = ['files', 'terms', 'metadata', 'uploading', 'success']
    const currentIndex = steps.indexOf(state.currentStep)
    
    if (currentIndex < steps.length - 1) {
      const nextStepName = steps[currentIndex + 1]
      
      // Show terms reminder when moving to terms step
      if (nextStepName === 'terms') {
        setState(prev => ({
          ...prev,
          showTermsReminder: true,
          error: null
        }))
        return
      }

      setState(prev => ({
        ...prev,
        currentStep: nextStepName,
        error: null
      }))

      // Auto-start upload when reaching upload step
      if (nextStepName === 'uploading') {
        handleUpload()
      }
    }
  }

  const prevStep = () => {
    const steps: UploadStep[] = ['files', 'terms', 'metadata', 'uploading', 'success']
    const currentIndex = steps.indexOf(state.currentStep)
    
    if (currentIndex > 0) {
      setState(prev => ({
        ...prev,
        currentStep: steps[currentIndex - 1],
        error: null
      }))
    }
  }

  const handleUpload = async () => {
    if (!user) return

    setState(prev => ({ ...prev, isSubmitting: true, error: null }))

    try {
      const validFiles = state.files.filter(f => f.status !== 'error')
      const metadataArray = validFiles.map(() => state.metadata) // Use same metadata for all files

      const results = await uploadService.uploadFiles(
        validFiles,
        metadataArray,
        user.id,
        handleUploadProgress
      )

      setState(prev => ({
        ...prev,
        uploadResults: results,
        currentStep: 'success',
        isSubmitting: false
      }))

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isSubmitting: false
      }))
    }
  }

  const resetUpload = () => {
    setState({
      files: [],
      metadata: initialMetadata,
      currentStep: 'files',
      uploadProgress: {},
      uploadResults: [],
      isSubmitting: false,
      error: null,
      hasAcceptedTerms: false,
      showTermsReminder: false
    })
  }

  const getStepTitle = (step: UploadStep): string => {
    switch (step) {
      case 'files':
        return 'Select Audio Files'
      case 'terms':
        return 'AI Content Guidelines'
      case 'metadata':
        return 'Add Track Information'
      case 'uploading':
        return 'Uploading Your Music'
      case 'success':
        return 'Upload Complete!'
      default:
        return 'Upload Music'
    }
  }

  const getStepDescription = (step: UploadStep): string => {
    switch (step) {
      case 'files':
        return 'Choose the audio files you want to upload to Tunami'
      case 'terms':
        return 'Important guidelines for AI-generated content'
      case 'metadata':
        return 'Tell us about your AI-generated music'
      case 'uploading':
        return 'Please wait while we upload and process your files'
      case 'success':
        return 'Your music has been successfully uploaded and is ready to share!'
      default:
        return 'Upload your AI-generated music to the platform'
    }
  }

  const renderStepContent = () => {
    switch (state.currentStep) {
      case 'files':
        return (
          <FileUpload
            onFilesSelected={handleFilesSelected}
            onFileRemove={handleFileRemove}
            maxFiles={5}
            className="max-w-2xl mx-auto"
          />
        )

      case 'terms':
        return (
          <div className="max-w-2xl mx-auto text-center">
            <p className="text-gray-300 mb-6">
              Before uploading your AI-generated music, please review our content guidelines.
            </p>
            <button
              onClick={() => setState(prev => ({ ...prev, showTermsReminder: true }))}
              className="btn-primary"
            >
              Review Guidelines
            </button>
          </div>
        )

      case 'metadata':
        return (
          <UploadForm
            metadata={state.metadata}
            onMetadataChange={handleMetadataChange}
            isSubmitting={state.isSubmitting}
            className="max-w-2xl mx-auto"
          />
        )

      case 'uploading':
        return (
          <div className="max-w-2xl mx-auto space-y-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Upload className="w-8 h-8 text-white animate-pulse" />
              </div>
              <p className="text-gray-300">
                Uploading {state.files.length} file{state.files.length !== 1 ? 's' : ''}...
              </p>
            </div>

            <div className="space-y-4">
              {state.files.map(file => {
                const progress = state.uploadProgress[file.id]
                return (
                  <div key={file.id} className="p-4 bg-gray-800 rounded-lg border border-gray-700">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white truncate">
                        {file.file.name}
                      </span>
                      <span className="text-xs text-gray-400">
                        {progress?.progress || 0}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress?.progress || 0}%` }}
                      />
                    </div>
                    {progress?.error && (
                      <p className="text-xs text-red-400 mt-1">{progress.error}</p>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        )

      case 'success':
        const successCount = state.uploadResults.filter(r => r.success).length
        const failureCount = state.uploadResults.filter(r => !r.success).length

        return (
          <div className="max-w-2xl mx-auto text-center space-y-6">
            <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="w-10 h-10 text-white" />
            </div>

            <div>
              <h3 className="text-2xl font-bold text-white mb-2">
                Upload Successful!
              </h3>
              <p className="text-gray-300">
                {successCount} of {state.uploadResults.length} file{state.uploadResults.length !== 1 ? 's' : ''} uploaded successfully
              </p>
            </div>

            {failureCount > 0 && (
              <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                <p className="text-red-300 text-sm">
                  {failureCount} file{failureCount !== 1 ? 's' : ''} failed to upload. Please try again.
                </p>
              </div>
            )}

            <div className="flex gap-4 justify-center">
              <button
                onClick={() => {
                  const firstSuccessfulTrack = state.uploadResults.find(r => r.success && r.trackId)
                  if (firstSuccessfulTrack?.trackId) {
                    router.push(`/track/${firstSuccessfulTrack.trackId}`)
                  } else {
                    router.push('/dashboard')
                  }
                }}
                className="btn-primary flex items-center gap-2"
              >
                <Music className="w-4 h-4" />
                {state.uploadResults.find(r => r.success && r.trackId) ? 'View Track' : 'View My Music'}
              </button>
              <button
                onClick={resetUpload}
                className="btn-secondary flex items-center gap-2"
              >
                <Upload className="w-4 h-4" />
                Upload More
              </button>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (!user) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <Music className="w-16 h-16 text-purple-400 animate-spin mx-auto mb-4" />
            <p className="text-gray-300">Loading...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-white">
              {getStepTitle(state.currentStep)}
            </h1>
          </div>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            {getStepDescription(state.currentStep)}
          </p>
        </div>

        {/* Progress Steps */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            {(['files', 'terms', 'metadata', 'uploading', 'success'] as UploadStep[]).map((step, index) => {
              const isActive = state.currentStep === step
              const isCompleted = ['files', 'terms', 'metadata', 'uploading', 'success'].indexOf(state.currentStep) > index
              
              return (
                <div key={step} className="flex items-center">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                    ${isActive 
                      ? 'bg-purple-600 text-white' 
                      : isCompleted 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-700 text-gray-400'
                    }
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  {index < 4 && (
                    <div className={`
                      w-12 h-0.5 mx-2
                      ${isCompleted ? 'bg-green-600' : 'bg-gray-700'}
                    `} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Error Display */}
        {state.error && (
          <div className="max-w-2xl mx-auto mb-6">
            <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-red-400" />
                <p className="text-red-300 text-sm">{state.error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Step Content */}
        <div className="mb-8">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        {state.currentStep !== 'uploading' && state.currentStep !== 'success' && (
          <div className="flex justify-center gap-4">
            {state.currentStep !== 'files' && (
              <button
                onClick={prevStep}
                className="btn-secondary flex items-center gap-2"
                disabled={state.isSubmitting}
              >
                <ArrowLeft className="w-4 h-4" />
                Back
              </button>
            )}

            <button
              onClick={nextStep}
              disabled={!validateStep(state.currentStep) || state.isSubmitting}
              className="btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {state.currentStep === 'metadata' ? 'Upload Files' : 'Continue'}
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        )}

        {/* Recent Uploads Section - Only show on files step */}
        {state.currentStep === 'files' && user && (
          <div className="mt-12 border-t border-gray-700 pt-8">
            <UploadHistory 
              userId={user.id}
              limit={5}
              showFilters={false}
              className="max-w-4xl mx-auto"
            />
          </div>
        )}

        {/* Terms Reminder Modal */}
        <TermsReminder
          isVisible={state.showTermsReminder}
          onAccept={handleTermsAccept}
          onDecline={handleTermsDecline}
        />
      </div>
    </MainLayout>
  )
} 