'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { User } from '@supabase/supabase-js'
import { Music, Loader2 } from 'lucide-react'
import { auth } from '@/lib/supabase'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
  requireAuth?: boolean
}

export default function ProtectedRoute({ 
  children, 
  fallback,
  redirectTo = '/auth/login',
  requireAuth = true 
}: ProtectedRouteProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  const checkAuth = useCallback(async () => {
    try {
      const { user } = await auth.getCurrentUser()
      setUser(user)
      
      if (requireAuth && !user) {
        router.push(redirectTo)
        return
      }
      
      if (!requireAuth && user) {
        // If component requires no auth but user is authenticated
        // (useful for auth pages when user is already logged in)
        router.push('/dashboard')
        return
      }
      
    } catch (error) {
      console.error('Auth check failed:', error)
      if (requireAuth) {
        router.push(redirectTo)
      }
    } finally {
      setLoading(false)
    }
  }, [requireAuth, redirectTo, router])

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  // Loading state
  if (loading) {
    return fallback || (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center">
        <div className="text-center">
          <div className="relative mb-4">
            <Music className="w-16 h-16 text-primary-400 animate-pulse mx-auto" />
            <Loader2 className="w-6 h-6 text-white animate-spin absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
          </div>
          <p className="text-gray-300 animate-pulse">Loading...</p>
        </div>
      </div>
    )
  }

  // Auth check failed
  if (requireAuth && !user) {
    return fallback || (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center">
        <div className="text-center">
          <Music className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <p className="text-gray-300">Access denied. Redirecting...</p>
        </div>
      </div>
    )
  }

  // Non-auth page check (redirect authenticated users)
  if (!requireAuth && user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center">
        <div className="text-center">
          <Music className="w-16 h-16 text-green-400 mx-auto mb-4" />
          <p className="text-gray-300">Already logged in. Redirecting...</p>
        </div>
      </div>
    )
  }

  // Render children if auth check passes
  return <>{children}</>
}

// Higher-order component wrapper
export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    )
  }
}

// Hook for checking auth status
export function useAuthRequired(redirectTo: string = '/auth/login') {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { user } = await auth.getCurrentUser()
        setUser(user)
        
        if (!user) {
          router.push(redirectTo)
        }
      } catch (error) {
        router.push(redirectTo)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [redirectTo, router])

  return { user, loading }
} 