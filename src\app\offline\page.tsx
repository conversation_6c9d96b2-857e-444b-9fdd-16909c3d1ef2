'use client'

import { useState, useEffect } from 'react'
import { 
  WifiOff, 
  RefreshCw, 
  Music, 
  Play, 
  Headphones,
  Download,
  Heart,
  Clock,
  Signal,
  Zap,
  Home
} from 'lucide-react'
import Link from 'next/link'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'

export default function OfflinePage() {
  const [isRetrying, setIsRetrying] = useState(false)
  const [lastOnlineTime, setLastOnlineTime] = useState<Date | null>(null)
  const [cachedTracks, setCachedTracks] = useState<any[]>([])
  const [offlineFeatures] = useState([
    {
      icon: Play,
      title: 'Cached Music',
      description: 'Play your recently listened tracks'
    },
    {
      icon: Download,
      title: 'Downloaded Content',
      description: 'Access your offline downloads'
    },
    {
      icon: Heart,
      title: 'Favorites',
      description: 'Browse your liked tracks'
    },
    {
      icon: Clock,
      title: 'History',
      description: 'View your listening history'
    }
  ])

  const { isOnline, networkType, connectionSpeed } = useNetworkStatus()

  useEffect(() => {
    // Track when user was last online
    if (isOnline && !lastOnlineTime) {
      setLastOnlineTime(new Date())
    } else if (!isOnline && !lastOnlineTime) {
      const stored = localStorage.getItem('tunami-last-online')
      if (stored) {
        setLastOnlineTime(new Date(stored))
      }
    }

    // Store last online time
    if (isOnline) {
      localStorage.setItem('tunami-last-online', new Date().toISOString())
    }
  }, [isOnline, lastOnlineTime])

  useEffect(() => {
    // Load cached content info
    loadCachedContent()
  }, [])

  const loadCachedContent = async () => {
    try {
      // Check for cached tracks in service worker
      if ('caches' in window) {
        const audioCache = await caches.open('tunami-audio-v1.0.0')
        const cachedRequests = await audioCache.keys()
        
        const tracks = cachedRequests.map((request, index) => ({
          id: index,
          title: `Cached Track ${index + 1}`,
          artist: 'Unknown Artist',
          url: request.url,
          cached: true
        }))
        
        setCachedTracks(tracks.slice(0, 5)) // Show first 5
      }
    } catch (error) {
      console.error('Failed to load cached content:', error)
    }
  }

  const handleRetryConnection = async () => {
    setIsRetrying(true)
    
    try {
      // Try to fetch a small resource to test connectivity
      const response = await fetch('/manifest.json', { 
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000) 
      })
      
      if (response.ok) {
        // Connection restored, reload the page
        window.location.reload()
      } else {
        throw new Error('Connection test failed')
      }
    } catch (error) {
      console.log('Still offline:', error)
      setTimeout(() => setIsRetrying(false), 2000)
    }
  }

  const formatLastOnlineTime = (date: Date | null) => {
    if (!date) return 'Unknown'
    
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`
    return `${days} day${days > 1 ? 's' : ''} ago`
  }

  const getConnectionIcon = () => {
    if (isOnline) {
      switch (connectionSpeed) {
        case 'fast': return <Signal className="w-5 h-5 text-green-500" />
        case 'slow': return <Signal className="w-5 h-5 text-yellow-500" />
        default: return <Signal className="w-5 h-5 text-blue-500" />
      }
    }
    return <WifiOff className="w-5 h-5 text-red-500" />
  }

  const getConnectionStatus = () => {
    if (isOnline) {
      return (
        <div className="inline-flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm">
          <Signal className="w-4 h-4" />
          <span>Connected</span>
        </div>
      )
    }
    
    return (
      <div className="inline-flex items-center space-x-2 px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-full text-sm">
        <WifiOff className="w-4 h-4" />
        <span>Offline</span>
      </div>
    )
  }

  if (isOnline) {
    // Redirect if back online
    window.location.href = '/'
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 text-white">
      {/* Header */}
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="relative">
              <div className="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                <WifiOff className="w-10 h-10 text-red-400" />
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">!</span>
              </div>
            </div>
          </div>
          
          <h1 className="text-4xl font-bold mb-4">
            You're Offline
          </h1>
          
          <p className="text-xl text-purple-200 mb-6">
            No internet connection detected. But don't worry - you can still enjoy Tunami!
          </p>
          
          {getConnectionStatus()}
        </div>

        {/* Connection Info */}
        <div className="max-w-md mx-auto mb-8 p-6 bg-white/10 backdrop-blur-sm rounded-2xl">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Connection Status</h3>
            {getConnectionIcon()}
          </div>
          
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-300">Status:</span>
              <span className="text-red-400">Offline</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-300">Last Online:</span>
              <span className="text-purple-300">{formatLastOnlineTime(lastOnlineTime)}</span>
            </div>
            
            {networkType && (
              <div className="flex justify-between">
                <span className="text-gray-300">Network Type:</span>
                <span className="text-blue-300 capitalize">{networkType}</span>
              </div>
            )}
          </div>
          
          <button
            onClick={handleRetryConnection}
            disabled={isRetrying}
            className="w-full mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:opacity-50 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
            <span>{isRetrying ? 'Checking...' : 'Retry Connection'}</span>
          </button>
        </div>

        {/* Offline Features */}
        <div className="max-w-4xl mx-auto mb-8">
          <h2 className="text-2xl font-bold text-center mb-6">
            What You Can Do Offline
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {offlineFeatures.map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <div
                  key={index}
                  className="p-6 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/20 transition-colors cursor-pointer"
                >
                  <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <IconComponent className="w-6 h-6" />
                  </div>
                  
                  <h3 className="text-lg font-semibold text-center mb-2">
                    {feature.title}
                  </h3>
                  
                  <p className="text-sm text-gray-300 text-center">
                    {feature.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Cached Content */}
        {cachedTracks.length > 0 && (
          <div className="max-w-2xl mx-auto mb-8">
            <h2 className="text-xl font-bold mb-4 text-center">
              Recently Cached Tracks
            </h2>
            
            <div className="space-y-3">
              {cachedTracks.map((track) => (
                <div
                  key={track.id}
                  className="flex items-center space-x-4 p-4 bg-white/10 backdrop-blur-sm rounded-lg"
                >
                  <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                    <Music className="w-6 h-6" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-white truncate">
                      {track.title}
                    </h4>
                    <p className="text-sm text-gray-300 truncate">
                      {track.artist}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs text-green-400">Cached</span>
                  </div>
                  
                  <button className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <Play className="w-5 h-5" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tips */}
        <div className="max-w-2xl mx-auto mb-8">
          <h2 className="text-xl font-bold mb-4 text-center">
            Offline Tips
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-4 bg-white/10 backdrop-blur-sm rounded-lg">
              <Zap className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium mb-1">Save Battery</h4>
                <p className="text-sm text-gray-300">
                  Turn on airplane mode to prevent your device from searching for networks.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-4 bg-white/10 backdrop-blur-sm rounded-lg">
              <Download className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium mb-1">Download for Later</h4>
                <p className="text-sm text-gray-300">
                  When back online, download your favorite tracks for offline listening.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-4 bg-white/10 backdrop-blur-sm rounded-lg">
              <Headphones className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium mb-1">Enjoy Cached Content</h4>
                <p className="text-sm text-gray-300">
                  Your recently played tracks are automatically cached for offline access.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="text-center">
          <div className="inline-flex items-center space-x-4">
            <Link
              href="/"
              className="px-6 py-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <Home className="w-5 h-5" />
              <span>Go Home</span>
            </Link>
            
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <RefreshCw className="w-5 h-5" />
              <span>Reload Page</span>
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-12 text-gray-400 text-sm">
          <p>This page works offline thanks to PWA technology</p>
          <p className="mt-1">Tunami • Progressive Web App</p>
        </div>
      </div>
    </div>
  )
} 