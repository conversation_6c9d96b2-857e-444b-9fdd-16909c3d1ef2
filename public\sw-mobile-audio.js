// Service Worker for Tunami Mobile Audio
// Handles background notifications and media controls

const CACHE_NAME = 'tunami-mobile-audio-v1'
const NOTIFICATION_TAG = 'tunami-audio'

// Install event
self.addEventListener('install', (event) => {
  console.log('Tunami Audio SW installed')
  self.skipWaiting()
})

// Activate event
self.addEventListener('activate', (event) => {
  console.log('Tunami Audio SW activated')
  event.waitUntil(self.clients.claim())
})

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event.notification.tag, event.action)
  
  event.notification.close()

  // Handle different actions
  if (event.action === 'play-pause') {
    // Send message to main app to toggle play/pause
    event.waitUntil(
      self.clients.matchAll({ type: 'window' }).then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: 'NOTIFICATION_ACTION',
            action: 'play-pause'
          })
        })
      })
    )
  } else if (event.action === 'next') {
    // Send message to main app to play next track
    event.waitUntil(
      self.clients.matchAll({ type: 'window' }).then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: 'NOTIFICATION_ACTION',
            action: 'next'
          })
        })
      })
    )
  } else if (event.action === 'previous') {
    // Send message to main app to play previous track
    event.waitUntil(
      self.clients.matchAll({ type: 'window' }).then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: 'NOTIFICATION_ACTION',
            action: 'previous'
          })
        })
      })
    )
  } else {
    // Default action - focus the app
    event.waitUntil(
      self.clients.matchAll({ type: 'window' }).then((clients) => {
        // Check if there's already a window/tab open
        for (const client of clients) {
          if (client.url.includes('tunami') && 'focus' in client) {
            return client.focus()
          }
        }
        
        // If no window is open, open a new one
        if (self.clients.openWindow) {
          return self.clients.openWindow('/')
        }
      })
    )
  }
})

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event.notification.tag)
  
  // Send message to main app about notification close
  self.clients.matchAll({ type: 'window' }).then((clients) => {
    clients.forEach((client) => {
      client.postMessage({
        type: 'NOTIFICATION_CLOSED'
      })
    })
  })
})

// Handle background sync (for offline capabilities)
self.addEventListener('sync', (event) => {
  console.log('Background sync:', event.tag)
  
  if (event.tag === 'audio-playback-sync') {
    event.waitUntil(handleAudioSync())
  }
})

// Function to handle audio sync
async function handleAudioSync() {
  try {
    // Get all clients
    const clients = await self.clients.matchAll({ type: 'window' })
    
    // Send sync message to active clients
    clients.forEach((client) => {
      client.postMessage({
        type: 'AUDIO_SYNC',
        timestamp: Date.now()
      })
    })
  } catch (error) {
    console.error('Audio sync failed:', error)
  }
}

// Handle messages from the main app
self.addEventListener('message', (event) => {
  console.log('SW received message:', event.data)
  
  const { type, data } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'UPDATE_AUDIO_STATE':
      // Store current audio state for persistence
      handleAudioStateUpdate(data)
      break
      
    case 'SHOW_PERSISTENT_NOTIFICATION':
      handleShowPersistentNotification(data)
      break
      
    case 'HIDE_PERSISTENT_NOTIFICATION':
      handleHidePersistentNotification()
      break
      
    default:
      console.log('Unknown message type:', type)
  }
})

// Handle audio state updates
function handleAudioStateUpdate(audioState) {
  // Store in IndexedDB or localStorage for persistence
  // This could be used to restore state after app restart
  console.log('Audio state updated:', audioState)
}

// Show persistent notification
async function handleShowPersistentNotification(notificationData) {
  const { title, body, icon, actions } = notificationData
  
  try {
    await self.registration.showNotification(title, {
      body,
      icon: icon || '/images/tunami-icon-192.png',
      badge: '/images/tunami-badge-72.png',
      tag: NOTIFICATION_TAG,
      persistent: true,
      requireInteraction: false,
      actions: actions || [
        {
          action: 'play-pause',
          title: 'Play/Pause',
          icon: '/images/play-pause-icon.png'
        },
        {
          action: 'previous',
          title: 'Previous',
          icon: '/images/previous-icon.png'
        },
        {
          action: 'next',
          title: 'Next',
          icon: '/images/next-icon.png'
        }
      ]
    })
  } catch (error) {
    console.error('Failed to show persistent notification:', error)
  }
}

// Hide persistent notification
async function handleHidePersistentNotification() {
  try {
    const notifications = await self.registration.getNotifications({
      tag: NOTIFICATION_TAG
    })
    
    notifications.forEach(notification => notification.close())
  } catch (error) {
    console.error('Failed to hide persistent notification:', error)
  }
} 