
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tunami - Browser Compatibility Report</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6; color: #333; background: #f5f5f5; 
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #8b5cf6; color: white; padding: 2rem; border-radius: 8px; margin-bottom: 2rem; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .card { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { font-size: 2rem; font-weight: bold; color: #8b5cf6; }
        .browser-results { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .success { color: #10b981; }
        .warning { color: #f59e0b; }
        .error { color: #ef4444; }
        .progress { background: #e5e5e5; border-radius: 4px; height: 8px; overflow: hidden; }
        .progress-bar { height: 100%; transition: width 0.3s ease; }
        .recommendations { background: white; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; }
        .recommendation { padding: 1rem; margin: 0.5rem 0; border-left: 4px solid; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e5e5; }
        th { background: #f8f9fa; font-weight: 600; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Tunami Browser Compatibility Report</h1>
            <p>Generated on 31/5/2025, 9:25:10 am</p>
        </div>

        <div class="summary">
            <div class="card">
                <h3>Overall Compatibility</h3>
                <div class="metric">80%</div>
                <div class="progress">
                    <div class="progress-bar" style="width: 80%; background: #f59e0b;"></div>
                </div>
            </div>
            <div class="card">
                <h3>Browsers Tested</h3>
                <div class="metric">5</div>
                <p>3 Desktop, 2 Mobile</p>
            </div>
            <div class="card">
                <h3>Test Results</h3>
                <div class="metric warning">
                    4/5
                </div>
                <p>Successful Tests</p>
            </div>
        </div>

        <div class="browser-results">
            
                <div class="card">
                    <h3>Chromium</h3>
                    <p class="success">
                        ✅ Tests Passed
                    </p>
                    
                        <p>Passed: 0, Failed: 0, Skipped: 0</p>
                    
                    
                </div>
            
                <div class="card">
                    <h3>Firefox</h3>
                    <p class="error">
                        ❌ Tests Failed
                    </p>
                    
                    <p class="error">Error: Command failed: npx playwright test --project=firefox --reporter=json
</p>
                </div>
            
                <div class="card">
                    <h3>Webkit</h3>
                    <p class="success">
                        ✅ Tests Passed
                    </p>
                    
                        <p>Passed: 0, Failed: 0, Skipped: 0</p>
                    
                    
                </div>
            
                <div class="card">
                    <h3>Mobile-chrome</h3>
                    <p class="success">
                        ✅ Tests Passed
                    </p>
                    
                        <p>Passed: 0, Failed: 0, Skipped: 0</p>
                    
                    
                </div>
            
                <div class="card">
                    <h3>Mobile-safari</h3>
                    <p class="success">
                        ✅ Tests Passed
                    </p>
                    
                        <p>Passed: 0, Failed: 0, Skipped: 0</p>
                    
                    
                </div>
            
        </div>

        
            <div class="recommendations">
                <h2>📋 Recommendations</h2>
                
                    <div class="recommendation" style="border-color: #ef4444; background: #fef2f2;">
                        <strong>firefox: firefox testing failed: Command failed: npx playwright test --project=firefox --reporter=json
</strong>
                        <p>Check browser compatibility and fix blocking issues</p>
                    </div>
                
            </div>
        

        <div class="card">
            <h2>🔊 Audio Compatibility</h2>
            <table>
                <thead>
                    <tr>
                        <th>Browser</th>
                        <th>Audio Tests</th>
                        <th>Passed</th>
                        <th>Failed</th>
                        <th>Score</th>
                    </tr>
                </thead>
                <tbody>
                    
                        <tr>
                            <td>chromium</td>
                            <td>0</td>
                            <td class="success">0</td>
                            <td class="">0</td>
                            <td class="error">0%</td>
                        </tr>
                    
                        <tr>
                            <td>firefox</td>
                            <td>0</td>
                            <td class="success">0</td>
                            <td class="">0</td>
                            <td class="error">0%</td>
                        </tr>
                    
                        <tr>
                            <td>webkit</td>
                            <td>0</td>
                            <td class="success">0</td>
                            <td class="">0</td>
                            <td class="error">0%</td>
                        </tr>
                    
                        <tr>
                            <td>mobile-chrome</td>
                            <td>0</td>
                            <td class="success">0</td>
                            <td class="">0</td>
                            <td class="error">0%</td>
                        </tr>
                    
                        <tr>
                            <td>mobile-safari</td>
                            <td>0</td>
                            <td class="success">0</td>
                            <td class="">0</td>
                            <td class="error">0%</td>
                        </tr>
                    
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>