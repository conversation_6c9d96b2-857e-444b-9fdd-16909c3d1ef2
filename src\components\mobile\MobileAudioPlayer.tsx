'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>or<PERSON>, 
  Loader2, 
  Music, 
  AlertTriangle,
  RefreshCw,
  Wifi,
  WifiOff,
  Signal,
  SignalLow,
  SignalMedium,
  SignalHigh
} from 'lucide-react'
import { AudioTrack } from '@/types/audio'
import { useMobileDetection } from '@/hooks/useMobileDetection'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { 
  createMobileAudioElement,
  playMobileAudio,
  handleMobileAudioInterruptions,
  getMobileAudioQuality,
  unlockIOSAudioContext
} from '@/utils/mobileAudio'
import { 
  handleAudioError, 
  TunamiError, 
  showErrorToast, 
  showSuccessToast 
} from '@/lib/error-handler'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'

interface MobileAudioPlayerProps {
  track?: AudioTrack
  autoPlay?: boolean
  showControls?: boolean
  showTrackInfo?: boolean
  showVolumeControl?: boolean
  showSeekBar?: boolean
  showSkipButtons?: boolean
  showNetworkIndicator?: boolean
  hasNext?: boolean
  hasPrevious?: boolean
  className?: string
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  onNext?: () => void
  onPrevious?: () => void
  onTimeUpdate?: (time: number) => void
  onLoadedMetadata?: (duration: number) => void
  onSeek?: (time: number) => void
  onVolumeChange?: (volume: number) => void
  onError?: (error: TunamiError) => void
}

interface MobileAudioState {
  isPlaying: boolean
  isLoading: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean
  error: TunamiError | null
  retryCount: number
  userInteracted: boolean
  audioContextUnlocked: boolean
  bufferedPercentage: number
}

export default function MobileAudioPlayer({
  track,
  autoPlay = false,
  showControls = true,
  showTrackInfo = true,
  showVolumeControl = true,
  showSeekBar = true,
  showSkipButtons = false,
  showNetworkIndicator = true,
  hasNext = false,
  hasPrevious = false,
  className = '',
  onPlay,
  onPause,
  onEnded,
  onNext,
  onPrevious,
  onTimeUpdate,
  onLoadedMetadata,
  onSeek,
  onVolumeChange,
  onError
}: MobileAudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const progressBarRef = useRef<HTMLDivElement>(null)
  const volumeBarRef = useRef<HTMLDivElement>(null)
  const interruptionCleanupRef = useRef<(() => void) | null>(null)
  
  const mobileDetection = useMobileDetection()
  const networkStatus = useNetworkStatus()
  
  const [state, setState] = useState<MobileAudioState>({
    isPlaying: false,
    isLoading: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    error: null,
    retryCount: 0,
    userInteracted: false,
    audioContextUnlocked: false,
    bufferedPercentage: 0
  })

  // Initialize mobile audio when track changes
  useEffect(() => {
    if (!track) return

    setState(prev => ({ 
      ...prev, 
      isLoading: true, 
      error: null, 
      currentTime: 0, 
      duration: 0 
    }))

    // Clean up previous audio
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.src = ''
    }

    // Clean up interruption handlers
    if (interruptionCleanupRef.current) {
      interruptionCleanupRef.current()
    }

    // Create mobile-optimized audio element
    const audio = createMobileAudioElement(
      track.src,
      networkStatus,
      () => {
        setState(prev => ({ ...prev, isLoading: false }))
      },
      (error) => {
        const tunamiError = handleAudioError(error, audio)
        setState(prev => ({ ...prev, error: tunamiError, isLoading: false }))
        showErrorToast(tunamiError)
        onError?.(tunamiError)
      }
    )

    audioRef.current = audio

    // Set up mobile audio interruption handling
    interruptionCleanupRef.current = handleMobileAudioInterruptions(
      audio,
      () => {
        setState(prev => ({ ...prev, isPlaying: false }))
        onPause?.()
      },
      () => {
        // Resume playback after interruption
        if (state.userInteracted) {
          handlePlay()
        }
      }
    )

    // Audio event listeners
    const handleTimeUpdate = () => {
      if (audio) {
        const time = audio.currentTime
        setState(prev => ({ ...prev, currentTime: time }))
        onTimeUpdate?.(time)
        
        // Update buffered percentage
        if (audio.buffered.length > 0) {
          const buffered = audio.buffered.end(audio.buffered.length - 1)
          const percentage = (buffered / audio.duration) * 100
          setState(prev => ({ ...prev, bufferedPercentage: percentage }))
        }
      }
    }

    const handleLoadedMetadata = () => {
      if (audio) {
        const dur = audio.duration
        setState(prev => ({ ...prev, duration: dur, isLoading: false }))
        onLoadedMetadata?.(dur)
      }
    }

    const handleEnded = () => {
      setState(prev => ({ ...prev, isPlaying: false, currentTime: 0 }))
      onEnded?.()
    }

    const handleLoadStart = () => {
      setState(prev => ({ ...prev, isLoading: true }))
    }

    const handleCanPlay = () => {
      setState(prev => ({ ...prev, isLoading: false }))
    }

    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('ended', handleEnded)
    audio.addEventListener('loadstart', handleLoadStart)
    audio.addEventListener('canplay', handleCanPlay)

    // Auto-play handling for mobile
    if (autoPlay && state.userInteracted) {
      setTimeout(() => {
        handlePlay()
      }, 100)
    }

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('ended', handleEnded)
      audio.removeEventListener('loadstart', handleLoadStart)
      audio.removeEventListener('canplay', handleCanPlay)
      
      if (interruptionCleanupRef.current) {
        interruptionCleanupRef.current()
      }
    }
  }, [track, networkStatus])

  // Handle iOS audio context unlock
  const handleUserInteraction = useCallback(async () => {
    if (!state.userInteracted) {
      setState(prev => ({ ...prev, userInteracted: true }))
      
      if (mobileDetection.isIOS && !state.audioContextUnlocked) {
        try {
          const unlocked = await unlockIOSAudioContext()
          setState(prev => ({ ...prev, audioContextUnlocked: unlocked }))
          if (unlocked) {
            showSuccessToast('Audio ready for playback')
          }
        } catch (error) {
          console.warn('Failed to unlock iOS audio context:', error)
        }
      }
    }
  }, [state.userInteracted, state.audioContextUnlocked, mobileDetection.isIOS])

  // Enhanced play functionality for mobile
  const handlePlay = useCallback(async () => {
    if (!audioRef.current || !track) return
    
    await handleUserInteraction()
    
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const success = await playMobileAudio(
        audioRef.current, 
        mobileDetection.isIOS && !state.audioContextUnlocked
      )
      
      if (success) {
        setState(prev => ({ ...prev, isPlaying: true, isLoading: false }))
        onPlay?.()
      }
    } catch (error) {
      const tunamiError = handleAudioError(error, audioRef.current)
      setState(prev => ({ 
        ...prev, 
        error: tunamiError, 
        isLoading: false, 
        isPlaying: false 
      }))
      showErrorToast(tunamiError)
      onError?.(tunamiError)
    }
  }, [track, mobileDetection.isIOS, state.audioContextUnlocked, handleUserInteraction, onPlay, onError])

  // Pause functionality
  const handlePause = useCallback(() => {
    if (!audioRef.current) return
    audioRef.current.pause()
    setState(prev => ({ ...prev, isPlaying: false }))
    onPause?.()
  }, [onPause])

  // Toggle play/pause with mobile optimizations
  const togglePlayPause = useCallback(async () => {
    await handleUserInteraction()
    
    if (state.isPlaying) {
      handlePause()
    } else {
      handlePlay()
    }
  }, [state.isPlaying, handlePlay, handlePause, handleUserInteraction])

  // Touch-optimized seek functionality
  const handleSeek = useCallback((e: React.TouchEvent<HTMLDivElement> | React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !progressBarRef.current || !state.duration) return

    const rect = progressBarRef.current.getBoundingClientRect()
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const clickX = clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    const newTime = percentage * state.duration

    audioRef.current.currentTime = newTime
    setState(prev => ({ ...prev, currentTime: newTime }))
    onSeek?.(newTime)
  }, [state.duration, onSeek])

  // Touch-optimized volume control
  const handleVolumeChange = useCallback((e: React.TouchEvent<HTMLDivElement> | React.MouseEvent<HTMLDivElement>) => {
    if (!volumeBarRef.current) return

    const rect = volumeBarRef.current.getBoundingClientRect()
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const clickX = clientX - rect.left
    const percentage = Math.max(0, Math.min(1, clickX / rect.width))
    
    setState(prev => ({ ...prev, volume: percentage, isMuted: false }))
    
    if (audioRef.current) {
      audioRef.current.volume = percentage
    }
    
    onVolumeChange?.(percentage)
  }, [onVolumeChange])

  // Toggle mute
  const toggleMute = useCallback(() => {
    const newMuted = !state.isMuted
    setState(prev => ({ ...prev, isMuted: newMuted }))
    
    if (audioRef.current) {
      audioRef.current.muted = newMuted
    }
  }, [state.isMuted])

  // Format time for mobile display
  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Get network quality icon
  const getNetworkIcon = () => {
    if (!networkStatus.isOnline) return <WifiOff className="w-4 h-4 text-red-400" />
    
    switch (networkStatus.quality) {
      case 'excellent':
        return <SignalHigh className="w-4 h-4 text-green-400" />
      case 'good':
        return <SignalMedium className="w-4 h-4 text-blue-400" />
      case 'fair':
        return <SignalLow className="w-4 h-4 text-yellow-400" />
      case 'poor':
        return <Signal className="w-4 h-4 text-red-400" />
      default:
        return <Wifi className="w-4 h-4 text-gray-400" />
    }
  }

  const progressPercentage = state.duration > 0 ? (state.currentTime / state.duration) * 100 : 0
  const audioQuality = getMobileAudioQuality(networkStatus)

  // Error state UI
  if (state.error) {
    return (
      <div className={`bg-gray-900 rounded-lg border border-red-500 p-4 ${className}`}>
        <div className="flex items-center space-x-3 mb-3">
          <AlertTriangle className="w-6 h-6 text-red-400 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <p className="text-red-300 font-medium text-sm">Audio Error</p>
            <p className="text-gray-400 text-xs truncate">{state.error.userMessage}</p>
          </div>
          {showNetworkIndicator && getNetworkIcon()}
        </div>
        
        <button
          onClick={handlePlay}
          className="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 min-h-[44px]"
        >
          <RefreshCw className="w-4 h-4" />
          Try Again
        </button>
      </div>
    )
  }

  // No track state
  if (!track) {
    return (
      <div className={`flex items-center justify-center p-8 text-gray-400 ${className}`}>
        <Music className="w-8 h-8 mr-3" />
        <span>No track selected</span>
      </div>
    )
  }

  return (
    <ComponentErrorBoundary>
      <div className={`bg-gray-900 rounded-lg border border-gray-800 p-4 ${className}`}>
        {/* Network Status Indicator */}
        {showNetworkIndicator && (
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              {getNetworkIcon()}
              <span className="text-xs text-gray-400">
                {networkStatus.quality} • {audioQuality.bitrate}kbps
              </span>
            </div>
            {networkStatus.saveData && (
              <span className="text-xs text-yellow-400">Data Saver</span>
            )}
          </div>
        )}

        {/* Track Info Section */}
        {showTrackInfo && (
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <Music className="w-6 h-6 text-white" />
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className="text-white font-medium truncate text-lg">{track.title}</h3>
              <p className="text-gray-400 text-sm truncate">{track.artist}</p>
              {track.aiTool && (
                <p className="text-purple-400 text-xs">Generated with {track.aiTool}</p>
              )}
            </div>
          </div>
        )}

        {/* Controls Section */}
        {showControls && (
          <div className="space-y-4">
            {/* Progress Bar */}
            {showSeekBar && (
              <div className="space-y-2">
                <div 
                  ref={progressBarRef}
                  onTouchStart={handleSeek}
                  onMouseDown={handleSeek}
                  className="relative w-full h-3 bg-gray-700 rounded-full overflow-hidden cursor-pointer touch-manipulation"
                >
                  {/* Buffered progress */}
                  <div 
                    className="absolute top-0 left-0 h-full bg-gray-600 transition-all duration-200"
                    style={{ width: `${state.bufferedPercentage}%` }}
                  />
                  
                  {/* Current progress */}
                  <div 
                    className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-200"
                    style={{ width: `${progressPercentage}%` }}
                  />
                  
                  {/* Touch target */}
                  <div 
                    className="absolute top-1/2 left-0 w-6 h-6 bg-white rounded-full shadow-md transform -translate-y-1/2 transition-all duration-200"
                    style={{ left: `${progressPercentage}%`, marginLeft: '-12px' }}
                  />
                </div>
                
                <div className="flex justify-between text-sm text-gray-400">
                  <span>{formatTime(state.currentTime)}</span>
                  <span>{formatTime(state.duration)}</span>
                </div>
              </div>
            )}

            {/* Control Buttons */}
            <div className="flex items-center justify-center space-x-6">
              {showSkipButtons && (
                <button
                  onClick={onPrevious}
                  disabled={!hasPrevious}
                  className="w-12 h-12 flex items-center justify-center text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 min-h-[44px] min-w-[44px]"
                >
                  <SkipBack className="w-6 h-6" />
                </button>
              )}

              <button
                onClick={togglePlayPause}
                disabled={state.isLoading}
                className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 min-h-[44px] min-w-[44px]"
              >
                {state.isLoading ? (
                  <Loader2 className="w-6 h-6 text-white animate-spin" />
                ) : state.isPlaying ? (
                  <Pause className="w-6 h-6 text-white" />
                ) : (
                  <Play className="w-6 h-6 text-white ml-1" />
                )}
              </button>

              {showSkipButtons && (
                <button
                  onClick={onNext}
                  disabled={!hasNext}
                  className="w-12 h-12 flex items-center justify-center text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 min-h-[44px] min-w-[44px]"
                >
                  <SkipForward className="w-6 h-6" />
                </button>
              )}
            </div>

            {/* Volume Control */}
            {showVolumeControl && !mobileDetection.isIOS && (
              <div className="flex items-center justify-center space-x-3">
                <button
                  onClick={toggleMute}
                  className="text-gray-400 hover:text-white transition-colors duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center"
                >
                  {state.isMuted || state.volume === 0 ? (
                    <VolumeX className="w-5 h-5" />
                  ) : (
                    <Volume2 className="w-5 h-5" />
                  )}
                </button>
                
                <div 
                  ref={volumeBarRef}
                  onTouchStart={handleVolumeChange}
                  onMouseDown={handleVolumeChange}
                  className="relative w-24 h-3 bg-gray-700 rounded-full cursor-pointer touch-manipulation"
                >
                  <div 
                    className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-200"
                    style={{ width: `${state.isMuted ? 0 : state.volume * 100}%` }}
                  />
                  <div 
                    className="absolute top-1/2 left-0 w-4 h-4 bg-white rounded-full shadow-md transform -translate-y-1/2 transition-all duration-200"
                    style={{ left: `${(state.isMuted ? 0 : state.volume) * 100}%`, marginLeft: '-8px' }}
                  />
                </div>
                
                <span className="text-xs text-gray-400 w-8 text-right">
                  {Math.round((state.isMuted ? 0 : state.volume) * 100)}%
                </span>
              </div>
            )}
          </div>
        )}

        {/* iOS Audio Context Warning */}
        {mobileDetection.isIOS && !state.userInteracted && (
          <div className="mt-3 p-2 bg-blue-900 border border-blue-700 rounded text-xs text-blue-300 text-center">
            Tap play button to enable audio
          </div>
        )}
      </div>
    </ComponentErrorBoundary>
  )
} 