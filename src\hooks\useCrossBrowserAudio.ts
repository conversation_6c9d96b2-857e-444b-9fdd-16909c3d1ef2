import { useState, useEffect, useRef, useCallback } from 'react'
import { browserDetector, isSafari, isIOS, isChrome } from '@/lib/browser-detection'

interface AudioSource {
  src: string
  type: string
  format: string
}

interface CrossBrowserAudioState {
  isPlaying: boolean
  isPaused: boolean
  isLoading: boolean
  isBuffering: boolean
  duration: number
  currentTime: number
  volume: number
  isMuted: boolean
  error: string | null
  canPlay: boolean
  buffered: TimeRanges | null
  hasUserInteracted: boolean
  autoplayBlocked: boolean
}

interface CrossBrowserAudioOptions {
  sources: AudioSource[]
  preload?: 'none' | 'metadata' | 'auto'
  autoplay?: boolean
  loop?: boolean
  crossOrigin?: 'anonymous' | 'use-credentials'
  onLoadStart?: () => void
  onLoadedMetadata?: () => void
  onCanPlay?: () => void
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  onError?: (error: string) => void
  onTimeUpdate?: (currentTime: number) => void
  onProgress?: (buffered: TimeRanges) => void
}

export function useCrossBrowserAudio(options: CrossBrowserAudioOptions) {
  const {
    sources,
    preload = 'metadata',
    autoplay = false,
    loop = false,
    crossOrigin,
    onLoadStart,
    onLoadedMetadata,
    onCanPlay,
    onPlay,
    onPause,
    onEnded,
    onError,
    onTimeUpdate,
    onProgress
  } = options

  const audioRef = useRef<HTMLAudioElement | null>(null)
  const contextRef = useRef<AudioContext | null>(null)
  const sourceNodeRef = useRef<MediaElementAudioSourceNode | null>(null)
  const gainNodeRef = useRef<GainNode | null>(null)

  const [state, setState] = useState<CrossBrowserAudioState>({
    isPlaying: false,
    isPaused: true,
    isLoading: false,
    isBuffering: false,
    duration: 0,
    currentTime: 0,
    volume: 1,
    isMuted: false,
    error: null,
    canPlay: false,
    buffered: null,
    hasUserInteracted: false,
    autoplayBlocked: false
  })

  const browserInfo = browserDetector.detect()

  // Initialize audio element and Web Audio API context
  useEffect(() => {
    initializeAudio()
    return cleanup
  }, [sources])

  const initializeAudio = useCallback(() => {
    try {
      // Create audio element
      const audio = new Audio()
      
      // Set audio attributes based on browser capabilities
      setupAudioElement(audio)
      
      // Select best audio source for current browser
      const selectedSource = selectBestAudioSource(sources)
      if (selectedSource) {
        audio.src = selectedSource.src
      }

      // Set up Web Audio API if supported
      if (browserInfo.features.webAudio) {
        setupWebAudioContext(audio)
      }

      // Attach event listeners
      setupEventListeners(audio)

      audioRef.current = audio

    } catch (error) {
      console.error('Failed to initialize cross-browser audio:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Audio initialization failed'
      }))
    }
  }, [sources, browserInfo])

  const setupAudioElement = (audio: HTMLAudioElement) => {
    audio.preload = preload
    audio.loop = loop
    
    if (crossOrigin) {
      audio.crossOrigin = crossOrigin
    }

    // iOS Safari specific attributes
    if (isIOS()) {
      audio.setAttribute('playsinline', '')
      audio.setAttribute('webkit-playsinline', '')
    }

    // Chrome/Safari specific optimizations
    if (isChrome() || isSafari()) {
      audio.setAttribute('preload', preload)
    }
  }

  const selectBestAudioSource = (sources: AudioSource[]): AudioSource | null => {
    if (!sources.length) return null

    // Create temporary audio element for format testing
    const testAudio = new Audio()
    
    // Priority order based on browser capabilities
    const formatPriority = getBrowserFormatPriority()
    
    // Find the best supported format
    for (const format of formatPriority) {
      const source = sources.find(s => s.format === format)
      if (source && testAudio.canPlayType(source.type) !== '') {
        return source
      }
    }

    // Fallback to first source that can be played
    for (const source of sources) {
      if (testAudio.canPlayType(source.type) !== '') {
        return source
      }
    }

    return sources[0] // Last resort fallback
  }

  const getBrowserFormatPriority = (): string[] => {
    if (isSafari()) {
      return ['aac', 'mp3', 'wav', 'flac']
    } else if (browserInfo.name === 'Firefox') {
      return ['ogg', 'wav', 'mp3', 'flac']
    } else if (isChrome()) {
      return ['webm', 'ogg', 'mp3', 'wav', 'flac']
    }
    
    // Default priority
    return ['mp3', 'wav', 'ogg', 'aac', 'flac']
  }

  const setupWebAudioContext = (audio: HTMLAudioElement) => {
    try {
      const AudioContext = window.AudioContext || window.webkitAudioContext
      const context = new AudioContext()
      
      // Create audio graph
      const sourceNode = context.createMediaElementSource(audio)
      const gainNode = context.createGain()
      
      sourceNode.connect(gainNode)
      gainNode.connect(context.destination)
      
      contextRef.current = context
      sourceNodeRef.current = sourceNode
      gainNodeRef.current = gainNode

      // Handle context state changes
      context.addEventListener('statechange', () => {
        if (context.state === 'suspended' && state.hasUserInteracted) {
          context.resume()
        }
      })

    } catch (error) {
      console.warn('Web Audio API setup failed:', error)
    }
  }

  const setupEventListeners = (audio: HTMLAudioElement) => {
    const updateState = (updates: Partial<CrossBrowserAudioState>) => {
      setState(prev => ({ ...prev, ...updates }))
    }

    audio.addEventListener('loadstart', () => {
      updateState({ isLoading: true, error: null })
      onLoadStart?.()
    })

    audio.addEventListener('loadedmetadata', () => {
      updateState({ 
        duration: audio.duration,
        isLoading: false 
      })
      onLoadedMetadata?.()
    })

    audio.addEventListener('canplay', () => {
      updateState({ 
        canPlay: true,
        isLoading: false 
      })
      onCanPlay?.()

      // Attempt autoplay if enabled
      if (autoplay && state.hasUserInteracted) {
        play()
      }
    })

    audio.addEventListener('play', () => {
      updateState({ 
        isPlaying: true,
        isPaused: false 
      })
      onPlay?.()
    })

    audio.addEventListener('pause', () => {
      updateState({ 
        isPlaying: false,
        isPaused: true 
      })
      onPause?.()
    })

    audio.addEventListener('ended', () => {
      updateState({ 
        isPlaying: false,
        isPaused: true,
        currentTime: 0
      })
      onEnded?.()
    })

    audio.addEventListener('timeupdate', () => {
      updateState({ 
        currentTime: audio.currentTime 
      })
      onTimeUpdate?.(audio.currentTime)
    })

    audio.addEventListener('progress', () => {
      updateState({ 
        buffered: audio.buffered 
      })
      onProgress?.(audio.buffered)
    })

    audio.addEventListener('waiting', () => {
      updateState({ isBuffering: true })
    })

    audio.addEventListener('canplaythrough', () => {
      updateState({ isBuffering: false })
    })

    audio.addEventListener('error', (e) => {
      const errorMessage = getAudioErrorMessage(audio.error)
      updateState({ 
        error: errorMessage,
        isLoading: false,
        isBuffering: false 
      })
      onError?.(errorMessage)
    })

    audio.addEventListener('volumechange', () => {
      updateState({ 
        volume: audio.volume,
        isMuted: audio.muted 
      })
    })
  }

  const getAudioErrorMessage = (error: MediaError | null): string => {
    if (!error) return 'Unknown audio error'

    switch (error.code) {
      case MediaError.MEDIA_ERR_ABORTED:
        return 'Audio playback was aborted'
      case MediaError.MEDIA_ERR_NETWORK:
        return 'Network error occurred while loading audio'
      case MediaError.MEDIA_ERR_DECODE:
        return 'Audio format is not supported or corrupted'
      case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
        return 'Audio source is not supported in this browser'
      default:
        return error.message || 'Audio playback error'
    }
  }

  // User interaction handler for autoplay policies
  const enableUserInteraction = useCallback(() => {
    setState(prev => ({ ...prev, hasUserInteracted: true }))

    // Resume suspended audio context
    if (contextRef.current?.state === 'suspended') {
      contextRef.current.resume()
    }

    // Try autoplay if it was blocked
    if (autoplay && state.autoplayBlocked && audioRef.current) {
      play()
    }
  }, [autoplay, state.autoplayBlocked])

  // Playback controls
  const play = useCallback(async () => {
    if (!audioRef.current) return

    try {
      // Resume audio context if needed
      if (contextRef.current?.state === 'suspended') {
        await contextRef.current.resume()
      }

      await audioRef.current.play()
      
    } catch (error) {
      console.warn('Play failed:', error)
      
      if (error.name === 'NotAllowedError') {
        setState(prev => ({ 
          ...prev, 
          autoplayBlocked: true,
          error: 'Playback requires user interaction'
        }))
      } else {
        setState(prev => ({ 
          ...prev, 
          error: error instanceof Error ? error.message : 'Playback failed'
        }))
      }
    }
  }, [])

  const pause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
  }, [])

  const stop = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }
  }, [])

  const seek = useCallback((time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = Math.max(0, Math.min(time, audioRef.current.duration))
    }
  }, [])

  const setVolume = useCallback((volume: number) => {
    if (audioRef.current) {
      const clampedVolume = Math.max(0, Math.min(1, volume))
      audioRef.current.volume = clampedVolume
      
      // Also update Web Audio API gain node
      if (gainNodeRef.current) {
        gainNodeRef.current.gain.value = clampedVolume
      }
    }
  }, [])

  const setMuted = useCallback((muted: boolean) => {
    if (audioRef.current) {
      audioRef.current.muted = muted
    }
  }, [])

  const cleanup = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.src = ''
      audioRef.current.load()
    }

    if (contextRef.current) {
      contextRef.current.close()
    }

    sourceNodeRef.current = null
    gainNodeRef.current = null
    contextRef.current = null
  }, [])

  return {
    // State
    ...state,
    
    // Controls
    play,
    pause,
    stop,
    seek,
    setVolume,
    setMuted,
    enableUserInteraction,
    
    // Refs (for advanced usage)
    audioRef,
    contextRef,
    sourceNodeRef,
    gainNodeRef,
    
    // Utils
    cleanup,
    
    // Browser info
    browserInfo,
    supportedFormats: browserInfo.audioFormats
  }
}

export default useCrossBrowserAudio 