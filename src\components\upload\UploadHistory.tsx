'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON>, Music, Eye, Heart, Play, ExternalLink, Filter, Search, Calendar, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import type { Track } from '@/types/database'

interface UploadHistoryItem extends Track {
  profiles?: {
    username: string | null
    full_name: string | null
  }
}

interface UploadHistoryProps {
  userId?: string
  limit?: number
  showFilters?: boolean
  className?: string
}

type FilterStatus = 'all' | 'completed' | 'pending' | 'failed'
type SortBy = 'recent' | 'oldest' | 'name' | 'plays' | 'likes'

export default function UploadHistory({ 
  userId, 
  limit = 20, 
  showFilters = true,
  className = '' 
}: UploadHistoryProps) {
  const [uploads, setUploads] = useState<UploadHistoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<FilterStatus>('all')
  const [sortBy, setSortBy] = useState<SortBy>('recent')
  const [currentUser, setCurrentUser] = useState<any>(null)

  const checkAuth = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setCurrentUser(user)
  }

  const loadUploadHistory = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const targetUserId = userId || currentUser?.id
      if (!targetUserId) {
        setError('User not authenticated')
        return
      }

      let query = supabase
        .from('tracks')
        .select(`
          *,
          profiles:uploaded_by (
            username,
            full_name
          )
        `)
        .eq('uploaded_by', targetUserId)
        .limit(limit)

      // Apply status filter
      if (filterStatus !== 'all') {
        switch (filterStatus) {
          case 'completed':
            query = query.eq('upload_status', 'completed')
            break
          case 'pending':
            query = query.in('upload_status', ['pending', 'processing'])
            break
          case 'failed':
            query = query.eq('upload_status', 'failed')
            break
        }
      }

      // Apply sorting
      switch (sortBy) {
        case 'recent':
          query = query.order('created_at', { ascending: false })
          break
        case 'oldest':
          query = query.order('created_at', { ascending: true })
          break
        case 'name':
          query = query.order('title', { ascending: true })
          break
        case 'plays':
          query = query.order('play_count', { ascending: false })
          break
        case 'likes':
          query = query.order('like_count', { ascending: false })
          break
      }

      const { data, error: queryError } = await query

      if (queryError) {
        throw queryError
      }

      // Apply search filter client-side
      let filteredData = data || []
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase()
        filteredData = filteredData.filter(track =>
          track.title.toLowerCase().includes(query) ||
          track.artist_name.toLowerCase().includes(query) ||
          track.genre?.toLowerCase().includes(query)
        )
      }

      setUploads(filteredData)

    } catch (err) {
      console.error('Error loading upload history:', err)
      setError(err instanceof Error ? err.message : 'Failed to load upload history')
    } finally {
      setLoading(false)
    }
  }, [userId, currentUser, filterStatus, sortBy, limit, searchQuery])

  useEffect(() => {
    checkAuth()
  }, [])

  useEffect(() => {
    if (currentUser) {
      loadUploadHistory()
    }
  }, [currentUser, filterStatus, sortBy, limit, loadUploadHistory])

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatDuration = (seconds: number | null): string => {
    if (!seconds) return '--:--'
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'pending':
      case 'processing':
        return <AlertCircle className="w-4 h-4 text-yellow-400" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'completed':
        return 'Completed'
      case 'pending':
        return 'Pending'
      case 'processing':
        return 'Processing'
      case 'failed':
        return 'Failed'
      default:
        return 'Unknown'
    }
  }

  const getUploadStats = () => {
    const total = uploads.length
    const completed = uploads.filter(u => u.upload_status === 'completed').length
    const pending = uploads.filter(u => ['pending', 'processing'].includes(u.upload_status)).length
    const failed = uploads.filter(u => u.upload_status === 'failed').length

    return { total, completed, pending, failed }
  }

  if (!currentUser) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-400">Please sign in to view upload history</p>
      </div>
    )
  }

  const stats = getUploadStats()

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header & Stats */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <Clock className="w-6 h-6 text-purple-400" />
            Upload History
          </h2>
          <p className="text-gray-400 text-sm mt-1">
            Track your uploaded music and monitor upload status
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-4 gap-3">
          <div className="bg-gray-800 rounded-lg p-3 text-center">
            <p className="text-xs text-gray-400">Total</p>
            <p className="text-lg font-bold text-white">{stats.total}</p>
          </div>
          <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-3 text-center">
            <p className="text-xs text-green-400">Completed</p>
            <p className="text-lg font-bold text-green-400">{stats.completed}</p>
          </div>
          <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3 text-center">
            <p className="text-xs text-yellow-400">Pending</p>
            <p className="text-lg font-bold text-yellow-400">{stats.pending}</p>
          </div>
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3 text-center">
            <p className="text-xs text-red-400">Failed</p>
            <p className="text-lg font-bold text-red-400">{stats.failed}</p>
          </div>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-gray-800 rounded-lg p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              <input
                type="text"
                placeholder="Search uploads..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg 
                         focus:ring-2 focus:ring-purple-500 focus:border-transparent
                         text-white placeholder-gray-400 text-sm"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <Filter className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2 z-10" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as FilterStatus)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg 
                         focus:ring-2 focus:ring-purple-500 focus:border-transparent
                         text-white text-sm appearance-none cursor-pointer"
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
              </select>
            </div>

            {/* Sort */}
            <div className="relative">
              <Calendar className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2 z-10" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortBy)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg 
                         focus:ring-2 focus:ring-purple-500 focus:border-transparent
                         text-white text-sm appearance-none cursor-pointer"
              >
                <option value="recent">Most Recent</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name A-Z</option>
                <option value="plays">Most Played</option>
                <option value="likes">Most Liked</option>
              </select>
            </div>
          </div>

          <button
            onClick={loadUploadHistory}
            className="btn-secondary text-sm"
          >
            Apply Filters
          </button>
        </div>
      )}

      {/* Upload List */}
      {loading ? (
        <div className="text-center py-8">
          <Music className="w-12 h-12 text-purple-400 animate-pulse mx-auto mb-4" />
          <p className="text-gray-300">Loading upload history...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-300 mb-2">Error loading uploads</p>
          <p className="text-gray-400 text-sm">{error}</p>
          <button
            onClick={loadUploadHistory}
            className="btn-primary mt-4"
          >
            Try Again
          </button>
        </div>
      ) : uploads.length === 0 ? (
        <div className="text-center py-12">
          <Music className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No uploads yet</h3>
          <p className="text-gray-400 mb-4">
            {searchQuery ? 'No uploads match your search criteria' : 'Start uploading your AI-generated music!'}
          </p>
          <a href="/upload" className="btn-primary">
            Upload Your First Track
          </a>
        </div>
      ) : (
        <div className="space-y-3">
          {uploads.map((upload) => (
            <div
              key={upload.id}
              className="bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-start gap-3">
                    {/* Status & Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getStatusIcon(upload.upload_status)}
                        <h3 className="font-medium text-white">{upload.title}</h3>
                        <span className="text-xs px-2 py-1 bg-gray-700 rounded text-gray-300">
                          {getStatusText(upload.upload_status)}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-400">
                        <div>
                          <span className="block text-xs text-gray-500">Artist</span>
                          <span>{upload.artist_name}</span>
                        </div>
                        <div>
                          <span className="block text-xs text-gray-500">Duration</span>
                          <span>{formatDuration(upload.duration)}</span>
                        </div>
                        <div>
                          <span className="block text-xs text-gray-500">Uploaded</span>
                          <span>{formatDate(upload.created_at)}</span>
                        </div>
                        <div>
                          <span className="block text-xs text-gray-500">Visibility</span>
                          <span>{upload.is_public ? 'Public' : 'Private'}</span>
                        </div>
                      </div>

                      {/* Stats */}
                      <div className="flex items-center gap-4 mt-3 text-sm text-gray-400">
                        <div className="flex items-center gap-1">
                          <Play className="w-3 h-3" />
                          <span>{upload.play_count.toLocaleString()} plays</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="w-3 h-3" />
                          <span>{upload.like_count.toLocaleString()} likes</span>
                        </div>
                        {upload.genre && (
                          <div className="px-2 py-1 bg-purple-600/20 border border-purple-500/30 rounded text-xs text-purple-300">
                            {upload.genre.replace(/_/g, ' ')}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col gap-2">
                      {upload.upload_status === 'completed' && (
                        <a
                          href={`/track/${upload.id}`}
                          className="btn-secondary text-xs flex items-center gap-1"
                        >
                          <Eye className="w-3 h-3" />
                          View
                        </a>
                      )}
                      {upload.file_url && (
                        <a
                          href={upload.file_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="btn-secondary text-xs flex items-center gap-1"
                        >
                          <ExternalLink className="w-3 h-3" />
                          File
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
} 