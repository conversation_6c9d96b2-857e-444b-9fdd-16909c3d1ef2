'use client'

interface SkeletonProps {
  className?: string
  variant?: 'default' | 'rounded' | 'circle'
}

function Skeleton({ className = '', variant = 'default' }: SkeletonProps) {
  const baseClasses = 'animate-pulse bg-gray-800'
  const variantClasses = {
    default: 'rounded',
    rounded: 'rounded-lg',
    circle: 'rounded-full'
  }
  
  return <div className={`${baseClasses} ${variantClasses[variant]} ${className}`} />
}

// Track Card Skeleton
export function TrackCardSkeleton({ variant = 'default' }: { variant?: 'default' | 'compact' | 'featured' }) {
  if (variant === 'compact') {
    return (
      <div className="flex items-center space-x-3 p-3">
        <Skeleton variant="rounded" className="w-12 h-12" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
        </div>
        <Skeleton className="h-3 w-12" />
      </div>
    )
  }

  return (
    <div className="bg-gray-900 rounded-xl border border-gray-800 overflow-hidden">
      <Skeleton className="aspect-square w-full" />
      <div className="p-4 space-y-3">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
        <div className="flex items-center justify-between">
          <Skeleton className="h-3 w-16" />
          <div className="flex space-x-2">
            <Skeleton variant="circle" className="w-6 h-6" />
            <Skeleton variant="circle" className="w-6 h-6" />
          </div>
        </div>
      </div>
    </div>
  )
}

// Grid Skeleton
export function GridSkeleton({ 
  columns = 4, 
  rows = 3, 
  variant = 'default' 
}: { 
  columns?: number
  rows?: number
  variant?: 'default' | 'compact'
}) {
  const totalItems = columns * rows
  
  if (variant === 'compact') {
    return (
      <div className="space-y-2">
        {Array.from({ length: totalItems }, (_, i) => (
          <TrackCardSkeleton key={i} variant="compact" />
        ))}
      </div>
    )
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-${Math.min(columns, 4)} gap-6`}>
      {Array.from({ length: totalItems }, (_, i) => (
        <TrackCardSkeleton key={i} variant="default" />
      ))}
    </div>
  )
}

// Stats Card Skeleton
export function StatsCardSkeleton() {
  return (
    <div className="bg-gray-900 p-6 rounded-xl border border-gray-800">
      <div className="flex items-center space-x-3">
        <Skeleton variant="circle" className="w-8 h-8" />
        <div className="space-y-2">
          <Skeleton className="h-3 w-20" />
          <Skeleton className="h-6 w-12" />
        </div>
      </div>
    </div>
  )
}

// Section Header Skeleton
export function SectionHeaderSkeleton() {
  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center space-x-3">
        <Skeleton variant="circle" className="w-6 h-6" />
        <Skeleton className="h-6 w-32" />
      </div>
      <Skeleton className="h-4 w-20" />
    </div>
  )
} 