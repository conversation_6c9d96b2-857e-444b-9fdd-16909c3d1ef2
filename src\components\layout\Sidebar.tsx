﻿'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Home,
  Search,
  Music,
  Upload,
  ListMusic,
  Heart,
  History,
  Clock,
  Headphones,
  Settings,
  Plus,
  ChevronRight
} from 'lucide-react'

interface SidebarProps {
  isOpen: boolean
  onClose?: () => void
}

const navigation = [
  { name: 'Home', href: '/', icon: Home },
  { name: 'Search', href: '/search', icon: Search },
  { name: 'Browse', href: '/browse', icon: Music },
  { name: 'Upload', href: '/upload', icon: Upload }
]

const library = [
  { name: 'My Playlists', href: '/playlists', icon: ListMusic },
  { name: 'Liked Songs', href: '/liked', icon: Heart },
  { name: 'Upload History', href: '/history', icon: Clock },
  { name: 'Audio Demo', href: '/audio-demo', icon: Headphones }
]

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const pathname = usePathname()
  const [playlists] = useState([
    { id: 1, name: 'My Favorites', tracks: 24 },
    { id: 2, name: 'Chill Vibes', tracks: 18 },
    { id: 3, name: 'Workout Mix', tracks: 32 }
  ])

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-[45] lg:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <aside className={`fixed lg:static inset-y-0 left-0 z-50 w-64 bg-gray-900 border-r border-gray-800 transform ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 transition-transform duration-300 ease-in-out`}>
        <div className="flex flex-col h-full">
          {/* Main Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            <div className="space-y-1">
              {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      isActive
                        ? 'bg-purple-600 text-white'
                        : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }`}
                    onClick={onClose}
                  >
                    <item.icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </Link>
                )
              })}
            </div>

            {/* Library Section */}
            <div className="pt-6">
              <h3 className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Your Library
              </h3>
              <div className="mt-2 space-y-1">
                {library.map((item) => {
                  const isActive = pathname === item.href
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        isActive
                          ? 'bg-purple-600 text-white'
                          : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                      }`}
                      onClick={onClose}
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      {item.name}
                    </Link>
                  )
                })}
              </div>
            </div>

            {/* Playlists */}
            <div className="pt-6">
              <div className="flex items-center justify-between px-3 mb-2">
                <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
                  Playlists
                </h3>
                <button 
                  className="p-1 rounded-md hover:bg-gray-800 transition-colors"
                  title="Create playlist"
                >
                  <Plus className="h-4 w-4 text-gray-400" />
                </button>
              </div>
              <div className="space-y-1">
                {playlists.map((playlist) => (
                  <Link
                    key={playlist.id}
                    href={`/playlist/${playlist.id}`}
                    className="flex items-center justify-between px-3 py-2 text-sm text-gray-300 hover:bg-gray-800 hover:text-white rounded-md transition-colors group"
                    onClick={onClose}
                  >
                    <div>
                      <div className="font-medium">{playlist.name}</div>
                      <div className="text-xs text-gray-500">{playlist.tracks} tracks</div>
                    </div>
                    <ChevronRight className="h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                ))}
              </div>
            </div>
          </nav>

          {/* Settings */}
          <div className="border-t border-gray-800 p-4">
            <Link
              href="/settings"
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:bg-gray-800 hover:text-white rounded-md transition-colors"
              onClick={onClose}
            >
              <Settings className="h-5 w-5 mr-3" />
              Settings
            </Link>
          </div>
        </div>
      </aside>
    </>
  )
}
