// Dashboard Analytics Utilities
import { supabase } from './supabase'
import { 
  DashboardStats, 
  RecentlyPlayedTrack, 
  UploadHistoryItem, 
  FavoriteTrack, 
  PersonalizedRecommendation,
  ListeningSession,
  DashboardTimeFilter
} from '@/types/dashboard'
import { Track } from '@/types/database'
import { isDevelopmentMode } from './mockProfileData'

// Time filters for dashboard analytics
export const dashboardTimeFilters: DashboardTimeFilter[] = [
  { value: 'today', label: 'Today', days: 1 },
  { value: 'week', label: 'This Week', days: 7 },
  { value: 'month', label: 'This Month', days: 30 },
  { value: 'year', label: 'This Year', days: 365 },
  { value: 'all', label: 'All Time', days: 0 }
]

// Get dashboard statistics
export const getDashboardStats = async (userId: string, timeFilter: DashboardTimeFilter['value'] = 'week'): Promise<{ stats: DashboardStats | null; error: any }> => {
  if (isDevelopmentMode) {
    return mockDashboardAPI.getDashboardStats(userId, timeFilter)
  }

  try {
    const filter = dashboardTimeFilters.find(f => f.value === timeFilter)
    const daysAgo = filter?.days || 7
    const startDate = daysAgo > 0 ? new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000).toISOString() : null

    // Get total stats
    const [
      { count: totalUploads },
      { data: userTracks },
      { data: listeningHistory },
      { count: totalLikes }
    ] = await Promise.all([
      supabase.from('tracks').select('*', { count: 'exact', head: true }).eq('uploaded_by', userId),
      supabase.from('tracks').select('play_count').eq('uploaded_by', userId),
      supabase.from('listening_history').select('duration_listened').eq('user_id', userId),
      supabase.from('track_likes').select('*', { count: 'exact', head: true }).eq('user_id', userId)
    ])

    // Calculate totals
    const totalPlays = userTracks?.reduce((sum, track) => sum + (track.play_count || 0), 0) || 0
    const totalListeningTime = listeningHistory?.reduce((sum, entry) => sum + (entry.duration_listened || 0), 0) || 0

    // Get time-filtered stats
    let weeklyStats = { uploads: 0, plays: 0, likes: 0, listeningTime: 0 }
    
    if (startDate) {
      const [
        { count: weeklyUploads },
        { data: weeklyTracks },
        { data: weeklyListening },
        { count: weeklyLikes }
      ] = await Promise.all([
        supabase.from('tracks').select('*', { count: 'exact', head: true })
          .eq('uploaded_by', userId)
          .gte('created_at', startDate),
        supabase.from('tracks').select('play_count')
          .eq('uploaded_by', userId)
          .gte('created_at', startDate),
        supabase.from('listening_history').select('duration_listened')
          .eq('user_id', userId)
          .gte('listened_at', startDate),
        supabase.from('track_likes').select('*', { count: 'exact', head: true })
          .eq('user_id', userId)
          .gte('created_at', startDate)
      ])

      weeklyStats = {
        uploads: weeklyUploads || 0,
        plays: weeklyTracks?.reduce((sum, track) => sum + (track.play_count || 0), 0) || 0,
        likes: weeklyLikes || 0,
        listeningTime: weeklyListening?.reduce((sum, entry) => sum + (entry.duration_listened || 0), 0) || 0
      }
    }

    // Get favorite genre and AI tool
    const { data: genreStats } = await supabase
      .from('listening_history')
      .select('tracks(genre)')
      .eq('user_id', userId)
      .not('tracks.genre', 'is', null)

    const { data: aiToolStats } = await supabase
      .from('tracks')
      .select('ai_tool')
      .eq('uploaded_by', userId)
      .not('ai_tool', 'is', null)

    const favoriteGenre = getMostFrequent(genreStats?.map(item => item.tracks?.genre).filter(Boolean) || [])
    const favoriteAiTool = getMostFrequent(aiToolStats?.map(item => item.ai_tool).filter(Boolean) || [])

    // Calculate activity streak
    const streakDays = await calculateActivityStreak(userId)

    const stats: DashboardStats = {
      total_plays: totalPlays,
      total_uploads: totalUploads || 0,
      total_likes_received: totalLikes || 0,
      total_listening_time: totalListeningTime,
      tracks_uploaded_this_week: weeklyStats.uploads,
      plays_this_week: weeklyStats.plays,
      likes_this_week: weeklyStats.likes,
      listening_time_this_week: weeklyStats.listeningTime,
      favorite_genre: favoriteGenre,
      favorite_ai_tool: favoriteAiTool,
      streak_days: streakDays
    }

    return { stats, error: null }
  } catch (error) {
    console.error('Get dashboard stats error:', error)
    return { stats: null, error }
  }
}

// Get recently played tracks
export const getRecentlyPlayedTracks = async (userId: string, limit = 10, offset = 0): Promise<{ tracks: RecentlyPlayedTrack[] | null; error: any }> => {
  if (isDevelopmentMode) {
    return mockDashboardAPI.getRecentlyPlayedTracks(userId, limit, offset)
  }

  try {
    const { data, error } = await supabase
      .from('listening_history')
      .select(`
        *,
        tracks(*),
        playlists(name)
      `)
      .eq('user_id', userId)
      .order('listened_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    const tracks: RecentlyPlayedTrack[] = data?.map(item => ({
      ...item.tracks,
      listened_at: item.listened_at,
      duration_listened: item.duration_listened || 0,
      completed: item.completed || false,
      source: item.source || 'direct',
      playlist_name: item.playlists?.name
    })) || []

    return { tracks, error: null }
  } catch (error) {
    console.error('Get recently played tracks error:', error)
    return { tracks: null, error }
  }
}

// Get upload history
export const getUploadHistory = async (userId: string, limit = 10, offset = 0): Promise<{ uploads: UploadHistoryItem[] | null; error: any }> => {
  if (isDevelopmentMode) {
    return mockDashboardAPI.getUploadHistory(userId, limit, offset)
  }

  try {
    const { data, error } = await supabase
      .from('tracks')
      .select('*')
      .eq('uploaded_by', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    const uploads: UploadHistoryItem[] = data?.map(track => ({
      ...track,
      upload_status: track.upload_status || 'completed'
    })) || []

    return { uploads, error: null }
  } catch (error) {
    console.error('Get upload history error:', error)
    return { uploads: null, error }
  }
}

// Get favorite tracks
export const getFavoriteTracks = async (userId: string, limit = 10, offset = 0): Promise<{ favorites: FavoriteTrack[] | null; error: any }> => {
  if (isDevelopmentMode) {
    return mockDashboardAPI.getFavoriteTracks(userId, limit, offset)
  }

  try {
    const { data, error } = await supabase
      .from('track_likes')
      .select(`
        *,
        tracks(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error

    const favorites: FavoriteTrack[] = data?.map(item => ({
      ...item.tracks,
      liked_at: item.created_at,
      play_count_since_liked: 0 // TODO: Calculate plays since liked
    })) || []

    return { favorites, error: null }
  } catch (error) {
    console.error('Get favorite tracks error:', error)
    return { favorites: null, error }
  }
}

// Toggle track like/unlike
export const toggleTrackLike = async (userId: string, trackId: string): Promise<{ liked: boolean; error: any }> => {
  if (isDevelopmentMode) {
    return mockDashboardAPI.toggleTrackLike(userId, trackId)
  }

  try {
    // Check if already liked
    const { data: existingLike } = await supabase
      .from('track_likes')
      .select('id')
      .eq('user_id', userId)
      .eq('track_id', trackId)
      .single()

    if (existingLike) {
      // Unlike
      const { error } = await supabase
        .from('track_likes')
        .delete()
        .eq('user_id', userId)
        .eq('track_id', trackId)

      if (error) throw error

      // Update track like count
      await supabase.rpc('decrement_track_likes', { track_id: trackId })

      return { liked: false, error: null }
    } else {
      // Like
      const { error } = await supabase
        .from('track_likes')
        .insert({
          user_id: userId,
          track_id: trackId
        })

      if (error) throw error

      // Update track like count
      await supabase.rpc('increment_track_likes', { track_id: trackId })

      return { liked: true, error: null }
    }
  } catch (error) {
    console.error('Toggle track like error:', error)
    return { liked: false, error }
  }
}

// Get personalized recommendations
export const getPersonalizedRecommendations = async (userId: string, limit = 10): Promise<{ recommendations: PersonalizedRecommendation[] | null; error: any }> => {
  if (isDevelopmentMode) {
    return mockDashboardAPI.getPersonalizedRecommendations(userId, limit)
  }

  try {
    // Get user's listening history to understand preferences
    const { data: listeningHistory } = await supabase
      .from('listening_history')
      .select('tracks(genre, ai_tool, mood)')
      .eq('user_id', userId)
      .limit(50)

    // Get user's liked tracks
    const { data: likedTracks } = await supabase
      .from('track_likes')
      .select('tracks(genre, ai_tool, mood)')
      .eq('user_id', userId)
      .limit(20)

    // Analyze preferences
    const allPreferences = [
      ...(listeningHistory?.map(item => item.tracks) || []),
      ...(likedTracks?.map(item => item.tracks) || [])
    ].filter(Boolean)

    const preferredGenres = getMostFrequent(allPreferences.map(t => t.genre).filter(Boolean))
    const preferredAiTools = getMostFrequent(allPreferences.map(t => t.ai_tool).filter(Boolean))
    const preferredMoods = getMostFrequent(allPreferences.map(t => t.mood).filter(Boolean))

    // Get recommendations based on preferences
    const { data: recommendedTracks } = await supabase
      .from('tracks')
      .select('*')
      .eq('is_public', true)
      .neq('uploaded_by', userId) // Exclude user's own tracks
      .or(`genre.eq.${preferredGenres},ai_tool.eq.${preferredAiTools},mood.eq.${preferredMoods}`)
      .order('play_count', { ascending: false })
      .limit(limit)

    const recommendations: PersonalizedRecommendation[] = recommendedTracks?.map(track => ({
      track,
      reason: track.genre === preferredGenres ? 'similar_genre' : 
              track.ai_tool === preferredAiTools ? 'similar_artist' :
              track.mood === preferredMoods ? 'similar_mood' : 'trending',
      confidence_score: 0.8,
      explanation: `Based on your listening history, you might enjoy this ${track.genre} track.`
    })) || []

    return { recommendations, error: null }
  } catch (error) {
    console.error('Get personalized recommendations error:', error)
    return { recommendations: null, error }
  }
}

// Get recently created playlists
export const getRecentPlaylists = async (userId: string, limit = 5): Promise<{ playlists: any[] | null; error: any }> => {
  if (isDevelopmentMode) {
    return mockDashboardAPI.getRecentPlaylists(userId, limit)
  }

  try {
    const { data, error } = await supabase
      .from('playlists')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return { playlists: data, error: null }
  } catch (error) {
    console.error('Get recent playlists error:', error)
    return { playlists: null, error }
  }
}

// Record listening activity
export const recordListeningActivity = async (
  userId: string, 
  trackId: string, 
  durationListened: number, 
  completed: boolean,
  source: string = 'direct',
  playlistId?: string
): Promise<{ error: any }> => {
  if (isDevelopmentMode) {
    return { error: null }
  }

  try {
    const { error } = await supabase
      .from('listening_history')
      .insert({
        user_id: userId,
        track_id: trackId,
        duration_listened: durationListened,
        completed,
        source,
        playlist_id: playlistId
      })

    if (error) throw error

    // Update track play count if completed
    if (completed) {
      await supabase.rpc('increment_track_plays', { track_id: trackId })
    }

    return { error: null }
  } catch (error) {
    console.error('Record listening activity error:', error)
    return { error }
  }
}

// Helper functions
const getMostFrequent = (arr: string[]): string | null => {
  if (arr.length === 0) return null
  
  const frequency: Record<string, number> = {}
  arr.forEach(item => {
    frequency[item] = (frequency[item] || 0) + 1
  })
  
  return Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b)
}

const calculateActivityStreak = async (userId: string): Promise<number> => {
  try {
    const { data } = await supabase
      .from('listening_history')
      .select('listened_at')
      .eq('user_id', userId)
      .order('listened_at', { ascending: false })
      .limit(30)

    if (!data || data.length === 0) return 0

    const dates = data.map(item => new Date(item.listened_at).toDateString())
    const uniqueDates = [...new Set(dates)]
    
    let streak = 0
    const today = new Date().toDateString()
    
    for (let i = 0; i < uniqueDates.length; i++) {
      const expectedDate = new Date(Date.now() - i * 24 * 60 * 60 * 1000).toDateString()
      if (uniqueDates.includes(expectedDate)) {
        streak++
      } else {
        break
      }
    }
    
    return streak
  } catch (error) {
    console.error('Calculate activity streak error:', error)
    return 0
  }
}

// Mock API for development
const mockDashboardAPI = {
  getDashboardStats: async (userId: string, timeFilter: string) => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      stats: {
        total_plays: 15420,
        total_uploads: 42,
        total_likes_received: 892,
        total_listening_time: 86400,
        tracks_uploaded_this_week: 3,
        plays_this_week: 1250,
        likes_this_week: 67,
        listening_time_this_week: 7200,
        favorite_genre: 'electronic',
        favorite_ai_tool: 'suno',
        streak_days: 7
      },
      error: null
    }
  },

  getRecentlyPlayedTracks: async (userId: string, limit: number, offset: number) => {
    await new Promise(resolve => setTimeout(resolve, 400))
    const mockTracks = Array.from({ length: limit }, (_, i) => ({
      id: `recent_${offset + i + 1}`,
      title: `Recently Played Track ${offset + i + 1}`,
      artist_name: 'AI Artist',
      genre: 'electronic',
      ai_tool: 'suno',
      duration: 180,
      listened_at: new Date(Date.now() - (offset + i) * 60 * 60 * 1000).toISOString(),
      duration_listened: 150,
      completed: true,
      source: 'playlist' as const,
      playlist_name: 'Chill Vibes',
      file_url: null,
      file_path: null,
      file_size: 5000000,
      mood: 'chill',
      tempo: 120,
      key_signature: 'C',
      ai_prompt: 'Create a chill electronic track',
      ai_parameters: {},
      is_public: true,
      is_featured: false,
      play_count: 100,
      like_count: 25,
      upload_status: 'completed',
      uploaded_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }))
    return { tracks: mockTracks, error: null }
  },

  getUploadHistory: async (userId: string, limit: number, offset: number) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    const mockUploads = Array.from({ length: limit }, (_, i) => ({
      id: `upload_${offset + i + 1}`,
      title: `My Track ${offset + i + 1}`,
      artist_name: 'You',
      genre: 'ambient',
      ai_tool: 'udio',
      duration: 200,
      upload_status: 'completed' as const,
      file_url: null,
      file_path: null,
      file_size: 8000000,
      mood: 'creative',
      tempo: 100,
      key_signature: 'D',
      ai_prompt: 'Create an ambient track',
      ai_parameters: {},
      is_public: true,
      is_featured: false,
      play_count: 50,
      like_count: 12,
      uploaded_by: userId,
      created_at: new Date(Date.now() - (offset + i) * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString()
    }))
    return { uploads: mockUploads, error: null }
  },

  getFavoriteTracks: async (userId: string, limit: number, offset: number) => {
    await new Promise(resolve => setTimeout(resolve, 350))
    const mockFavorites = Array.from({ length: limit }, (_, i) => ({
      id: `favorite_${offset + i + 1}`,
      title: `Favorite Track ${offset + i + 1}`,
      artist_name: 'Popular Artist',
      genre: 'jazz',
      ai_tool: 'aiva',
      duration: 240,
      liked_at: new Date(Date.now() - (offset + i) * 12 * 60 * 60 * 1000).toISOString(),
      play_count_since_liked: 15,
      file_url: null,
      file_path: null,
      file_size: 6000000,
      mood: 'uplifting',
      tempo: 140,
      key_signature: 'G',
      ai_prompt: 'Create an uplifting jazz track',
      ai_parameters: {},
      is_public: true,
      is_featured: true,
      play_count: 500,
      like_count: 89,
      upload_status: 'completed',
      uploaded_by: 'other_user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }))
    return { favorites: mockFavorites, error: null }
  },

  toggleTrackLike: async (userId: string, trackId: string) => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return { liked: Math.random() > 0.5, error: null }
  },

  getPersonalizedRecommendations: async (userId: string, limit: number) => {
    await new Promise(resolve => setTimeout(resolve, 600))
    const mockRecommendations = Array.from({ length: limit }, (_, i) => ({
      track: {
        id: `rec_${i + 1}`,
        title: `Recommended Track ${i + 1}`,
        artist_name: 'AI Recommendation',
        genre: 'experimental',
        ai_tool: 'mubert',
        duration: 190,
        file_url: null,
        file_path: null,
        file_size: 7000000,
        mood: 'dreamy',
        tempo: 110,
        key_signature: 'F',
        ai_prompt: 'Create a dreamy experimental track',
        ai_parameters: {},
        is_public: true,
        is_featured: false,
        play_count: 200,
        like_count: 45,
        upload_status: 'completed',
        uploaded_by: 'ai_user',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      reason: ['similar_genre', 'similar_mood', 'trending'][i % 3] as any,
      confidence_score: 0.8 + Math.random() * 0.2,
      explanation: `Based on your love for ${['electronic', 'ambient', 'jazz'][i % 3]} music, you might enjoy this track.`
    }))
    return { recommendations: mockRecommendations, error: null }
  },

  getRecentPlaylists: async (userId: string, limit: number) => {
    await new Promise(resolve => setTimeout(resolve, 250))
    const mockPlaylists = Array.from({ length: limit }, (_, i) => ({
      id: `playlist_${i + 1}`,
      name: `My Playlist ${i + 1}`,
      description: `A collection of my favorite tracks`,
      cover_image_url: null,
      user_id: userId,
      is_public: i < 2,
      is_collaborative: false,
      track_count: 15 + i * 5,
      total_duration: 3600 + i * 600,
      created_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString()
    }))
    return { playlists: mockPlaylists, error: null }
  }
} 