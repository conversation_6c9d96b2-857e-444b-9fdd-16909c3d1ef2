import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { useNetworkStatus } from './useNetworkStatus'

export interface AudioQuality {
  bitrate: number
  format: string
  label: string
  size: 'low' | 'medium' | 'high' | 'lossless'
}

export interface AudioSource {
  src: string
  quality: AudioQuality
  type: string
}

export interface OptimizedAudioState {
  isLoading: boolean
  isPlaying: boolean
  isPaused: boolean
  duration: number
  currentTime: number
  volume: number
  isMuted: boolean
  buffered: TimeRanges | null
  error: string | null
  canPlay: boolean
  isBuffering: boolean
  networkQuality: 'slow' | 'medium' | 'fast'
  selectedQuality: AudioQuality
  availableQualities: AudioQuality[]
}

export interface UseOptimizedAudioOptions {
  preload?: 'none' | 'metadata' | 'auto'
  autoQuality?: boolean
  enableCompression?: boolean
  crossOrigin?: 'anonymous' | 'use-credentials'
  onLoadStart?: () => void
  onCanPlay?: () => void
  onLoadedData?: () => void
  onProgress?: (buffered: TimeRanges) => void
  onTimeUpdate?: (currentTime: number) => void
  onEnded?: () => void
  onError?: (error: string) => void
  onQualityChange?: (quality: AudioQuality) => void
}

const AUDIO_QUALITIES: AudioQuality[] = [
  { bitrate: 64, format: 'mp3', label: 'Low (64kbps)', size: 'low' },
  { bitrate: 128, format: 'mp3', label: 'Medium (128kbps)', size: 'medium' },
  { bitrate: 320, format: 'mp3', label: 'High (320kbps)', size: 'high' },
  { bitrate: 1411, format: 'flac', label: 'Lossless (FLAC)', size: 'lossless' }
]

export function useOptimizedAudio(
  sources: AudioSource[] | string,
  options: UseOptimizedAudioOptions = {}
) {
  const {
    preload = 'metadata',
    autoQuality = true,
    enableCompression = true,
    crossOrigin = 'anonymous',
    onLoadStart,
    onCanPlay,
    onLoadedData,
    onProgress,
    onTimeUpdate,
    onEnded,
    onError,
    onQualityChange
  } = options

  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [state, setState] = useState<OptimizedAudioState>({
    isLoading: false,
    isPlaying: false,
    isPaused: false,
    duration: 0,
    currentTime: 0,
    volume: 1,
    isMuted: false,
    buffered: null,
    error: null,
    canPlay: false,
    isBuffering: false,
    networkQuality: 'medium',
    selectedQuality: AUDIO_QUALITIES[1], // Default to medium quality
    availableQualities: AUDIO_QUALITIES
  })

  const { isOnline, effectiveType, downlink } = useNetworkStatus()

  // Determine optimal quality based on network conditions
  const getOptimalQuality = useCallback(() => {
    if (!autoQuality) return state.selectedQuality

    const connection = (navigator as any).connection
    const networkSpeed = connection?.downlink || downlink || 1

    if (networkSpeed < 0.5 || effectiveType === 'slow-2g' || effectiveType === '2g') {
      return AUDIO_QUALITIES[0] // Low quality
    } else if (networkSpeed < 2 || effectiveType === '3g') {
      return AUDIO_QUALITIES[1] // Medium quality
    } else if (networkSpeed < 10 || effectiveType === '4g') {
      return AUDIO_QUALITIES[2] // High quality
    } else {
      return AUDIO_QUALITIES[3] // Lossless quality
    }
  }, [autoQuality, state.selectedQuality, downlink, effectiveType])

  // Generate audio sources based on quality
  const audioSources = useMemo(() => {
    if (typeof sources === 'string') {
      return AUDIO_QUALITIES.map(quality => ({
        src: sources,
        quality,
        type: `audio/${quality.format}`
      }))
    }
    return sources
  }, [sources])

  // Get current audio source
  const currentSource = useMemo(() => {
    return audioSources.find(source => 
      source.quality.bitrate === state.selectedQuality.bitrate
    ) || audioSources[0]
  }, [audioSources, state.selectedQuality])

  // Initialize audio element
  useEffect(() => {
    if (!audioRef.current) {
      audioRef.current = new Audio()
      audioRef.current.preload = preload
      audioRef.current.crossOrigin = crossOrigin

      // Enable compression if supported
      if (enableCompression && 'mediaSession' in navigator) {
        try {
          // Enable hardware acceleration
          audioRef.current.setAttribute('playsinline', 'true')
        } catch (e) {
          console.warn('Hardware acceleration not supported:', e)
        }
      }
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current = null
      }
    }
  }, [preload, crossOrigin, enableCompression])

  // Set up event listeners
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const handleLoadStart = () => {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      onLoadStart?.()
    }

    const handleCanPlay = () => {
      setState(prev => ({ ...prev, canPlay: true, isLoading: false }))
      onCanPlay?.()
    }

    const handleLoadedData = () => {
      setState(prev => ({ 
        ...prev, 
        duration: audio.duration,
        isLoading: false 
      }))
      onLoadedData?.()
    }

    const handleProgress = () => {
      setState(prev => ({ ...prev, buffered: audio.buffered }))
      onProgress?.(audio.buffered)
    }

    const handleTimeUpdate = () => {
      setState(prev => ({ ...prev, currentTime: audio.currentTime }))
      onTimeUpdate?.(audio.currentTime)
    }

    const handlePlay = () => {
      setState(prev => ({ ...prev, isPlaying: true, isPaused: false }))
    }

    const handlePause = () => {
      setState(prev => ({ ...prev, isPlaying: false, isPaused: true }))
    }

    const handleEnded = () => {
      setState(prev => ({ 
        ...prev, 
        isPlaying: false, 
        isPaused: false,
        currentTime: 0 
      }))
      onEnded?.()
    }

    const handleError = () => {
      const errorMessage = audio.error?.message || 'Audio playback error'
      setState(prev => ({ 
        ...prev, 
        error: errorMessage,
        isLoading: false,
        isPlaying: false 
      }))
      onError?.(errorMessage)
    }

    const handleWaiting = () => {
      setState(prev => ({ ...prev, isBuffering: true }))
    }

    const handleCanPlayThrough = () => {
      setState(prev => ({ ...prev, isBuffering: false }))
    }

    const handleVolumeChange = () => {
      setState(prev => ({ 
        ...prev, 
        volume: audio.volume,
        isMuted: audio.muted 
      }))
    }

    // Add event listeners
    audio.addEventListener('loadstart', handleLoadStart)
    audio.addEventListener('canplay', handleCanPlay)
    audio.addEventListener('loadeddata', handleLoadedData)
    audio.addEventListener('progress', handleProgress)
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('play', handlePlay)
    audio.addEventListener('pause', handlePause)
    audio.addEventListener('ended', handleEnded)
    audio.addEventListener('error', handleError)
    audio.addEventListener('waiting', handleWaiting)
    audio.addEventListener('canplaythrough', handleCanPlayThrough)
    audio.addEventListener('volumechange', handleVolumeChange)

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart)
      audio.removeEventListener('canplay', handleCanPlay)
      audio.removeEventListener('loadeddata', handleLoadedData)
      audio.removeEventListener('progress', handleProgress)
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('play', handlePlay)
      audio.removeEventListener('pause', handlePause)
      audio.removeEventListener('ended', handleEnded)
      audio.removeEventListener('error', handleError)
      audio.removeEventListener('waiting', handleWaiting)
      audio.removeEventListener('canplaythrough', handleCanPlayThrough)
      audio.removeEventListener('volumechange', handleVolumeChange)
    }
  }, [onLoadStart, onCanPlay, onLoadedData, onProgress, onTimeUpdate, onEnded, onError])

  // Load audio source
  useEffect(() => {
    const audio = audioRef.current
    if (!audio || !currentSource) return

    const wasPlaying = !audio.paused
    const currentTime = audio.currentTime

    audio.src = currentSource.src
    
    // Restore playback position and state
    if (currentTime > 0) {
      audio.currentTime = currentTime
    }
    
    if (wasPlaying) {
      audio.play().catch(console.error)
    }

    setState(prev => ({ ...prev, selectedQuality: currentSource.quality }))
    onQualityChange?.(currentSource.quality)
  }, [currentSource, onQualityChange])

  // Auto-adjust quality based on network
  useEffect(() => {
    if (autoQuality) {
      const optimalQuality = getOptimalQuality()
      if (optimalQuality.bitrate !== state.selectedQuality.bitrate) {
        setState(prev => ({ ...prev, selectedQuality: optimalQuality }))
      }
    }
  }, [autoQuality, getOptimalQuality, state.selectedQuality.bitrate])

  // Control functions
  const play = useCallback(async () => {
    if (!audioRef.current) return
    
    try {
      await audioRef.current.play()
    } catch (error) {
      console.error('Playback failed:', error)
      setState(prev => ({ 
        ...prev, 
        error: 'Playback failed. Please try again.' 
      }))
    }
  }, [])

  const pause = useCallback(() => {
    if (!audioRef.current) return
    audioRef.current.pause()
  }, [])

  const seek = useCallback((time: number) => {
    if (!audioRef.current) return
    audioRef.current.currentTime = Math.max(0, Math.min(time, audioRef.current.duration))
  }, [])

  const setVolume = useCallback((volume: number) => {
    if (!audioRef.current) return
    const clampedVolume = Math.max(0, Math.min(1, volume))
    audioRef.current.volume = clampedVolume
  }, [])

  const mute = useCallback(() => {
    if (!audioRef.current) return
    audioRef.current.muted = true
  }, [])

  const unmute = useCallback(() => {
    if (!audioRef.current) return
    audioRef.current.muted = false
  }, [])

  const changeQuality = useCallback((quality: AudioQuality) => {
    setState(prev => ({ ...prev, selectedQuality: quality }))
  }, [])

  // Calculate buffer percentage
  const bufferPercentage = useMemo(() => {
    if (!state.buffered || !state.duration) return 0
    
    const buffered = state.buffered
    let bufferedEnd = 0
    
    for (let i = 0; i < buffered.length; i++) {
      if (buffered.start(i) <= state.currentTime && state.currentTime <= buffered.end(i)) {
        bufferedEnd = buffered.end(i)
        break
      }
    }
    
    return (bufferedEnd / state.duration) * 100
  }, [state.buffered, state.duration, state.currentTime])

  return {
    ...state,
    bufferPercentage,
    isOnline,
    controls: {
      play,
      pause,
      seek,
      setVolume,
      mute,
      unmute,
      changeQuality
    },
    audioRef
  }
} 