'use client'

import React, { useState, useEffect } from 'react'
import { X, Search, Plus, Music, Check } from 'lucide-react'
import { useEnhancedPlaylist } from '@/contexts/EnhancedPlaylistContext'
import { AudioTrack } from '@/types/audio'
import { CreatePlaylistData } from '@/types/playlist'

interface AddToPlaylistModalProps {
  isOpen: boolean
  onClose: () => void
  track: AudioTrack
}

export default function AddToPlaylistModal({ isOpen, onClose, track }: AddToPlaylistModalProps) {
  const {
    playlists,
    loading,
    error,
    getUserPlaylists,
    createPlaylist,
    addTrackToPlaylist,
    isTrackInPlaylist,
    clearError
  } = useEnhancedPlaylist()

  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newPlaylistName, setNewPlaylistName] = useState('')
  const [newPlaylistDescription, setNewPlaylistDescription] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const [addingToPlaylists, setAddingToPlaylists] = useState<Set<string>>(new Set())
  const [trackInPlaylists, setTrackInPlaylists] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (isOpen) {
      getUserPlaylists()
      clearError()
      setSearchTerm('')
      setShowCreateForm(false)
      setNewPlaylistName('')
      setNewPlaylistDescription('')
      setAddingToPlaylists(new Set())
      checkTrackInPlaylists()
    }
  }, [isOpen, getUserPlaylists, clearError])

  const checkTrackInPlaylists = async () => {
    const playlistsWithTrack = new Set<string>()
    
    for (const playlist of playlists) {
      try {
        const hasTrack = await isTrackInPlaylist(playlist.id, track.id)
        if (hasTrack) {
          playlistsWithTrack.add(playlist.id)
        }
      } catch (error) {
        console.error('Error checking track in playlist:', error)
      }
    }
    
    setTrackInPlaylists(playlistsWithTrack)
  }

  const filteredPlaylists = playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    playlist.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddToPlaylist = async (playlistId: string) => {
    if (trackInPlaylists.has(playlistId)) return

    setAddingToPlaylists(prev => new Set(prev).add(playlistId))
    
    try {
      await addTrackToPlaylist({
        playlist_id: playlistId,
        track_id: track.id
      })
      
      setTrackInPlaylists(prev => new Set(prev).add(playlistId))
    } catch (error) {
      console.error('Failed to add track to playlist:', error)
    } finally {
      setAddingToPlaylists(prev => {
        const newSet = new Set(prev)
        newSet.delete(playlistId)
        return newSet
      })
    }
  }

  const handleCreatePlaylist = async () => {
    if (!newPlaylistName.trim()) return

    setIsCreating(true)
    
    try {
      const playlistData: CreatePlaylistData = {
        name: newPlaylistName.trim(),
        description: newPlaylistDescription.trim() || undefined,
        is_public: false
      }
      
      const newPlaylist = await createPlaylist(playlistData)
      
      // Add track to the new playlist
      await addTrackToPlaylist({
        playlist_id: newPlaylist.id,
        track_id: track.id
      })
      
      setShowCreateForm(false)
      setNewPlaylistName('')
      setNewPlaylistDescription('')
      
      // Refresh playlists to show the new one
      await getUserPlaylists()
    } catch (error) {
      console.error('Failed to create playlist:', error)
    } finally {
      setIsCreating(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg w-full max-w-md max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Add to Playlist</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Track Info */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
              <Music className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-white font-medium truncate">{track.title}</h3>
              <p className="text-gray-400 text-sm truncate">{track.artist}</p>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search playlists..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Create New Playlist */}
          <div className="p-4 border-b border-gray-700">
            {!showCreateForm ? (
              <button
                onClick={() => setShowCreateForm(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
              >
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                  <Plus className="w-5 h-5 text-white" />
                </div>
                <span className="text-white font-medium">Create New Playlist</span>
              </button>
            ) : (
              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Playlist name"
                  value={newPlaylistName}
                  onChange={(e) => setNewPlaylistName(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  autoFocus
                />
                <textarea
                  placeholder="Description (optional)"
                  value={newPlaylistDescription}
                  onChange={(e) => setNewPlaylistDescription(e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleCreatePlaylist}
                    disabled={!newPlaylistName.trim() || isCreating}
                    className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors font-medium"
                  >
                    {isCreating ? 'Creating...' : 'Create & Add'}
                  </button>
                  <button
                    onClick={() => setShowCreateForm(false)}
                    className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Playlist List */}
          <div className="p-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-400 mb-2">Failed to load playlists</p>
                <button
                  onClick={() => getUserPlaylists()}
                  className="text-purple-400 hover:text-purple-300 transition-colors"
                >
                  Try again
                </button>
              </div>
            ) : filteredPlaylists.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-400">
                  {searchTerm ? 'No playlists found' : 'No playlists yet'}
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredPlaylists.map((playlist) => {
                  const isAdding = addingToPlaylists.has(playlist.id)
                  const hasTrack = trackInPlaylists.has(playlist.id)
                  
                  return (
                    <button
                      key={playlist.id}
                      onClick={() => handleAddToPlaylist(playlist.id)}
                      disabled={isAdding || hasTrack}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                        hasTrack
                          ? 'bg-green-900/20 border border-green-500/30'
                          : 'bg-gray-700 hover:bg-gray-600'
                      }`}
                    >
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                        <Music className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1 text-left min-w-0">
                        <h3 className="text-white font-medium truncate">{playlist.name}</h3>
                        <p className="text-gray-400 text-sm">
                          {playlist.track_count || 0} tracks
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        {isAdding ? (
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-500"></div>
                        ) : hasTrack ? (
                          <Check className="w-5 h-5 text-green-400" />
                        ) : (
                          <Plus className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                    </button>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 