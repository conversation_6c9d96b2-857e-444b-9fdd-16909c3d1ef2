#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import os from 'os';

const CURSOR_CONFIG_PATHS = {
  win32: path.join(os.homedir(), 'AppData', 'Roaming', 'Cursor', 'User', 'settings.json'),
  darwin: path.join(os.homedir(), 'Library', 'Application Support', 'Cursor', 'User', 'settings.json'),
  linux: path.join(os.homedir(), '.config', 'Cursor', 'User', 'settings.json')
};

const MCP_CONFIG_PATHS = {
  cursor: {
    win32: path.join(os.homedir(), '.cursor', 'mcp.json'),
    darwin: path.join(os.homedir(), '.cursor', 'mcp.json'),
    linux: path.join(os.homedir(), '.cursor', 'mcp.json')
  }
};

async function setupContext7() {
  console.log('📚 Setting up Context7 MCP Server for Tunami...\n');
  
  const platform = os.platform();
  console.log(`🖥️  Platform: ${platform}`);

  // Context7 MCP configuration
  const context7Config = {
    "mcpServers": {
      "context7": {
        "command": "npx",
        "args": ["-y", "@upstash/context7-mcp@latest"],
        "description": "Up-to-date documentation and code examples for libraries"
      }
    }
  };

  // Alternative configurations for different runtimes
  const alternatives = {
    bun: {
      "command": "bunx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    },
    deno: {
      "command": "deno",
      "args": ["run", "--allow-net", "npm:@upstash/context7-mcp"]
    }
  };

  try {
    // Setup for Cursor (mcp.json approach)
    const cursorMcpPath = MCP_CONFIG_PATHS.cursor[platform];
    
    if (cursorMcpPath) {
      console.log(`📁 Setting up Cursor MCP config: ${cursorMcpPath}`);
      
      // Ensure directory exists
      await fs.mkdir(path.dirname(cursorMcpPath), { recursive: true });
      
      // Read existing config or create new one
      let existingConfig = { mcpServers: {} };
      try {
        const configContent = await fs.readFile(cursorMcpPath, 'utf-8');
        existingConfig = JSON.parse(configContent);
        if (!existingConfig.mcpServers) {
          existingConfig.mcpServers = {};
        }
      } catch (error) {
        console.log('📝 Creating new MCP configuration...');
      }

      // Add Context7 to existing config
      existingConfig.mcpServers.context7 = context7Config.mcpServers.context7;

      // Write updated configuration
      await fs.writeFile(cursorMcpPath, JSON.stringify(existingConfig, null, 2), 'utf-8');
      console.log('✅ Context7 added to Cursor MCP configuration');
    }

    console.log('\n🎉 Context7 MCP Server setup complete!');
    console.log('\n🔧 Next steps:');
    console.log('1. Restart Cursor IDE');
    console.log('2. Test Context7 with a prompt like:');
    console.log('   "Create a React component with hooks. use context7"');
    console.log('   "Show me how to use Next.js app router. use context7"');
    console.log('   "Write a Supabase query with TypeScript. use context7"');

    console.log('\n💡 Context7 Features:');
    console.log('- ✅ Up-to-date documentation for any library');
    console.log('- ✅ Version-specific code examples');
    console.log('- ✅ No more outdated or hallucinated APIs');
    console.log('- ✅ Works with React, Next.js, Supabase, TypeScript, and more');

    console.log('\n🔧 Alternative Runtime Configurations:');
    console.log('\nIf you encounter issues with npx, try these alternatives:');
    console.log('\n📦 Using Bun:');
    console.log(JSON.stringify({
      mcpServers: {
        context7: alternatives.bun
      }
    }, null, 2));
    
    console.log('\n🦕 Using Deno:');
    console.log(JSON.stringify({
      mcpServers: {
        context7: alternatives.deno
      }
    }, null, 2));

  } catch (error) {
    console.error('❌ Error setting up Context7:', error.message);
    console.log('\n🔧 Manual setup for Cursor:');
    console.log('Add this to ~/.cursor/mcp.json:');
    console.log(JSON.stringify(context7Config, null, 2));
  }
}

// Check Node.js version
function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    console.warn('⚠️  Warning: Context7 requires Node.js v18 or higher');
    console.warn(`   Current version: ${nodeVersion}`);
    console.warn('   Please upgrade Node.js for optimal compatibility');
  } else {
    console.log(`✅ Node.js version: ${nodeVersion} (compatible)`);
  }
}

// Main setup function
async function main() {
  try {
    checkNodeVersion();
    await setupContext7();
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

main(); 