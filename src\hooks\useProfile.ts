// Profile Management Hook
'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { 
  getUserProfile, 
  updateUserProfile, 
  getUserStats, 
  getUserTracks, 
  getUserPlaylists,
  uploadProfileImage,
  deleteProfileImage
} from '@/lib/profile'
import { ExtendedProfile, UserStats } from '@/types/profile'
import { Track, Playlist } from '@/types/database'
import { isDevelopmentMode } from '@/lib/mockProfileData'

export const useProfile = (userId?: string) => {
  const { user } = useAuth()
  const targetUserId = userId || user?.id
  
  const [profile, setProfile] = useState<ExtendedProfile | null>(null)
  const [stats, setStats] = useState<UserStats | null>(null)
  const [tracks, setTracks] = useState<Track[]>([])
  const [playlists, setPlaylists] = useState<Playlist[]>([])
  
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const [tracksOffset, setTracksOffset] = useState(0)
  const [playlistsOffset, setPlaylistsOffset] = useState(0)
  const [hasMoreTracks, setHasMoreTracks] = useState(true)
  const [hasMorePlaylists, setHasMorePlaylists] = useState(true)

  const isOwnProfile = user?.id === targetUserId

  // Load initial profile data
  const loadProfile = async () => {
    if (!targetUserId) return

    try {
      setLoading(true)
      setError(null)

      // Load profile, stats, tracks, and playlists in parallel
      const [profileResult, statsResult, tracksResult, playlistsResult] = await Promise.all([
        getUserProfile(targetUserId),
        getUserStats(targetUserId),
        getUserTracks(targetUserId, 10, 0),
        getUserPlaylists(targetUserId, 10, 0)
      ])

      if (profileResult.error) throw new Error(profileResult.error)
      if (statsResult.error) throw new Error(statsResult.error)
      if (tracksResult.error) throw new Error(tracksResult.error)
      if (playlistsResult.error) throw new Error(playlistsResult.error)

      setProfile(profileResult.profile)
      setStats(statsResult.stats)
      setTracks(tracksResult.tracks || [])
      setPlaylists(playlistsResult.playlists || [])
      
      setTracksOffset(10)
      setPlaylistsOffset(10)
      setHasMoreTracks((tracksResult.tracks?.length || 0) === 10)
      setHasMorePlaylists((playlistsResult.playlists?.length || 0) === 10)
    } catch (err: any) {
      setError(err.message || 'Failed to load profile')
    } finally {
      setLoading(false)
    }
  }

  // Update profile
  const updateProfile = async (updates: Partial<ExtendedProfile>) => {
    if (!targetUserId) return { success: false, error: 'No user ID' }

    try {
      setUpdating(true)
      setError(null)

      const result = await updateUserProfile(targetUserId, updates)
      
      if (result.error) throw new Error(result.error)
      
      setProfile(result.profile)
      return { success: true, error: null }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update profile'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setUpdating(false)
    }
  }

  // Upload avatar
  const uploadAvatar = async (file: File) => {
    if (!targetUserId) return { success: false, error: 'No user ID' }

    try {
      setUploading(true)
      setError(null)

      // Delete old avatar if exists
      if (profile?.avatar_url) {
        await deleteProfileImage(profile.avatar_url)
      }

      // Upload new avatar
      const uploadResult = await uploadProfileImage(file, targetUserId)
      
      if (uploadResult.error) throw new Error(uploadResult.error)

      // Update profile with new avatar URL
      const updateResult = await updateUserProfile(targetUserId, {
        avatar_url: uploadResult.url
      })

      if (updateResult.error) throw new Error(updateResult.error)

      setProfile(updateResult.profile)
      return { success: true, error: null }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to upload avatar'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setUploading(false)
    }
  }

  // Load more tracks
  const loadMoreTracks = async () => {
    if (!targetUserId || !hasMoreTracks) return

    try {
      const result = await getUserTracks(targetUserId, 10, tracksOffset)
      
      if (result.error) throw new Error(result.error)
      
      const newTracks = result.tracks || []
      setTracks(prev => [...prev, ...newTracks])
      setTracksOffset(prev => prev + 10)
      setHasMoreTracks(newTracks.length === 10)
    } catch (err: any) {
      setError(err.message || 'Failed to load more tracks')
    }
  }

  // Load more playlists
  const loadMorePlaylists = async () => {
    if (!targetUserId || !hasMorePlaylists) return

    try {
      const result = await getUserPlaylists(targetUserId, 10, playlistsOffset)
      
      if (result.error) throw new Error(result.error)
      
      const newPlaylists = result.playlists || []
      setPlaylists(prev => [...prev, ...newPlaylists])
      setPlaylistsOffset(prev => prev + 10)
      setHasMorePlaylists(newPlaylists.length === 10)
    } catch (err: any) {
      setError(err.message || 'Failed to load more playlists')
    }
  }

  // Refresh all data
  const refresh = () => {
    setTracksOffset(0)
    setPlaylistsOffset(0)
    setHasMoreTracks(true)
    setHasMorePlaylists(true)
    loadProfile()
  }

  // Load profile when user changes
  useEffect(() => {
    if (targetUserId) {
      loadProfile()
    }
  }, [targetUserId])

  return {
    profile,
    stats,
    tracks,
    playlists,
    loading,
    updating,
    uploading,
    error,
    hasMoreTracks,
    hasMorePlaylists,
    isOwnProfile,
    updateProfile,
    uploadAvatar,
    loadMoreTracks,
    loadMorePlaylists,
    refresh
  }
}

// Hook for profile image preview
export const useProfileImagePreview = () => {
  const [preview, setPreview] = useState<string | null>(null)
  const [file, setFile] = useState<File | null>(null)

  const handleFileSelect = (selectedFile: File) => {
    setFile(selectedFile)
    
    // Create preview URL
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(selectedFile)
  }

  const clearPreview = () => {
    setPreview(null)
    setFile(null)
  }

  return {
    preview,
    file,
    handleFileSelect,
    clearPreview
  }
} 