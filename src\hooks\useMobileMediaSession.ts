'use client'

import { useEffect } from 'react'
import { AudioTrack } from '@/types/audio'

interface MediaSessionConfig {
  track?: AudioTrack
  isPlaying: boolean
  onPlay: () => void
  onPause: () => void
  onNext?: () => void
  onPrevious?: () => void
  onSeek?: (time: number) => void
}

export function useMobileMediaSession({
  track,
  isPlaying,
  onPlay,
  onPause,
  onNext,
  onPrevious,
  onSeek
}: MediaSessionConfig) {
  useEffect(() => {
    // Check if Media Session API is supported
    if (!('mediaSession' in navigator)) {
      console.warn('Media Session API not supported')
      return
    }

    if (!track) {
      // Clear media session when no track
      navigator.mediaSession.metadata = null
      return
    }

    // Set media metadata for lock screen display
    navigator.mediaSession.metadata = new MediaMetadata({
      title: track.title,
      artist: track.artist,
      album: track.album || 'Tunami',
      artwork: [
        {
          src: track.coverArt || '/images/default-artwork-96.png',
          sizes: '96x96',
          type: 'image/png'
        },
        {
          src: track.coverArt || '/images/default-artwork-128.png',
          sizes: '128x128',
          type: 'image/png'
        },
        {
          src: track.coverArt || '/images/default-artwork-192.png',
          sizes: '192x192',
          type: 'image/png'
        },
        {
          src: track.coverArt || '/images/default-artwork-256.png',
          sizes: '256x256',
          type: 'image/png'
        },
        {
          src: track.coverArt || '/images/default-artwork-384.png',
          sizes: '384x384',
          type: 'image/png'
        },
        {
          src: track.coverArt || '/images/default-artwork-512.png',
          sizes: '512x512',
          type: 'image/png'
        }
      ]
    })

    // Set playback state
    navigator.mediaSession.playbackState = isPlaying ? 'playing' : 'paused'

    // Set up action handlers
    navigator.mediaSession.setActionHandler('play', () => {
      onPlay()
    })

    navigator.mediaSession.setActionHandler('pause', () => {
      onPause()
    })

    if (onNext) {
      navigator.mediaSession.setActionHandler('nexttrack', () => {
        onNext()
      })
    }

    if (onPrevious) {
      navigator.mediaSession.setActionHandler('previoustrack', () => {
        onPrevious()
      })
    }

    if (onSeek) {
      navigator.mediaSession.setActionHandler('seekto', (details) => {
        if (details.seekTime !== undefined) {
          onSeek(details.seekTime)
        }
      })

      navigator.mediaSession.setActionHandler('seekforward', (details) => {
        const seekOffset = details.seekOffset || 10
        // This would need access to current time
        // onSeek(currentTime + seekOffset)
      })

      navigator.mediaSession.setActionHandler('seekbackward', (details) => {
        const seekOffset = details.seekOffset || 10
        // This would need access to current time
        // onSeek(Math.max(0, currentTime - seekOffset))
      })
    }

    // Cleanup function
    return () => {
      // Clear action handlers
      navigator.mediaSession.setActionHandler('play', null)
      navigator.mediaSession.setActionHandler('pause', null)
      navigator.mediaSession.setActionHandler('nexttrack', null)
      navigator.mediaSession.setActionHandler('previoustrack', null)
      navigator.mediaSession.setActionHandler('seekto', null)
      navigator.mediaSession.setActionHandler('seekforward', null)
      navigator.mediaSession.setActionHandler('seekbackward', null)
    }
  }, [track, isPlaying, onPlay, onPause, onNext, onPrevious, onSeek])

  // Function to update position state (call this from the audio player)
  const updatePositionState = (duration: number, playbackRate: number, position: number) => {
    if ('mediaSession' in navigator && 'setPositionState' in navigator.mediaSession) {
      try {
        navigator.mediaSession.setPositionState({
          duration,
          playbackRate,
          position
        })
      } catch (error) {
        console.warn('Failed to set position state:', error)
      }
    }
  }

  return { updatePositionState }
} 