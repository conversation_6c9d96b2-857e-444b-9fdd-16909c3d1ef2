import { Music } from 'lucide-react'

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  showMusicIcon?: boolean
}

export default function AuthLayout({ 
  children, 
  title, 
  subtitle, 
  showMusicIcon = true 
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center p-4">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-secondary-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-accent-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          {showMusicIcon && (
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl">
                <Music className="w-8 h-8 text-white" />
              </div>
            </div>
          )}
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-2">
            {title}
          </h1>
          <p className="text-gray-400">
            {subtitle}
          </p>
        </div>

        {/* Content */}
        <div className="glass-dark p-8 rounded-2xl border border-gray-700">
          {children}
        </div>
      </div>
    </div>
  )
} 