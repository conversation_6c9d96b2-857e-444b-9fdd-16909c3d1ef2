// Recently Played Section Component
'use client'

import { useState } from 'react'
import { 
  Clock, 
  Play, 
  MoreHorizontal, 
  RefreshCw,
  CheckCircle,
  Circle,
  Music,
  List,
  Grid3X3
} from 'lucide-react'
import { RecentlyPlayedTrack } from '@/types/dashboard'
import { useAudio } from '@/contexts/AudioContext'
import { convertTracksToAudioTracks } from '@/lib/audio-utils'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { EmptyState } from '@/components/ui/EmptyState'

interface RecentlyPlayedSectionProps {
  tracks: RecentlyPlayedTrack[]
  loading: boolean
  error: string | null
  hasMore: boolean
  onLoadMore: () => void
  onRefresh: () => void
}

export default function RecentlyPlayedSection({
  tracks,
  loading,
  error,
  hasMore,
  onLoadMore,
  onRefresh
}: RecentlyPlayedSectionProps) {
  const { setQueue, currentTrack, isPlaying } = useAudio()
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const [actionErrors, setActionErrors] = useState<Record<string, string>>({})

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getSourceIcon = (source: string) => {
    const icons: Record<string, string> = {
      playlist: '📋',
      search: '🔍',
      recommendations: '🎯',
      browse: '🌐',
      direct: '🎵'
    }
    return icons[source] || '🎵'
  }

  const getAIToolIcon = (tool: string) => {
    const icons: Record<string, string> = {
      suno: '🎵',
      udio: '🎶',
      mubert: '🎼',
      aiva: '🎹',
      amper: '🎸',
      custom: '⚡',
      other: '🎧'
    }
    return icons[tool] || '🎧'
  }

  const handlePlayTrack = (track: RecentlyPlayedTrack) => {
    try {
      const audioTracks = convertTracksToAudioTracks([track])
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        setActionErrors(prev => ({ ...prev, [`play-${track.id}`]: '' }))
      } else {
        setActionErrors(prev => ({ ...prev, [`play-${track.id}`]: 'Unable to play this track' }))
      }
    } catch (error) {
      setActionErrors(prev => ({ ...prev, [`play-${track.id}`]: 'Failed to play track' }))
    }
  }

  const handlePlayAll = () => {
    try {
      const audioTracks = convertTracksToAudioTracks(tracks)
      if (audioTracks.length > 0) {
        setQueue(audioTracks)
        setActionErrors(prev => ({ ...prev, 'play-all': '' }))
      } else {
        setActionErrors(prev => ({ ...prev, 'play-all': 'No tracks available to play' }))
      }
    } catch (error) {
      setActionErrors(prev => ({ ...prev, 'play-all': 'Failed to play tracks' }))
    }
  }

  // Loading state
  if (loading && tracks.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Clock className="w-5 h-5 text-purple-400" />
            Recently Played
          </h2>
          <LoadingSpinner size="sm" />
        </div>
        
        <div className="space-y-4" role="status" aria-label="Loading recently played tracks">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center gap-4 p-4 bg-gray-700 rounded-lg animate-pulse">
              <div className="w-12 h-12 bg-gray-600 rounded-lg" />
              <div className="flex-1">
                <div className="h-4 bg-gray-600 rounded w-3/4 mb-2" />
                <div className="h-3 bg-gray-600 rounded w-1/2" />
              </div>
              <div className="w-16 h-8 bg-gray-600 rounded" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Clock className="w-5 h-5 text-purple-400" />
            Recently Played
          </h2>
        </div>
        
        <ErrorMessage
          message={error}
          onRetry={onRefresh}
          retryLabel="Reload History"
          severity="error"
          className="max-w-2xl"
        />
      </div>
    )
  }

  // Empty state  
  if (tracks.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Clock className="w-5 h-5 text-purple-400" />
            Recently Played
          </h2>
        </div>
        
        <EmptyState
          icon={Music}
          title="No Recently Played Tracks"
          description="Start listening to music to see your history here!"
          action={{
            label: "Browse Music",
            href: "/browse"
          }}
        />
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white flex items-center gap-2">
          <Clock className="w-5 h-5 text-purple-400" />
          Recently Played
          <span className="text-sm text-gray-400 font-normal">({tracks.length})</span>
        </h2>
        
        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-700 rounded-lg p-1" role="tablist" aria-label="View mode">
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'list' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
              role="tab"
              aria-selected={viewMode === 'list'}
              aria-label="List view"
              title="List view"
            >
              <List className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'grid' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
              role="tab"
              aria-selected={viewMode === 'grid'}
              aria-label="Grid view"
              title="Grid view"
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
          </div>

          {/* Play All Button */}
          <button
            onClick={handlePlayAll}
            disabled={loading || tracks.length === 0}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={`Play all ${tracks.length} recently played tracks`}
          >
            <Play className="w-4 h-4" />
            Play All
          </button>

          {/* Refresh Button */}
          <button
            onClick={onRefresh}
            disabled={loading}
            className="text-gray-400 hover:text-white transition-colors disabled:opacity-50"
            aria-label="Refresh recently played tracks"
            title="Refresh recently played tracks"
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Action Errors */}
      {Object.entries(actionErrors).map(([key, error]) => 
        error && (
          <ErrorMessage
            key={key}
            message={error}
            severity="warning"
            onDismiss={() => setActionErrors(prev => ({ ...prev, [key]: '' }))}
            className="mb-4"
          />
        )
      )}

      {/* Tracks List */}
      {viewMode === 'list' ? (
        <div className="space-y-3">
          {tracks.map((track) => (
            <div
              key={`${track.id}-${track.listened_at}`}
              className="flex items-center gap-4 p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors group"
            >
              {/* Track Cover & Play Button */}
              <div className="relative w-12 h-12 flex-shrink-0">
                <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-lg">
                    {getAIToolIcon(track.ai_tool)}
                  </span>
                </div>
                <button
                  onClick={() => handlePlayTrack(track)}
                  className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Play className="w-5 h-5 text-white" />
                </button>
              </div>

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-white truncate">{track.title}</h4>
                  {track.completed ? (
                    <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                  ) : (
                    <Circle className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  )}
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <span>{track.artist_name}</span>
                  <span>•</span>
                  <span className="flex items-center gap-1">
                    {getSourceIcon(track.source)}
                    {track.source === 'playlist' && track.playlist_name ? track.playlist_name : track.source}
                  </span>
                  <span>•</span>
                  <span>{formatTimeAgo(track.listened_at)}</span>
                </div>
              </div>

              {/* Duration & Progress */}
              <div className="text-right text-sm text-gray-400">
                <div>{formatDuration(track.duration_listened)} / {formatDuration(track.duration || 0)}</div>
                <div className="w-16 bg-gray-600 rounded-full h-1 mt-1">
                  <div 
                    className="bg-purple-400 h-1 rounded-full"
                    style={{ 
                      width: `${Math.min(100, (track.duration_listened / (track.duration || 1)) * 100)}%` 
                    }}
                  />
                </div>
              </div>

              {/* More Options */}
              <button className="text-gray-400 hover:text-white transition-colors opacity-0 group-hover:opacity-100">
                <MoreHorizontal className="w-5 h-5" />
              </button>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {tracks.map((track) => (
            <div
              key={`${track.id}-${track.listened_at}`}
              className="bg-gray-700 hover:bg-gray-600 rounded-lg p-4 transition-colors group cursor-pointer"
              onClick={() => handlePlayTrack(track)}
            >
              {/* Track Cover */}
              <div className="relative w-full aspect-square mb-3">
                <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-2xl">
                    {getAIToolIcon(track.ai_tool)}
                  </span>
                </div>
                <div className="absolute top-2 right-2">
                  {track.completed ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <Circle className="w-5 h-5 text-gray-500" />
                  )}
                </div>
                <button className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <Play className="w-8 h-8 text-white" />
                </button>
              </div>

              {/* Track Info */}
              <div>
                <h4 className="font-medium text-white truncate mb-1">{track.title}</h4>
                <p className="text-sm text-gray-400 truncate mb-2">{track.artist_name}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    {getSourceIcon(track.source)}
                    {track.source}
                  </span>
                  <span>{formatTimeAgo(track.listened_at)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center mt-6">
          <button
            onClick={onLoadMore}
            disabled={loading}
            className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Load More'}
          </button>
        </div>
      )}
    </div>
  )
} 