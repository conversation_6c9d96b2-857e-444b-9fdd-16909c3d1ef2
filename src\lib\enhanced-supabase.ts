// Enhanced Supabase operations with comprehensive error handling
import { supabase, db, auth } from './supabase'
import { 
  handleSupabaseError, 
  defaultRetryHandler,
  TunamiError,
  showErrorToast,
  showSuccessToast 
} from './error-handler'

// Enhanced database operations with error handling and retry
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  context?: Record<string, any>,
  showToast = true
): Promise<T> => {
  try {
    return await defaultRetryHandler.execute(
      operation,
      (error) => handleSupabaseError(error, context)
    )
  } catch (error) {
    if (error instanceof TunamiError) {
      if (showToast) showErrorToast(error)
      throw error
    }
    
    const tunamiError = handleSupabaseError(error, context)
    if (showToast) showErrorToast(tunamiError)
    throw tunamiError
  }
}

// Enhanced authentication operations
export const enhancedAuth = {
  signUp: async (email: string, password: string, options?: { 
    data?: { 
      full_name?: string 
      username?: string 
    } 
  }) => {
    return withErrorHandling(
      () => auth.signUp(email, password, options),
      { operation: 'signUp', email }
    )
  },

  signIn: async (email: string, password: string) => {
    const result = await withErrorHandling(
      () => auth.signIn(email, password),
      { operation: 'signIn', email }
    )
    
    if (result.data?.user) {
      showSuccessToast('Welcome back!')
    }
    
    return result
  },

  signOut: async () => {
    const result = await withErrorHandling(
      () => auth.signOut(),
      { operation: 'signOut' }
    )
    
    showSuccessToast('Signed out successfully')
    return result
  },

  resetPassword: async (email: string) => {
    const result = await withErrorHandling(
      () => auth.resetPassword(email),
      { operation: 'resetPassword', email }
    )
    
    showSuccessToast('Password reset email sent')
    return result
  },

  updatePassword: async (password: string) => {
    const result = await withErrorHandling(
      () => auth.updatePassword(password),
      { operation: 'updatePassword' }
    )
    
    showSuccessToast('Password updated successfully')
    return result
  }
}

// Enhanced database operations
export const enhancedDb = {
  profiles: {
    get: async (userId: string) => {
      return withErrorHandling(
        () => db.profiles.get(userId),
        { operation: 'getProfile', userId },
        false // Don't show toast for profile fetching
      )
    },

    create: async (profile: any) => {
      const result = await withErrorHandling(
        () => db.profiles.create(profile),
        { operation: 'createProfile', profileId: profile.id }
      )
      
      showSuccessToast('Profile created successfully')
      return result
    },

    update: async (userId: string, updates: any) => {
      const result = await withErrorHandling(
        () => db.profiles.update(userId, updates),
        { operation: 'updateProfile', userId }
      )
      
      showSuccessToast('Profile updated successfully')
      return result
    }
  },

  tracks: {
    getAll: async (limit = 50) => {
      return withErrorHandling(
        () => db.tracks.getAll(limit),
        { operation: 'getAllTracks', limit },
        false // Don't show toast for data fetching
      )
    },

    getById: async (trackId: string) => {
      return withErrorHandling(
        () => db.tracks.getById(trackId),
        { operation: 'getTrackById', trackId },
        false
      )
    },

    search: async (query: string, limit = 20) => {
      return withErrorHandling(
        () => db.tracks.search(query, limit),
        { operation: 'searchTracks', query, limit },
        false
      )
    },

    incrementPlayCount: async (trackId: string) => {
      return withErrorHandling(
        () => db.tracks.incrementPlayCount(trackId),
        { operation: 'incrementPlayCount', trackId },
        false // Silent operation
      )
    }
  },

  playlists: {
    getUserPlaylists: async (userId: string) => {
      return withErrorHandling(
        () => db.playlists.getUserPlaylists(userId),
        { operation: 'getUserPlaylists', userId },
        false
      )
    },

    create: async (playlist: any) => {
      const result = await withErrorHandling(
        () => db.playlists.create(playlist),
        { operation: 'createPlaylist', playlistName: playlist.name }
      )
      
      showSuccessToast('Playlist created successfully')
      return result
    },

    addSong: async (playlistId: string, songId: string, position: number) => {
      const result = await withErrorHandling(
        () => db.playlists.addSong(playlistId, songId, position),
        { operation: 'addSongToPlaylist', playlistId, songId }
      )
      
      showSuccessToast('Song added to playlist')
      return result
    },

    removeSong: async (playlistId: string, songId: string) => {
      const result = await withErrorHandling(
        () => db.playlists.removeSong(playlistId, songId),
        { operation: 'removeSongFromPlaylist', playlistId, songId }
      )
      
      showSuccessToast('Song removed from playlist')
      return result
    }
  },

  likes: {
    toggle: async (userId: string, songId: string) => {
      const result = await withErrorHandling(
        () => db.likes.toggle(userId, songId),
        { operation: 'toggleLike', userId, songId }
      )
      
      if (result.liked) {
        showSuccessToast('Added to liked songs')
      } else {
        showSuccessToast('Removed from liked songs')
      }
      
      return result
    },

    getUserLikes: async (userId: string) => {
      return withErrorHandling(
        () => db.likes.getUserLikes(userId),
        { operation: 'getUserLikes', userId },
        false
      )
    }
  }
}

// Export everything for convenience
export { supabase, db, auth } from './supabase' 