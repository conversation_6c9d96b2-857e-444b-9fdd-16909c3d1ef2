'use client'

import { useState, useEffect } from 'react'

interface OrientationState {
  isLandscape: boolean
  isPortrait: boolean
  angle: number
  width: number
  height: number
}

export function useOrientation() {
  const [orientation, setOrientation] = useState<OrientationState>({
    isLandscape: false,
    isPortrait: true,
    angle: 0,
    width: 0,
    height: 0
  })

  useEffect(() => {
    const updateOrientation = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      const isLandscape = width > height
      
      // Get device orientation angle if available
      let angle = 0
      if (screen.orientation) {
        angle = screen.orientation.angle
      } else if (window.orientation !== undefined) {
        angle = window.orientation
      }

      setOrientation({
        isLandscape,
        isPortrait: !isLandscape,
        angle,
        width,
        height
      })
    }

    // Initial check
    updateOrientation()

    // Listen for orientation changes
    const handleOrientationChange = () => {
      // Use a small delay to ensure dimensions have updated
      setTimeout(updateOrientation, 100)
    }

    // Listen for both orientation and resize events
    window.addEventListener('orientationchange', handleOrientationChange)
    window.addEventListener('resize', updateOrientation)

    // Cleanup
    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange)
      window.removeEventListener('resize', updateOrientation)
    }
  }, [])

  return orientation
} 