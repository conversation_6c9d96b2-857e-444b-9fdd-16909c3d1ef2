'use client'

import { Check<PERSON>ircle, AlertCircle, Loader, Music, X } from 'lucide-react'
import type { UploadFile } from './FileUpload'
import type { UploadProgress as UploadProgressType } from '@/lib/upload-service'

interface UploadProgressProps {
  files: UploadFile[]
  progress: Record<string, UploadProgressType>
  onCancel?: (fileId: string) => void
  showCancel?: boolean
  className?: string
}

export default function UploadProgress({
  files,
  progress,
  onCancel,
  showCancel = false,
  className = ''
}: UploadProgressProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (file: UploadFile, progressData?: UploadProgressType) => {
    const status = progressData?.status || file.status

    switch (status) {
      case 'pending':
        return <Music className="w-5 h-5 text-gray-400" />
      case 'uploading':
        return <Loader className="w-5 h-5 text-blue-500 animate-spin" />
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />
      default:
        return <Music className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (file: UploadFile, progressData?: UploadProgressType) => {
    const status = progressData?.status || file.status

    switch (status) {
      case 'pending':
        return 'border-gray-600 bg-gray-800/50'
      case 'uploading':
        return 'border-blue-500 bg-blue-900/20'
      case 'success':
        return 'border-green-500 bg-green-900/20'
      case 'error':
        return 'border-red-500 bg-red-900/20'
      default:
        return 'border-gray-600 bg-gray-800/50'
    }
  }

  const getStatusText = (file: UploadFile, progressData?: UploadProgressType) => {
    const status = progressData?.status || file.status

    switch (status) {
      case 'pending':
        return 'Waiting to upload...'
      case 'uploading':
        return 'Uploading...'
      case 'success':
        return 'Upload complete!'
      case 'error':
        return progressData?.error || file.error || 'Upload failed'
      default:
        return 'Ready'
    }
  }

  const getTotalProgress = (): number => {
    if (files.length === 0) return 0
    
    const totalProgress = files.reduce((sum, file) => {
      const fileProgress = progress[file.id]?.progress || file.progress || 0
      return sum + fileProgress
    }, 0)
    
    return Math.round(totalProgress / files.length)
  }

  const getCompletedCount = (): number => {
    return files.filter(file => {
      const status = progress[file.id]?.status || file.status
      return status === 'success'
    }).length
  }

  const getFailedCount = (): number => {
    return files.filter(file => {
      const status = progress[file.id]?.status || file.status
      return status === 'error'
    }).length
  }

  if (files.length === 0) {
    return null
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Overall Progress */}
      <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-white flex items-center gap-2">
            <Music className="w-4 h-4" />
            Upload Progress
          </h3>
          <span className="text-sm text-gray-400">
            {getCompletedCount()}/{files.length} completed
          </span>
        </div>

        {/* Overall Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-400">
            <span>Overall Progress</span>
            <span>{getTotalProgress()}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${getTotalProgress()}%` }}
            />
          </div>
        </div>

        {/* Summary Stats */}
        {(getCompletedCount() > 0 || getFailedCount() > 0) && (
          <div className="mt-3 flex items-center gap-4 text-xs">
            {getCompletedCount() > 0 && (
              <span className="flex items-center gap-1 text-green-400">
                <CheckCircle className="w-3 h-3" />
                {getCompletedCount()} successful
              </span>
            )}
            {getFailedCount() > 0 && (
              <span className="flex items-center gap-1 text-red-400">
                <AlertCircle className="w-3 h-3" />
                {getFailedCount()} failed
              </span>
            )}
          </div>
        )}
      </div>

      {/* Individual File Progress */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-300">File Details</h4>
        
        <div className="space-y-2">
          {files.map(file => {
            const progressData = progress[file.id]
            const currentProgress = progressData?.progress || file.progress || 0
            const status = progressData?.status || file.status

            return (
              <div
                key={file.id}
                className={`
                  p-4 rounded-lg border transition-all duration-200
                  ${getStatusColor(file, progressData)}
                `}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1 min-w-0">
                    {/* Status Icon */}
                    <div className="mt-0.5 flex-shrink-0">
                      {getStatusIcon(file, progressData)}
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <p className="text-sm font-medium text-white truncate pr-2">
                          {file.file.name}
                        </p>
                        {status === 'uploading' && (
                          <span className="text-xs text-gray-400 flex-shrink-0">
                            {currentProgress}%
                          </span>
                        )}
                      </div>

                      <p className="text-xs text-gray-400 mb-2">
                        {formatFileSize(file.file.size)} • {file.file.type}
                      </p>

                      {/* Status Text */}
                      <p className={`text-xs ${
                        status === 'error' ? 'text-red-400' : 
                        status === 'success' ? 'text-green-400' : 
                        'text-gray-400'
                      }`}>
                        {getStatusText(file, progressData)}
                      </p>

                      {/* Progress Bar */}
                      {status === 'uploading' && (
                        <div className="mt-2">
                          <div className="w-full bg-gray-700 rounded-full h-1.5">
                            <div
                              className="bg-blue-500 h-1.5 rounded-full transition-all duration-300 ease-out"
                              style={{ width: `${currentProgress}%` }}
                            />
                          </div>
                        </div>
                      )}

                      {/* Success URL */}
                      {status === 'success' && progressData?.publicUrl && (
                        <div className="mt-2">
                          <a
                            href={progressData.publicUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-purple-400 hover:text-purple-300 underline"
                          >
                            View uploaded file →
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Cancel Button */}
                  {showCancel && onCancel && status !== 'success' && (
                    <button
                      onClick={() => onCancel(file.id)}
                      className="p-1 rounded-full hover:bg-gray-700 transition-colors flex-shrink-0 ml-2"
                      aria-label="Cancel upload"
                    >
                      <X className="w-4 h-4 text-gray-400 hover:text-red-400" />
                    </button>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
} 