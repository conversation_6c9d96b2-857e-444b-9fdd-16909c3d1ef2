'use client'

import React from 'react'
import { LucideIcon } from 'lucide-react'

interface EmptyStateProps {
  icon?: LucideIcon
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'primary' | 'secondary'
  }
  className?: string
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon,
  title,
  description,
  action,
  className = ''
}) => {
  return (
    <div className={`text-center py-12 ${className}`}>
      {Icon && (
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center">
            <Icon className="w-8 h-8 text-gray-400" />
          </div>
        </div>
      )}
      
      <h3 className="text-lg font-semibold text-white mb-2">
        {title}
      </h3>
      
      <p className="text-gray-400 text-sm max-w-md mx-auto mb-6">
        {description}
      </p>
      
      {action && (
        <button
          onClick={action.onClick}
          className={`px-6 py-2 rounded-lg font-medium transition-colors ${
            action.variant === 'secondary'
              ? 'bg-gray-700 hover:bg-gray-600 text-white'
              : 'bg-purple-600 hover:bg-purple-700 text-white'
          }`}
        >
          {action.label}
        </button>
      )}
    </div>
  )
}

export default EmptyState

// Icon components for empty states
const MusicEmojiIcon = ({ className }: { className?: string }) => (
  <div className={`${className} flex items-center justify-center text-4xl`}>🎵</div>
)

const PlaylistEmojiIcon = ({ className }: { className?: string }) => (
  <div className={`${className} flex items-center justify-center text-4xl`}>📝</div>
)

const ClockEmojiIcon = ({ className }: { className?: string }) => (
  <div className={`${className} flex items-center justify-center text-4xl`}>⏰</div>
)

// Predefined empty states for common scenarios
export function NoTracksFound({ onExplore }: { onExplore?: () => void }) {
  return (
    <EmptyState
      icon={MusicEmojiIcon}
      title="No tracks found"
      description="We couldn't find any tracks matching your criteria. Try adjusting your search or explore our featured content."
      action={{
        label: "Explore Music",
        onClick: onExplore,
        variant: 'primary'
      }}
    />
  )
}

export function NoPlaylistsYet({ onCreate }: { onCreate?: () => void }) {
  return (
    <EmptyState
      icon={PlaylistEmojiIcon}
      title="No playlists yet"
      description="Create your first playlist to start organizing your favorite tracks."
      action={{
        label: "Create Playlist",
        onClick: onCreate,
        variant: 'primary'
      }}
    />
  )
}

export function NoRecentlyPlayed() {
  return (
    <EmptyState
      icon={ClockEmojiIcon}
      title="No recently played tracks"
      description="Your listening history will appear here once you start playing music."
    />
  )
} 