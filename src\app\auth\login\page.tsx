'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { Mail, Lock, Eye, EyeOff, Music, AlertCircle, CheckCircle } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { GoogleIcon, GitHubIcon } from '@/components/icons/SocialIcons'
import { PageLoader } from '@/components/ui/SmoothLoader'
import { ComponentErrorBoundary } from '@/components/error/ErrorBoundary'

interface FormErrors {
  email?: string
  password?: string
  general?: string
}

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<FormErrors>({})
  const [mounted, setMounted] = useState(false)
  const [oauthLoading, setOauthLoading] = useState<'google' | 'github' | null>(null)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const router = useRouter()
  const { signIn, signInWithGoogle, signInWithGitHub, user, loading: authLoading } = useAuth()

  // Fix hydration issue
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if already authenticated
  useEffect(() => {
    if (mounted && !authLoading && user) {
      setSuccessMessage('Successfully logged in! Redirecting...')
      setTimeout(() => {
        router.replace('/dashboard')
      }, 1500)
    }
  }, [mounted, authLoading, user, router])

  // Real-time form validation
  const validateEmail = useCallback((email: string): string | undefined => {
    if (!email.trim()) return 'Email is required'
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) return 'Please enter a valid email address'
    return undefined
  }, [])

  const validatePassword = useCallback((password: string): string | undefined => {
    if (!password) return 'Password is required'
    if (password.length < 6) return 'Password must be at least 6 characters'
    return undefined
  }, [])

  // Memoized form validation
  const formValidation = useMemo(() => {
    const emailError = validateEmail(email)
    const passwordError = validatePassword(password)
    
    return {
      isValid: !emailError && !passwordError,
      errors: {
        email: isSubmitted ? emailError : undefined,
        password: isSubmitted ? passwordError : undefined
      }
    }
  }, [email, password, isSubmitted, validateEmail, validatePassword])

  // Enhanced login handler with better error messaging
  const handleLogin = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitted(true)
    
    if (!formValidation.isValid || loading) return
    
    setLoading(true)
    setErrors({})
    setSuccessMessage('')

    try {
      const { data, error: signInError } = await signIn(email, password)
      
      if (signInError) {
        // More specific error messages
        let errorMessage = 'Login failed. Please try again.'
        if (signInError.message?.includes('Invalid login credentials')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.'
        } else if (signInError.message?.includes('Email not confirmed')) {
          errorMessage = 'Please check your email and click the confirmation link before signing in.'
        } else if (signInError.message?.includes('Too many requests')) {
          errorMessage = 'Too many login attempts. Please wait a few minutes and try again.'
        }
        
        setErrors({ general: errorMessage })
        return
      }

      if (data?.user) {
        setSuccessMessage('Login successful! Redirecting to your dashboard...')
        // Redirect will be handled by useEffect
      }
    } catch (err: any) {
      setErrors({ 
        general: err.message || 'An unexpected error occurred. Please try again.' 
      })
    } finally {
      setLoading(false)
    }
  }, [email, password, signIn, formValidation.isValid, loading])

  // Enhanced input handlers with validation
  const handleEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setEmail(value)
    
    // Clear specific errors on input change
    if (errors.email || errors.general) {
      setErrors(prev => ({ ...prev, email: undefined, general: undefined }))
    }
  }, [errors.email, errors.general])

  const handlePasswordChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setPassword(value)
    
    // Clear specific errors on input change
    if (errors.password || errors.general) {
      setErrors(prev => ({ ...prev, password: undefined, general: undefined }))
    }
  }, [errors.password, errors.general])

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev)
  }, [])

  // Enhanced OAuth handlers with better error handling
  const handleGoogleSignIn = useCallback(async () => {
    if (oauthLoading) return
    
    setOauthLoading('google')
    setErrors({})
    
    try {
      const { error: oauthError } = await signInWithGoogle({
        redirectTo: `${window.location.origin}/auth/callback`
      })
      
      if (oauthError) {
        let errorMessage = 'Google sign in failed. Please try again.'
        if (oauthError.message?.includes('popup')) {
          errorMessage = 'Sign in popup was blocked. Please allow popups and try again.'
        }
        setErrors({ general: errorMessage })
        setOauthLoading(null)
      }
    } catch (err: any) {
      setErrors({ general: err.message || 'An unexpected error occurred with Google sign in.' })
      setOauthLoading(null)
    }
  }, [signInWithGoogle, oauthLoading])

  const handleGitHubSignIn = useCallback(async () => {
    if (oauthLoading) return
    
    setOauthLoading('github')
    setErrors({})
    
    try {
      const { error: oauthError } = await signInWithGitHub({
        redirectTo: `${window.location.origin}/auth/callback`
      })
      
      if (oauthError) {
        let errorMessage = 'GitHub sign in failed. Please try again.'
        if (oauthError.message?.includes('popup')) {
          errorMessage = 'Sign in popup was blocked. Please allow popups and try again.'
        }
        setErrors({ general: errorMessage })
        setOauthLoading(null)
      }
    } catch (err: any) {
      setErrors({ general: err.message || 'An unexpected error occurred with GitHub sign in.' })
      setOauthLoading(null)
    }
  }, [signInWithGitHub, oauthLoading])

  // Show loading state during hydration or auth check
  if (!mounted || (authLoading && !user)) {
    return <PageLoader message="Checking authentication..." />
  }

  // Don't render if user is already authenticated (prevents flash)
  if (user) {
    return null
  }

  return (
    <ComponentErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="glass-dark p-8 rounded-2xl shadow-2xl">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl">
                  <Music className="w-10 h-10 text-white" />
                </div>
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                Welcome to Tunami
              </h1>
              <p className="text-gray-400 mt-2">Sign in to your account to continue</p>
            </div>

            {/* Success Message */}
            {successMessage && (
              <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3 mb-6 flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                <p className="text-green-400 text-sm">{successMessage}</p>
              </div>
            )}

            {/* General Error Message */}
            {errors.general && (
              <div 
                className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-6 flex items-start gap-2"
                role="alert"
                aria-live="polite"
              >
                <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <p className="text-red-400 text-sm">{errors.general}</p>
              </div>
            )}

            {/* OAuth Buttons */}
            <div className="space-y-3 mb-6">
              <button
                type="button"
                onClick={handleGoogleSignIn}
                disabled={loading || oauthLoading === 'google'}
                className="w-full flex items-center justify-center px-4 py-3 border border-gray-600 rounded-lg bg-white hover:bg-gray-50 text-gray-900 font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900"
                aria-label="Continue with Google"
              >
                {oauthLoading === 'google' ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-3"></div>
                ) : (
                  <GoogleIcon className="mr-3" size={20} />
                )}
                Continue with Google
              </button>

              <button
                type="button"
                onClick={handleGitHubSignIn}
                disabled={loading || oauthLoading === 'github'}
                className="w-full flex items-center justify-center px-4 py-3 border border-gray-600 rounded-lg bg-gray-900 hover:bg-gray-800 text-white font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900"
                aria-label="Continue with GitHub"
              >
                {oauthLoading === 'github' ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                ) : (
                  <GitHubIcon className="mr-3 text-white" size={20} />
                )}
                Continue with GitHub
              </button>
            </div>

            {/* Divider */}
            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-600"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-dark-900 text-gray-400">Or continue with email</span>
              </div>
            </div>

            {/* Login Form */}
            <form onSubmit={handleLogin} className="space-y-6" noValidate>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  Email address *
                </label>
                <div className="relative">
                  <Mail className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={email}
                    onChange={handleEmailChange}
                    className={`w-full pl-10 pr-4 py-3 bg-dark-800 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white placeholder-gray-400 transition-colors ${
                      formValidation.errors.email 
                        ? 'border-red-500' 
                        : 'border-gray-700'
                    }`}
                    placeholder="Enter your email address"
                    required
                    autoComplete="email"
                    disabled={loading}
                    aria-invalid={!!formValidation.errors.email}
                    aria-describedby={formValidation.errors.email ? "email-error" : undefined}
                  />
                </div>
                {formValidation.errors.email && (
                  <p id="email-error" className="mt-1 text-sm text-red-400" role="alert">
                    {formValidation.errors.email}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                  Password *
                </label>
                <div className="relative">
                  <Lock className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={handlePasswordChange}
                    className={`w-full pl-10 pr-12 py-3 bg-dark-800 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white placeholder-gray-400 transition-colors ${
                      formValidation.errors.password 
                        ? 'border-red-500' 
                        : 'border-gray-700'
                    }`}
                    placeholder="Enter your password"
                    required
                    autoComplete="current-password"
                    disabled={loading}
                    aria-invalid={!!formValidation.errors.password}
                    aria-describedby={formValidation.errors.password ? "password-error" : undefined}
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 rounded"
                    disabled={loading}
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {formValidation.errors.password && (
                  <p id="password-error" className="mt-1 text-sm text-red-400" role="alert">
                    {formValidation.errors.password}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={loading || !formValidation.isValid}
                className="w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900"
                aria-label="Sign in to your account"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Signing in...
                  </>
                ) : (
                  'Sign in'
                )}
              </button>
            </form>

            {/* Footer */}
            <div className="mt-6 text-center">
              <p className="text-gray-400 text-sm">
                Don&apos;t have an account?{' '}
                <a 
                  href="/auth/signup" 
                  className="text-primary-400 hover:text-primary-300 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 rounded"
                >
                  Sign up here
                </a>
              </p>
              <p className="text-gray-500 text-xs mt-2">
                <a 
                  href="/auth/forgot-password" 
                  className="hover:text-gray-400 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 rounded"
                >
                  Forgot your password?
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </ComponentErrorBoundary>
  )
} 