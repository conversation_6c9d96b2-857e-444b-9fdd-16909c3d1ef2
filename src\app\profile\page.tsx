// User Profile Page
'use client'

import { useState } from 'react'
import { useProfile } from '@/hooks/useProfile'
import { useAuth } from '@/contexts/AuthContext'
import ProfileHeader from '@/components/profile/ProfileHeader'
import ProfileEditModal from '@/components/profile/ProfileEditModal'
import ProfileTabs from '@/components/profile/ProfileTabs'
import { LoadingSpinner, ErrorMessage } from '@/components/LoadingStates'
import { ExtendedProfile } from '@/types/profile'

export default function ProfilePage() {
  const { user } = useAuth()
  const {
    profile,
    stats,
    tracks,
    playlists,
    loading,
    updating,
    uploading,
    error,
    updateProfile,
    uploadAvatar,
    loadMoreTracks,
    loadMorePlaylists,
    refresh,
    isOwnProfile
  } = useProfile()

  const [isEditModalOpen, setIsEditModalOpen] = useState(false)

  const handleEditProfile = () => {
    setIsEditModalOpen(true)
  }

  const handleSaveProfile = async (data: Partial<ExtendedProfile>, avatarFile?: File) => {
    try {
      // Upload avatar if provided
      if (avatarFile) {
        const uploadResult = await uploadAvatar(avatarFile)
        if (!uploadResult.success) {
          throw new Error(uploadResult.error)
        }
        // Avatar URL is automatically updated in the profile
      }

      // Update profile data
      const updateResult = await updateProfile(data)
      if (!updateResult.success) {
        throw new Error(updateResult.error)
      }

      setIsEditModalOpen(false)
    } catch (error) {
      console.error('Save profile error:', error)
      throw error
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <ErrorMessage 
          message={error} 
          onRetry={refresh}
        />
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">👤</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Profile Not Found
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            The profile you're looking for doesn't exist.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {isOwnProfile ? 'My Profile' : `${profile.full_name || 'User'}'s Profile`}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {isOwnProfile 
              ? 'Manage your profile and view your music collection'
              : 'Discover their music and playlists'
            }
          </p>
        </div>

        <div className="space-y-8">
          {/* Profile Header */}
          <ProfileHeader
            profile={profile}
            stats={stats}
            isOwnProfile={isOwnProfile}
            onEditClick={handleEditProfile}
          />

          {/* Profile Content Tabs */}
          <ProfileTabs
            profile={profile}
            tracks={tracks}
            playlists={playlists}
            isOwnProfile={isOwnProfile}
            onLoadMoreTracks={loadMoreTracks}
            onLoadMorePlaylists={loadMorePlaylists}
          />
        </div>

        {/* Edit Profile Modal */}
        {isEditModalOpen && (
          <ProfileEditModal
            profile={profile}
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSave={handleSaveProfile}
            loading={updating || uploading}
          />
        )}
      </div>
    </div>
  )
} 