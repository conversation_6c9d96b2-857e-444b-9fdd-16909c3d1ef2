'use client'

import React, { useState } from 'react'
import { Plus, Search, Filter, Grid, List } from 'lucide-react'
import PlaylistCard from './PlaylistCard'
import PlaylistModal from './PlaylistModal'
import DeletePlaylistModal from './DeletePlaylistModal'
import { Playlist } from '@/types/playlist'
import { usePlaylist } from '@/contexts/PlaylistContext'

interface PlaylistListProps {
  playlists: Playlist[]
  title?: string
  showCreateButton?: boolean
  showOwner?: boolean
  emptyMessage?: string
  className?: string
}

export default function PlaylistList({ 
  playlists, 
  title = 'Playlists',
  showCreateButton = true,
  showOwner = false,
  emptyMessage = 'No playlists found',
  className = ''
}: PlaylistListProps) {
  const { loading, error } = usePlaylist()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedPlaylist, setSelectedPlaylist] = useState<Playlist | null>(null)

  // Filter playlists based on search term
  const filteredPlaylists = playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (playlist.description && playlist.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const handleCreatePlaylist = () => {
    setShowCreateModal(true)
  }

  const handleEditPlaylist = (playlist: Playlist) => {
    setSelectedPlaylist(playlist)
    setShowEditModal(true)
  }

  const handleDeletePlaylist = (playlist: Playlist) => {
    setSelectedPlaylist(playlist)
    setShowDeleteModal(true)
  }

  const handlePlayPlaylist = (playlist: Playlist) => {
    // TODO: Integrate with audio player
    console.log('Playing playlist:', playlist.name)
  }

  const closeModals = () => {
    setShowCreateModal(false)
    setShowEditModal(false)
    setShowDeleteModal(false)
    setSelectedPlaylist(null)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{title}</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {playlists.length} {playlists.length === 1 ? 'playlist' : 'playlists'}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search playlists..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white w-64"
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>

          {/* Create Button */}
          {showCreateButton && (
            <button
              onClick={handleCreatePlaylist}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Create Playlist</span>
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Content */}
      {!loading && (
        <>
          {filteredPlaylists.length === 0 ? (
            /* Empty State */
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {searchTerm ? 'No playlists found' : emptyMessage}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                {searchTerm 
                  ? `No playlists match "${searchTerm}"`
                  : 'Create your first playlist to get started'
                }
              </p>
              {showCreateButton && !searchTerm && (
                <button
                  onClick={handleCreatePlaylist}
                  className="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
                >
                  <Plus className="w-5 h-5" />
                  <span>Create Your First Playlist</span>
                </button>
              )}
            </div>
          ) : (
            /* Playlist Grid/List */
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
            }>
              {filteredPlaylists.map((playlist) => (
                <PlaylistCard
                  key={playlist.id}
                  playlist={playlist}
                  onEdit={handleEditPlaylist}
                  onDelete={handleDeletePlaylist}
                  onPlay={handlePlayPlaylist}
                  showOwner={showOwner}
                  className={viewMode === 'list' ? 'flex flex-row max-w-none' : ''}
                />
              ))}
            </div>
          )}
        </>
      )}

      {/* Modals */}
      <PlaylistModal
        isOpen={showCreateModal}
        onClose={closeModals}
        mode="create"
      />

      <PlaylistModal
        isOpen={showEditModal}
        onClose={closeModals}
        playlist={selectedPlaylist}
        mode="edit"
      />

      <DeletePlaylistModal
        isOpen={showDeleteModal}
        onClose={closeModals}
        playlist={selectedPlaylist}
      />
    </div>
  )
} 