// Debug utility for authentication testing
export const debugAuth = {
  // Check environment variables
  checkEnv: () => {
    console.log('🔍 Environment Check:')
    console.log('SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing')
    console.log('SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing')
  },

  // Test Supabase connection
  testConnection: async () => {
    try {
      const { supabase } = await import('./supabase')
      const { data, error } = await supabase.auth.getSession()
      console.log('🔗 Supabase Connection:', error ? '❌ Failed' : '✅ Connected')
      if (error) console.error('Connection error:', error)
      return !error
    } catch (err) {
      console.error('🔗 Supabase Connection: ❌ Failed', err)
      return false
    }
  },

  // Check current auth state
  checkAuthState: async () => {
    try {
      const { supabase } = await import('./supabase')
      const { data: { session }, error } = await supabase.auth.getSession()
      console.log('👤 Auth State:', session ? '✅ Authenticated' : '❌ Not authenticated')
      if (session) {
        console.log('User ID:', session.user.id)
        console.log('Email:', session.user.email)
      }
      return session
    } catch (err) {
      console.error('👤 Auth State Check Failed:', err)
      return null
    }
  }
}

// Add to window for browser console access
if (typeof window !== 'undefined') {
  (window as any).debugAuth = debugAuth
} 