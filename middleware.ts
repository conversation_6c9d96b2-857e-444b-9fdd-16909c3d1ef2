import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import type { Database } from '@/types/database'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  
  // Create a Supabase client configured to use cookies
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          req.cookies.set({
            name,
            value,
            ...options,
          })
          res.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          })
          res.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Refresh session if expired - required for Server Components
  const {
    data: { session },
  } = await supabase.auth.getSession()

  const { pathname } = req.nextUrl

  // Define protected routes
  const protectedRoutes = [
    '/dashboard',
    '/profile',
    '/playlists',
    '/library',
    '/upload',
    '/settings',
  ]

  // Define auth routes (should redirect if already authenticated)
  const authRoutes = [
    '/auth/login',
    '/auth/signup',
    '/auth/forgot-password',
  ]

  // Check if the current path is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  )

  // Check if the current path is an auth route
  const isAuthRoute = authRoutes.some(route => 
    pathname.startsWith(route)
  )

  // If user is not authenticated and trying to access protected route
  if (isProtectedRoute && !session) {
    const redirectUrl = new URL('/auth/login', req.url)
    redirectUrl.searchParams.set('redirectTo', pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // If user is authenticated and trying to access auth routes
  if (isAuthRoute && session) {
    const redirectTo = req.nextUrl.searchParams.get('redirectTo') || '/dashboard'
    return NextResponse.redirect(new URL(redirectTo, req.url))
  }

  // Special handling for email confirmation
  if (pathname === '/auth/confirm') {
    const token_hash = req.nextUrl.searchParams.get('token_hash')
    const type = req.nextUrl.searchParams.get('type')
    
    if (token_hash && type) {
      const { error } = await supabase.auth.verifyOtp({
        type: type as any,
        token_hash,
      })
      
      if (!error) {
        // Redirect to dashboard on successful verification
        return NextResponse.redirect(new URL('/dashboard', req.url))
      } else {
        // Redirect to login with error message
        const redirectUrl = new URL('/auth/login', req.url)
        redirectUrl.searchParams.set('error', 'verification_failed')
        return NextResponse.redirect(redirectUrl)
      }
    }
  }

  // Special handling for password reset
  if (pathname === '/auth/reset-password') {
    const token_hash = req.nextUrl.searchParams.get('token_hash')
    const type = req.nextUrl.searchParams.get('type')
    
    if (token_hash && type === 'recovery') {
      // Allow access to reset password page
      return res
    } else if (!session) {
      // Redirect to login if no session and no valid reset token
      return NextResponse.redirect(new URL('/auth/login', req.url))
    }
  }

  // Handle API routes - ensure user is authenticated for protected API routes
  if (pathname.startsWith('/api/')) {
    const protectedApiRoutes = [
      '/api/user',
      '/api/playlists',
      '/api/songs/upload',
      '/api/profile',
    ]

    const isProtectedApiRoute = protectedApiRoutes.some(route =>
      pathname.startsWith(route)
    )

    if (isProtectedApiRoute && !session) {
      return new NextResponse(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
    }
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
} 