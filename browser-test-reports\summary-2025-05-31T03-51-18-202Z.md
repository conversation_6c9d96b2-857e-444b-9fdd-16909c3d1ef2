# Browser Compatibility Report - Tunami

**Generated:** 31/5/2025, 9:25:10 am

## Summary

- **Overall Compatibility:** 80%
- **Browsers Tested:** 5 (3 Desktop, 2 Mobile)
- **Successful Tests:** 4/5

## Browser Results


### Chromium 🖥️

- **Status:** ✅ Passed
- **Tests:** 0 passed, 0 failed, 0 skipped


### Firefox 🖥️

- **Status:** ❌ Failed

- **Error:** Command failed: npx playwright test --project=firefox --reporter=json


### Webkit 🖥️

- **Status:** ✅ Passed
- **Tests:** 0 passed, 0 failed, 0 skipped


### Mobile-chrome 📱

- **Status:** ✅ Passed
- **Tests:** 0 passed, 0 failed, 0 skipped


### Mobile-safari 📱

- **Status:** ✅ Passed
- **Tests:** 0 passed, 0 failed, 0 skipped



## Audio Compatibility


- **chromium:** 0% (0/0 tests passed)

- **firefox:** 0% (0/0 tests passed)

- **webkit:** 0% (0/0 tests passed)

- **mobile-chrome:** 0% (0/0 tests passed)

- **mobile-safari:** 0% (0/0 tests passed)



## Recommendations


- **firefox:** firefox testing failed: Command failed: npx playwright test --project=firefox --reporter=json

  - *Action:* Check browser compatibility and fix blocking issues



---
*Report generated by Tunami Browser Compatibility Test Runner*
