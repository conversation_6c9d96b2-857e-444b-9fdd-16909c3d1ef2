import React, { useState, useEffect, useRef, ReactNode } from 'react'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'

interface LazyLoadProps {
  children: ReactNode
  placeholder?: ReactNode
  rootMargin?: string
  threshold?: number
  triggerOnce?: boolean
  className?: string
  fallback?: ReactNode
  delay?: number
}

export const LazyLoad: React.FC<LazyLoadProps> = ({
  children,
  placeholder,
  rootMargin = '50px',
  threshold = 0.1,
  triggerOnce = true,
  className = '',
  fallback,
  delay = 0
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [showContent, setShowContent] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)

  const entry = useIntersectionObserver(elementRef, {
    rootMargin,
    threshold,
    triggerOnce
  })

  useEffect(() => {
    if (entry?.isIntersecting && !isVisible) {
      setIsVisible(true)
      
      if (delay > 0) {
        const timer = setTimeout(() => {
          setShowContent(true)
        }, delay)
        return () => clearTimeout(timer)
      } else {
        setShowContent(true)
      }
    }
  }, [entry?.isIntersecting, isVisible, delay])

  return (
    <div ref={elementRef} className={className}>
      {showContent ? children : placeholder || fallback}
    </div>
  )
}

// Specialized lazy loading components
export const LazySection: React.FC<{
  children: ReactNode
  className?: string
  skeleton?: ReactNode
}> = ({ children, className, skeleton }) => {
  return (
    <LazyLoad
      className={className}
      placeholder={skeleton || <SectionSkeleton />}
      rootMargin="100px"
    >
      {children}
    </LazyLoad>
  )
}

export const LazyTrackList: React.FC<{
  children: ReactNode
  itemCount?: number
  layout?: 'grid' | 'list'
  className?: string
}> = ({ children, itemCount = 6, layout = 'grid', className }) => {
  const skeleton = layout === 'grid' ? (
    <TrackGridSkeleton count={itemCount} />
  ) : (
    <TrackListSkeleton count={itemCount} />
  )

  return (
    <LazyLoad
      className={className}
      placeholder={skeleton}
      rootMargin="200px"
      delay={100}
    >
      {children}
    </LazyLoad>
  )
}

export const LazyImage: React.FC<{
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  aspectRatio?: 'square' | 'video' | 'portrait'
}> = ({ src, alt, className, width, height, aspectRatio = 'square' }) => {
  return (
    <LazyLoad
      placeholder={<ImageSkeleton aspectRatio={aspectRatio} className={className} />}
      rootMargin="300px"
    >
      <img
        src={src}
        alt={alt}
        className={className}
        width={width}
        height={height}
        loading="lazy"
        decoding="async"
      />
    </LazyLoad>
  )
}

// Skeleton components for different layouts
const SectionSkeleton = () => (
  <div className="animate-pulse space-y-4">
    <div className="h-6 bg-gray-700 rounded w-1/4" />
    <div className="space-y-2">
      <div className="h-4 bg-gray-700 rounded w-3/4" />
      <div className="h-4 bg-gray-700 rounded w-1/2" />
    </div>
  </div>
)

const TrackGridSkeleton: React.FC<{ count: number }> = ({ count }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {Array.from({ length: count }).map((_, i) => (
      <div key={i} className="animate-pulse">
        <div className="aspect-square bg-gray-700 rounded-lg mb-3" />
        <div className="space-y-2">
          <div className="h-4 bg-gray-700 rounded w-3/4" />
          <div className="h-3 bg-gray-700 rounded w-1/2" />
        </div>
      </div>
    ))}
  </div>
)

const TrackListSkeleton: React.FC<{ count: number }> = ({ count }) => (
  <div className="space-y-3">
    {Array.from({ length: count }).map((_, i) => (
      <div key={i} className="flex items-center space-x-4 p-3 animate-pulse">
        <div className="w-12 h-12 bg-gray-700 rounded" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-700 rounded w-3/4" />
          <div className="h-3 bg-gray-700 rounded w-1/2" />
        </div>
        <div className="w-8 h-8 bg-gray-700 rounded-full" />
      </div>
    ))}
  </div>
)

const ImageSkeleton: React.FC<{ 
  aspectRatio: 'square' | 'video' | 'portrait'
  className?: string 
}> = ({ aspectRatio, className }) => {
  const aspectClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]'
  }

  return (
    <div className={`${aspectClasses[aspectRatio]} bg-gray-700 animate-pulse rounded ${className}`} />
  )
} 