// Mock Dashboard Analytics Data
import { 
  DashboardStats, 
  RecentlyPlayedTrack, 
  UploadH<PERSON>oryItem, 
  FavoriteTrack, 
  PersonalizedRecommendation 
} from '@/types/dashboard'
import { Playlist } from '@/types/database'

// Mock Dashboard Statistics
export const mockDashboardStats: DashboardStats = {
  total_plays: 15420,
  total_uploads: 28,
  total_likes_received: 342,
  total_listening_time: 86400, // 24 hours in seconds
  tracks_uploaded_this_week: 3,
  plays_this_week: 1250,
  likes_this_week: 45,
  listening_time_this_week: 7200, // 2 hours
  favorite_genre: 'electronic',
  favorite_ai_tool: 'suno',
  streak_days: 12
}

// Mock Recently Played Tracks
export const mockRecentlyPlayed: RecentlyPlayedTrack[] = [
  {
    id: 'track-1',
    title: 'Neon Dreams',
    artist_name: 'SynthWave AI',
    genre: 'electronic',
    duration: 240,
    ai_tool: 'suno',
    file_url: '/audio/demo1.mp3',
    is_public: true,
    created_at: '2024-01-15T10:30:00Z',
    play_count: 1250,
    like_count: 89,
    listened_at: '2024-01-15T14:30:00Z',
    duration_listened: 240,
    completed: true,
    source: 'playlist',
    playlist_name: 'Chill Vibes'
  },
  {
    id: 'track-2',
    title: 'Digital Sunrise',
    artist_name: 'AI Composer',
    genre: 'ambient',
    duration: 180,
    ai_tool: 'udio',
    file_url: '/audio/demo2.mp3',
    is_public: true,
    created_at: '2024-01-15T09:15:00Z',
    play_count: 890,
    like_count: 67,
    listened_at: '2024-01-15T13:45:00Z',
    duration_listened: 150,
    completed: false,
    source: 'search',
    playlist_name: undefined
  },
  {
    id: 'track-3',
    title: 'Cosmic Journey',
    artist_name: 'Space Beats',
    genre: 'experimental',
    duration: 320,
    ai_tool: 'mubert',
    file_url: '/audio/demo3.mp3',
    is_public: true,
    created_at: '2024-01-15T08:00:00Z',
    play_count: 2100,
    like_count: 156,
    listened_at: '2024-01-15T12:20:00Z',
    duration_listened: 320,
    completed: true,
    source: 'recommendations',
    playlist_name: undefined
  },
  {
    id: 'track-4',
    title: 'Urban Pulse',
    artist_name: 'City Sounds AI',
    genre: 'hip-hop',
    duration: 200,
    ai_tool: 'aiva',
    file_url: '/audio/demo4.mp3',
    is_public: true,
    created_at: '2024-01-15T07:30:00Z',
    play_count: 750,
    like_count: 42,
    listened_at: '2024-01-15T11:10:00Z',
    duration_listened: 180,
    completed: false,
    source: 'browse',
    playlist_name: undefined
  },
  {
    id: 'track-5',
    title: 'Ethereal Waves',
    artist_name: 'Ambient Creator',
    genre: 'ambient',
    duration: 280,
    ai_tool: 'custom',
    file_url: '/audio/demo5.mp3',
    is_public: true,
    created_at: '2024-01-14T22:45:00Z',
    play_count: 1680,
    like_count: 98,
    listened_at: '2024-01-15T10:30:00Z',
    duration_listened: 280,
    completed: true,
    source: 'direct',
    playlist_name: undefined
  }
]

// Mock Upload History
export const mockUploadHistory: UploadHistoryItem[] = [
  {
    id: 'upload-1',
    title: 'My Latest Creation',
    artist_name: 'Current User',
    genre: 'electronic',
    duration: 195,
    ai_tool: 'suno',
    file_url: '/audio/user-upload1.mp3',
    is_public: true,
    created_at: '2024-01-15T16:00:00Z',
    play_count: 45,
    like_count: 8,
    upload_status: 'completed',
    file_size: 4800000, // ~4.8MB
    processing_time: 120 // 2 minutes
  },
  {
    id: 'upload-2',
    title: 'Experimental Beat',
    artist_name: 'Current User',
    genre: 'experimental',
    duration: 240,
    ai_tool: 'udio',
    file_url: '/audio/user-upload2.mp3',
    is_public: false,
    created_at: '2024-01-14T14:30:00Z',
    play_count: 12,
    like_count: 3,
    upload_status: 'completed',
    file_size: 5200000, // ~5.2MB
    processing_time: 95
  },
  {
    id: 'upload-3',
    title: 'Work in Progress',
    artist_name: 'Current User',
    genre: 'ambient',
    duration: 0,
    ai_tool: 'mubert',
    file_url: '',
    is_public: false,
    created_at: '2024-01-15T18:00:00Z',
    play_count: 0,
    like_count: 0,
    upload_status: 'processing',
    file_size: 3600000, // ~3.6MB
    processing_time: 0
  },
  {
    id: 'upload-4',
    title: 'Jazz Fusion AI',
    artist_name: 'Current User',
    genre: 'jazz',
    duration: 210,
    ai_tool: 'aiva',
    file_url: '/audio/user-upload4.mp3',
    is_public: true,
    created_at: '2024-01-13T11:15:00Z',
    play_count: 78,
    like_count: 15,
    upload_status: 'completed',
    file_size: 4200000, // ~4.2MB
    processing_time: 150
  }
]

// Mock Favorite Tracks
export const mockFavorites: FavoriteTrack[] = [
  {
    id: 'fav-1',
    title: 'Midnight Synthwave',
    artist_name: 'Retro AI',
    genre: 'electronic',
    duration: 260,
    ai_tool: 'suno',
    file_url: '/audio/fav1.mp3',
    is_public: true,
    created_at: '2024-01-10T20:00:00Z',
    play_count: 3200,
    like_count: 245,
    liked_at: '2024-01-12T15:30:00Z',
    is_featured: true
  },
  {
    id: 'fav-2',
    title: 'Ocean Depths',
    artist_name: 'Aquatic Sounds',
    genre: 'ambient',
    duration: 340,
    ai_tool: 'udio',
    file_url: '/audio/fav2.mp3',
    is_public: true,
    created_at: '2024-01-08T14:20:00Z',
    play_count: 1890,
    like_count: 134,
    liked_at: '2024-01-11T09:45:00Z',
    is_featured: false
  },
  {
    id: 'fav-3',
    title: 'Quantum Beats',
    artist_name: 'Future Bass AI',
    genre: 'electronic',
    duration: 220,
    ai_tool: 'mubert',
    file_url: '/audio/fav3.mp3',
    is_public: true,
    created_at: '2024-01-05T16:30:00Z',
    play_count: 2750,
    like_count: 189,
    liked_at: '2024-01-10T12:20:00Z',
    is_featured: true
  },
  {
    id: 'fav-4',
    title: 'Classical Reimagined',
    artist_name: 'AI Orchestra',
    genre: 'classical',
    duration: 380,
    ai_tool: 'aiva',
    file_url: '/audio/fav4.mp3',
    is_public: true,
    created_at: '2024-01-03T10:15:00Z',
    play_count: 1560,
    like_count: 98,
    liked_at: '2024-01-09T18:10:00Z',
    is_featured: false
  },
  {
    id: 'fav-5',
    title: 'Hip Hop Revolution',
    artist_name: 'Beat Machine',
    genre: 'hip-hop',
    duration: 190,
    ai_tool: 'custom',
    file_url: '/audio/fav5.mp3',
    is_public: true,
    created_at: '2024-01-01T22:00:00Z',
    play_count: 4100,
    like_count: 312,
    liked_at: '2024-01-08T14:30:00Z',
    is_featured: true
  }
]

// Mock Personalized Recommendations
export const mockRecommendations: PersonalizedRecommendation[] = [
  {
    track: {
      id: 'rec-1',
      title: 'Synthwave Nights',
      artist_name: 'Neon Dreams',
      genre: 'electronic',
      duration: 245,
      ai_tool: 'suno',
      file_url: '/audio/rec1.mp3',
      is_public: true,
      created_at: '2024-01-14T19:00:00Z',
      play_count: 890,
      like_count: 67
    },
    reason: 'similar_genre',
    confidence_score: 0.92,
    explanation: 'Based on your love for electronic music, especially tracks like "Neon Dreams"'
  },
  {
    track: {
      id: 'rec-2',
      title: 'Ambient Meditation',
      artist_name: 'Zen AI',
      genre: 'ambient',
      duration: 420,
      ai_tool: 'udio',
      file_url: '/audio/rec2.mp3',
      is_public: true,
      created_at: '2024-01-14T16:30:00Z',
      play_count: 1250,
      like_count: 89
    },
    reason: 'similar_mood',
    confidence_score: 0.87,
    explanation: 'Perfect for your relaxing listening sessions, similar to "Ocean Depths"'
  },
  {
    track: {
      id: 'rec-3',
      title: 'Trending Now',
      artist_name: 'Viral Beats',
      genre: 'pop',
      duration: 180,
      ai_tool: 'mubert',
      file_url: '/audio/rec3.mp3',
      is_public: true,
      created_at: '2024-01-15T12:00:00Z',
      play_count: 5600,
      like_count: 423
    },
    reason: 'trending',
    confidence_score: 0.78,
    explanation: 'This track is trending among users with similar taste to yours'
  },
  {
    track: {
      id: 'rec-4',
      title: 'Jazz Fusion Future',
      artist_name: 'AI Orchestra',
      genre: 'jazz',
      duration: 290,
      ai_tool: 'aiva',
      file_url: '/audio/rec4.mp3',
      is_public: true,
      created_at: '2024-01-13T14:45:00Z',
      play_count: 720,
      like_count: 54
    },
    reason: 'similar_artist',
    confidence_score: 0.85,
    explanation: 'From AI Orchestra, who created "Classical Reimagined" which you loved'
  },
  {
    track: {
      id: 'rec-5',
      title: 'Experimental Soundscape',
      artist_name: 'Sound Explorer',
      genre: 'experimental',
      duration: 350,
      ai_tool: 'custom',
      file_url: '/audio/rec5.mp3',
      is_public: true,
      created_at: '2024-01-12T20:15:00Z',
      play_count: 1100,
      like_count: 78
    },
    reason: 'similar_genre',
    confidence_score: 0.81,
    explanation: 'Experimental music like "Cosmic Journey" that you recently enjoyed'
  },
  {
    track: {
      id: 'rec-6',
      title: 'Chill Vibes Only',
      artist_name: 'Relaxation AI',
      genre: 'ambient',
      duration: 280,
      ai_tool: 'udio',
      file_url: '/audio/rec6.mp3',
      is_public: true,
      created_at: '2024-01-11T11:30:00Z',
      play_count: 1890,
      like_count: 142
    },
    reason: 'similar_mood',
    confidence_score: 0.89,
    explanation: 'Matches your preference for calming, atmospheric tracks'
  },
  {
    track: {
      id: 'rec-7',
      title: 'Electronic Pulse',
      artist_name: 'Digital Waves',
      genre: 'electronic',
      duration: 210,
      ai_tool: 'suno',
      file_url: '/audio/rec7.mp3',
      is_public: true,
      created_at: '2024-01-10T15:20:00Z',
      play_count: 2340,
      like_count: 178
    },
    reason: 'similar_genre',
    confidence_score: 0.94,
    explanation: 'High-energy electronic music that aligns with your listening patterns'
  },
  {
    track: {
      id: 'rec-8',
      title: 'Viral Hit 2024',
      artist_name: 'Chart Toppers',
      genre: 'pop',
      duration: 195,
      ai_tool: 'mubert',
      file_url: '/audio/rec8.mp3',
      is_public: true,
      created_at: '2024-01-15T08:00:00Z',
      play_count: 8900,
      like_count: 567
    },
    reason: 'trending',
    confidence_score: 0.76,
    explanation: 'Currently the most popular track among users in your region'
  }
]

// Mock Recent Playlists
export const mockRecentPlaylists: Playlist[] = [
  {
    id: 'playlist-1',
    name: 'Chill Vibes',
    description: 'Perfect for relaxing and unwinding after a long day',
    user_id: 'current-user',
    is_public: true,
    is_collaborative: false,
    cover_image_url: null,
    created_at: '2024-01-14T10:00:00Z',
    updated_at: '2024-01-15T14:30:00Z',
    track_count: 12,
    total_duration: 2880 // 48 minutes
  },
  {
    id: 'playlist-2',
    name: 'Electronic Favorites',
    description: 'My collection of the best AI-generated electronic music',
    user_id: 'current-user',
    is_public: false,
    is_collaborative: false,
    cover_image_url: '/images/playlist-electronic.jpg',
    created_at: '2024-01-12T16:20:00Z',
    updated_at: '2024-01-14T09:15:00Z',
    track_count: 8,
    total_duration: 1920 // 32 minutes
  },
  {
    id: 'playlist-3',
    name: 'Collaborative Mix',
    description: 'A shared playlist with friends for discovering new music',
    user_id: 'current-user',
    is_public: true,
    is_collaborative: true,
    cover_image_url: null,
    created_at: '2024-01-10T14:45:00Z',
    updated_at: '2024-01-15T11:20:00Z',
    track_count: 24,
    total_duration: 5760 // 96 minutes
  },
  {
    id: 'playlist-4',
    name: 'Work Focus',
    description: 'Instrumental tracks for productivity and concentration',
    user_id: 'current-user',
    is_public: false,
    is_collaborative: false,
    cover_image_url: '/images/playlist-focus.jpg',
    created_at: '2024-01-08T09:30:00Z',
    updated_at: '2024-01-13T16:45:00Z',
    track_count: 15,
    total_duration: 4500 // 75 minutes
  },
  {
    id: 'playlist-5',
    name: 'Experimental Journey',
    description: 'Exploring the boundaries of AI-generated music',
    user_id: 'current-user',
    is_public: true,
    is_collaborative: false,
    cover_image_url: null,
    created_at: '2024-01-05T12:15:00Z',
    updated_at: '2024-01-12T18:30:00Z',
    track_count: 6,
    total_duration: 1800 // 30 minutes
  }
]

// Helper function to generate more mock data
export const generateMockRecentlyPlayed = (count: number): RecentlyPlayedTrack[] => {
  const genres = ['electronic', 'ambient', 'experimental', 'jazz', 'classical', 'hip-hop', 'pop']
  const aiTools = ['suno', 'udio', 'mubert', 'aiva', 'custom']
  const sources = ['playlist', 'search', 'recommendations', 'browse', 'direct']
  const artists = ['AI Composer', 'Digital Dreams', 'Synth Master', 'Beat Creator', 'Sound Explorer']

  return Array.from({ length: count }, (_, i) => ({
    id: `generated-${i}`,
    title: `Generated Track ${i + 1}`,
    artist_name: artists[i % artists.length],
    genre: genres[i % genres.length],
    duration: 180 + Math.floor(Math.random() * 240), // 3-7 minutes
    ai_tool: aiTools[i % aiTools.length],
    file_url: `/audio/generated-${i}.mp3`,
    is_public: Math.random() > 0.3,
    created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    play_count: Math.floor(Math.random() * 5000),
    like_count: Math.floor(Math.random() * 500),
    listened_at: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    duration_listened: Math.floor(Math.random() * 300),
    completed: Math.random() > 0.4,
    source: sources[i % sources.length] as any,
    playlist_name: Math.random() > 0.6 ? `Playlist ${i % 5 + 1}` : undefined
  }))
}

// Export development mode check
export const isDevelopmentMode = () => {
  return process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true'
} 