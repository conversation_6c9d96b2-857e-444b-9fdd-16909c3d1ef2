# Cursor IDE Integration with Tunami MCP Servers

This guide explains how to integrate the Tunami MCP servers with Cursor IDE for enhanced AI-powered development.

## 🎯 Integration Approaches

### 1. **Direct MCP Integration (Recommended)**

Cursor supports MCP through its AI chat interface. You can configure MCP servers to work directly with Cursor's AI models.

#### Configuration Steps:

1. **Create Cursor MCP Configuration**
   
   Create a file at `~/.cursor/mcp_config.json` (macOS/Linux) or `%APPDATA%\Cursor\mcp_config.json` (Windows):

```json
{
  "mcpServers": {
    "tunami-filesystem": {
      "command": "node",
      "args": ["D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers\\filesystem-server.js"],
      "cwd": "D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers",
      "description": "Secure filesystem operations for Tunami project"
    },
    "tunami-memory": {
      "command": "node", 
      "args": ["D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers\\memory-server.js"],
      "cwd": "D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers",
      "description": "Persistent memory for user preferences and analytics"
    },
    "tunami-fetch": {
      "command": "node",
      "args": ["D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers\\fetch-server.js"],
      "cwd": "D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers",
      "description": "Music metadata and lyrics fetching"
    },
    "tunami-github": {
      "command": "node",
      "args": ["D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers\\github-server.js"],
      "cwd": "D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers",
      "env": {
        "GITHUB_TOKEN": "your_github_token_here"
      },
      "description": "GitHub repository management and API integration"
    },
    "tunami-postgres": {
      "command": "node",
      "args": ["D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers\\postgres-server.js"],
      "cwd": "D:\\Vinod\\Work\\Cursor_Projects\\Tunami\\mcp-servers",
      "env": {
        "SUPABASE_DB_HOST": "your_supabase_host",
        "SUPABASE_DB_USER": "your_username",
        "SUPABASE_DB_PASSWORD": "your_password"
      },
      "description": "Supabase PostgreSQL database operations"
    }
  }
}
```

### 2. **Cursor Rules Integration**

Create a `.cursorrules` file in your project root to define how Cursor should interact with your MCP servers:

```markdown
# Tunami Project - Cursor Rules with MCP Integration

## Project Context
This is a Next.js 14 music streaming platform called Tunami with TypeScript, Supabase, and Tailwind CSS.

## MCP Server Tools Available

### Filesystem Operations (tunami-filesystem)
- read_file: Read files from allowed directories (src, public, uploads, logs)
- write_file: Write files securely
- list_directory: List directory contents
- create_directory: Create new directories
- get_file_info: Get file metadata

### Memory Management (tunami-memory)
- store_memory: Store data in categories (userPreferences, playlists, sessions, analytics, recommendations)
- retrieve_memory: Get stored memories
- search_memories: Search across memory categories
- list_memories: List all memories by category

### Music Data Fetching (tunami-fetch)
- fetch_music_metadata: Get metadata from MusicBrainz, Last.fm
- fetch_lyrics: Retrieve song lyrics
- fetch_album_art: Get album artwork URLs
- fetch_url: General web requests to music domains

### GitHub Integration (tunami-github)
- get_repository: Get repo information
- list_repository_files: Browse repo contents
- get_file_content: Read file contents from GitHub
- list_issues: View GitHub issues
- create_issue: Create new issues
- get_commits: View commit history

### Database Operations (tunami-postgres)
- list_tables: Show database tables
- describe_table: Get table schema
- execute_query: Run SELECT queries (read-only)
- get_table_stats: Database statistics

## Development Guidelines

1. **Use MCP tools** when working with:
   - File operations (prefer tunami-filesystem over direct file access)
   - Storing user preferences or analytics (use tunami-memory)
   - Fetching music metadata (use tunami-fetch)
   - GitHub operations (use tunami-github)
   - Database queries (use tunami-postgres)

2. **Security**: All MCP servers have built-in security restrictions
   - Filesystem: Limited to specific directories
   - Fetch: Restricted to music-related domains
   - Postgres: Read-only queries only

3. **Error Handling**: Always handle MCP tool errors gracefully

4. **Performance**: Use memory server to cache frequently accessed data
```

### 3. **VS Code Extension Approach**

Since Cursor is based on VS Code, you can also use MCP through extensions:

1. **Install MCP Extension** (if available in Cursor marketplace)
2. **Configure workspace settings** in `.vscode/settings.json`:

```json
{
  "mcp.servers": {
    "tunami-filesystem": {
      "command": "node",
      "args": ["./mcp-servers/filesystem-server.js"],
      "cwd": "./mcp-servers"
    },
    "tunami-memory": {
      "command": "node",
      "args": ["./mcp-servers/memory-server.js"],
      "cwd": "./mcp-servers"
    },
    "tunami-fetch": {
      "command": "node",
      "args": ["./mcp-servers/fetch-server.js"],
      "cwd": "./mcp-servers"
    }
  }
}
```

### 4. **HTTP Server Approach**

For more flexibility, run MCP servers as HTTP services:

#### Create HTTP Wrapper

```javascript
// mcp-servers/http-server.js
import express from 'express';
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";

const app = express();
app.use(express.json());

// Import your MCP servers
import './filesystem-server.js';
import './memory-server.js';
import './fetch-server.js';

const PORT = process.env.MCP_PORT || 3100;

app.listen(PORT, () => {
  console.log(`Tunami MCP HTTP Server running on port ${PORT}`);
});
```

Then configure Cursor to use HTTP endpoints instead of stdio.

## 🚀 Usage Examples

### In Cursor Chat

You can now ask Cursor to use these tools:

```
"Use the tunami-memory server to store the user's preferred audio quality setting as 'high'"

"Fetch lyrics for 'Bohemian Rhapsody' by Queen using the tunami-fetch server"

"List all files in the src/components directory using tunami-filesystem"

"Check the recent commits in our GitHub repository using tunami-github"

"Show me the structure of the tracks table using tunami-postgres"
```

### In Code Comments

```typescript
// @mcp tunami-memory: store user preference for dark mode
// @mcp tunami-fetch: get album art for this track
// @mcp tunami-postgres: query user's favorite tracks
```

## 🔧 Advanced Configuration

### Environment Variables

Create `.env` file in mcp-servers directory:

```env
# Copy from env.example and fill in your values
GITHUB_TOKEN=your_github_token
SUPABASE_DB_HOST=your_supabase_host
SUPABASE_DB_USER=your_username
SUPABASE_DB_PASSWORD=your_password
```

### Custom Prompts

Add to your `.cursorrules`:

```markdown
## MCP Integration Prompts

When I ask about:
- "user data" or "preferences" → Use tunami-memory server
- "music info" or "lyrics" → Use tunami-fetch server  
- "file operations" → Use tunami-filesystem server
- "GitHub" or "repository" → Use tunami-github server
- "database" or "tables" → Use tunami-postgres server
```

## 🛠️ Troubleshooting

### Common Issues

1. **Server not starting**: Check Node.js version (18+) and dependencies
2. **Permission errors**: Ensure proper file permissions on server files
3. **Environment variables**: Verify .env file is properly configured
4. **Port conflicts**: Change MCP_PORT if 3100 is in use

### Testing MCP Servers

```bash
# Test individual servers
cd mcp-servers
npm run start:memory
npm run start:filesystem
npm run start:fetch
```

### Debug Mode

Add to your MCP server files:

```javascript
// Add debug logging
console.error(`[DEBUG] ${server.name} tool called:`, toolName, params);
```

## 📚 Benefits of MCP Integration with Cursor

1. **Enhanced Context**: AI has access to project-specific tools
2. **Secure Operations**: Built-in security restrictions
3. **Persistent Memory**: Remember user preferences across sessions
4. **External APIs**: Fetch music data without manual API calls
5. **Database Integration**: Query database through natural language
6. **GitHub Integration**: Manage repository through AI chat

## 🔄 Next Steps

1. Configure your preferred integration method
2. Test MCP servers individually
3. Update environment variables
4. Start using MCP tools in Cursor chat
5. Customize .cursorrules for your workflow

This integration will significantly enhance your development experience by giving Cursor's AI direct access to your project's tools and data sources! 