import { Track } from './track'

export interface Playlist {
  id: string
  user_id: string
  name: string
  description?: string
  is_public: boolean
  cover_image_url?: string
  created_at: string
  updated_at: string
  track_count?: number
  total_duration?: number
}

export interface PlaylistTrack {
  id: string
  playlist_id: string
  track_id: string
  position: number
  added_at: string
  track?: Track
}

export interface PlaylistWithTracks extends Playlist {
  tracks: PlaylistTrack[]
}

export interface CreatePlaylistData {
  name: string
  description?: string
  is_public?: boolean
  cover_image_url?: string
}

export interface UpdatePlaylistData {
  name?: string
  description?: string
  is_public?: boolean
  cover_image_url?: string
}

export interface AddTrackToPlaylistData {
  playlist_id: string
  track_id: string
  position?: number
}

export interface ReorderTrackData {
  playlist_id: string
  track_id: string
  new_position: number
}

export interface PlaylistStats {
  track_count: number
  total_duration: number
  last_updated: string
}

export interface PlaylistFilters {
  search?: string
  is_public?: boolean
  user_id?: string
  sort_by?: 'name' | 'created_at' | 'updated_at' | 'track_count'
  sort_order?: 'asc' | 'desc'
}

export interface PlaylistContextType {
  playlists: Playlist[]
  currentPlaylist: PlaylistWithTracks | null
  loading: boolean
  error: string | null
  
  // Playlist CRUD operations
  createPlaylist: (data: CreatePlaylistData) => Promise<Playlist>
  updatePlaylist: (id: string, data: UpdatePlaylistData) => Promise<Playlist>
  deletePlaylist: (id: string) => Promise<void>
  getPlaylist: (id: string) => Promise<PlaylistWithTracks>
  getUserPlaylists: (userId?: string) => Promise<Playlist[]>
  getPublicPlaylists: (filters?: PlaylistFilters) => Promise<Playlist[]>
  
  // Track operations
  addTrackToPlaylist: (data: AddTrackToPlaylistData) => Promise<void>
  removeTrackFromPlaylist: (playlistId: string, trackId: string) => Promise<void>
  reorderPlaylistTracks: (playlistId: string, tracks: ReorderTrackData[]) => Promise<void>
  
  // Utility functions
  refreshPlaylists: () => Promise<void>
  clearError: () => void
}

export interface DragDropResult {
  draggableId: string
  type: string
  source: {
    droppableId: string
    index: number
  }
  destination?: {
    droppableId: string
    index: number
  } | null
  reason: 'DROP' | 'CANCEL'
} 