#!/usr/bin/env node

/**
 * Browser Compatibility Test Runner
 * Runs cross-browser tests and generates compatibility reports
 */

const { exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

class BrowserTestRunner {
  constructor() {
    this.browsers = ['chromium', 'firefox', 'webkit'];
    this.mobileDevices = ['mobile-chrome', 'mobile-safari'];
    this.reportDir = 'browser-test-reports';
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  }

  async run() {
    console.log('🧪 Starting comprehensive browser compatibility testing...\n');

    try {
      // Ensure report directory exists
      await this.ensureReportDirectory();

      // Run tests for each browser
      const results = await this.runAllBrowserTests();

      // Generate comprehensive report
      await this.generateReport(results);

      console.log('\n✅ Browser compatibility testing completed!');
      console.log(`📊 Reports available in: ${this.reportDir}/`);

    } catch (error) {
      console.error('❌ Browser testing failed:', error.message);
      process.exit(1);
    }
  }

  async ensureReportDirectory() {
    try {
      await fs.mkdir(this.reportDir, { recursive: true });
    } catch (error) {
      // Directory already exists
    }
  }

  async runAllBrowserTests() {
    const results = {};

    // Desktop browsers
    for (const browser of this.browsers) {
      console.log(`🌐 Testing ${browser}...`);
      try {
        const result = await this.runBrowserTest(browser);
        results[browser] = {
          ...result,
          type: 'desktop',
          success: true
        };
        console.log(`  ✅ ${browser} tests completed`);
      } catch (error) {
        console.log(`  ❌ ${browser} tests failed: ${error.message}`);
        results[browser] = {
          type: 'desktop',
          success: false,
          error: error.message
        };
      }
    }

    // Mobile devices
    for (const device of this.mobileDevices) {
      console.log(`📱 Testing ${device}...`);
      try {
        const result = await this.runBrowserTest(device);
        results[device] = {
          ...result,
          type: 'mobile',
          success: true
        };
        console.log(`  ✅ ${device} tests completed`);
      } catch (error) {
        console.log(`  ❌ ${device} tests failed: ${error.message}`);
        results[device] = {
          type: 'mobile',
          success: false,
          error: error.message
        };
      }
    }

    return results;
  }

  runBrowserTest(browser) {
    return new Promise((resolve, reject) => {
      const command = `npx playwright test --project=${browser} --reporter=json`;
      
      exec(command, { timeout: 60000 }, (error, stdout, stderr) => {
        if (error && !stdout) {
          reject(error);
          return;
        }

        try {
          // Parse test results
          const testResults = this.parseTestOutput(stdout, stderr);
          resolve(testResults);
        } catch (parseError) {
          console.warn(`Warning: Could not parse test results for ${browser}:`, parseError.message);
          resolve({
            tests: [],
            summary: { passed: 0, failed: 0, skipped: 0 },
            output: stdout,
            errors: stderr
          });
        }
      });
    });
  }

  parseTestOutput(stdout, stderr) {
    const results = {
      tests: [],
      summary: { passed: 0, failed: 0, skipped: 0 },
      output: stdout,
      errors: stderr
    };

    try {
      // Try to parse JSON output
      if (stdout.includes('{')) {
        const jsonStart = stdout.indexOf('{');
        const jsonOutput = stdout.substring(jsonStart);
        const testData = JSON.parse(jsonOutput);
        
        if (testData.suites) {
          testData.suites.forEach(suite => {
            suite.specs?.forEach(spec => {
              spec.tests?.forEach(test => {
                results.tests.push({
                  title: test.title,
                  status: test.results[0]?.status || 'unknown',
                  duration: test.results[0]?.duration || 0,
                  error: test.results[0]?.error?.message
                });

                // Update summary
                const status = test.results[0]?.status;
                if (status === 'passed') results.summary.passed++;
                else if (status === 'failed') results.summary.failed++;
                else if (status === 'skipped') results.summary.skipped++;
              });
            });
          });
        }
      }
    } catch (error) {
      console.warn('Could not parse JSON test output:', error.message);
    }

    return results;
  }

  async generateReport(results) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.generateSummary(results),
      browsers: results,
      recommendations: this.generateRecommendations(results),
      compatibility: this.analyzeCompatibility(results)
    };

    // Generate HTML report
    const htmlReport = this.generateHTMLReport(report);
    await fs.writeFile(
      path.join(this.reportDir, `browser-compatibility-${this.timestamp}.html`),
      htmlReport
    );

    // Generate JSON report
    await fs.writeFile(
      path.join(this.reportDir, `browser-compatibility-${this.timestamp}.json`),
      JSON.stringify(report, null, 2)
    );

    // Generate summary report
    const summaryReport = this.generateSummaryReport(report);
    await fs.writeFile(
      path.join(this.reportDir, `summary-${this.timestamp}.md`),
      summaryReport
    );

    console.log('\n📊 Generated reports:');
    console.log(`  - HTML: browser-compatibility-${this.timestamp}.html`);
    console.log(`  - JSON: browser-compatibility-${this.timestamp}.json`);
    console.log(`  - Summary: summary-${this.timestamp}.md`);
  }

  generateSummary(results) {
    const summary = {
      totalBrowsers: Object.keys(results).length,
      successfulTests: 0,
      failedTests: 0,
      desktopBrowsers: 0,
      mobileBrowsers: 0,
      overallCompatibility: 0
    };

    Object.values(results).forEach(result => {
      if (result.success) {
        summary.successfulTests++;
      } else {
        summary.failedTests++;
      }

      if (result.type === 'desktop') {
        summary.desktopBrowsers++;
      } else {
        summary.mobileBrowsers++;
      }
    });

    summary.overallCompatibility = Math.round(
      (summary.successfulTests / summary.totalBrowsers) * 100
    );

    return summary;
  }

  generateRecommendations(results) {
    const recommendations = [];

    // Analyze failures
    Object.entries(results).forEach(([browser, result]) => {
      if (!result.success) {
        recommendations.push({
          type: 'error',
          browser,
          message: `${browser} testing failed: ${result.error}`,
          action: 'Check browser compatibility and fix blocking issues'
        });
      } else if (result.summary?.failed > 0) {
        recommendations.push({
          type: 'warning',
          browser,
          message: `${result.summary.failed} tests failed in ${browser}`,
          action: 'Review failed tests and implement browser-specific fixes'
        });
      }
    });

    // Audio compatibility recommendations
    const audioIssues = Object.entries(results).filter(([_, result]) => 
      result.tests?.some(test => 
        test.title.includes('audio') && test.status === 'failed'
      )
    );

    if (audioIssues.length > 0) {
      recommendations.push({
        type: 'warning',
        browser: 'multiple',
        message: 'Audio compatibility issues detected',
        action: 'Implement audio format fallbacks and browser-specific audio fixes'
      });
    }

    // Mobile-specific recommendations
    const mobileIssues = Object.entries(results).filter(([browser, result]) => 
      result.type === 'mobile' && (!result.success || result.summary?.failed > 0)
    );

    if (mobileIssues.length > 0) {
      recommendations.push({
        type: 'warning',
        browser: 'mobile',
        message: 'Mobile browser compatibility issues detected',
        action: 'Review mobile-specific CSS and JavaScript fixes'
      });
    }

    return recommendations;
  }

  analyzeCompatibility(results) {
    const compatibility = {
      audioSupport: this.analyzeAudioSupport(results),
      webFeatures: this.analyzeWebFeatures(results),
      cssSupport: this.analyzeCSSSupport(results),
      performanceScores: this.analyzePerformance(results)
    };

    return compatibility;
  }

  analyzeAudioSupport(results) {
    const audioSupport = {};
    
    Object.entries(results).forEach(([browser, result]) => {
      const audioTests = result.tests?.filter(test => 
        test.title.toLowerCase().includes('audio')
      ) || [];

      audioSupport[browser] = {
        totalTests: audioTests.length,
        passed: audioTests.filter(test => test.status === 'passed').length,
        failed: audioTests.filter(test => test.status === 'failed').length,
        score: audioTests.length > 0 ? 
          Math.round((audioTests.filter(test => test.status === 'passed').length / audioTests.length) * 100) : 0
      };
    });

    return audioSupport;
  }

  analyzeWebFeatures(results) {
    // Analyze web feature support based on test results
    return Object.entries(results).reduce((acc, [browser, result]) => {
      acc[browser] = {
        serviceWorker: this.hasPassingTest(result, 'service worker'),
        webAudio: this.hasPassingTest(result, 'web audio'),
        intersectionObserver: this.hasPassingTest(result, 'intersection observer'),
        localStorage: this.hasPassingTest(result, 'local storage')
      };
      return acc;
    }, {});
  }

  analyzeCSSSupport(results) {
    // Analyze CSS feature support
    return Object.entries(results).reduce((acc, [browser, result]) => {
      acc[browser] = {
        grid: this.hasPassingTest(result, 'css grid'),
        flexbox: this.hasPassingTest(result, 'flexbox'),
        customProperties: this.hasPassingTest(result, 'custom properties'),
        backdropFilter: this.hasPassingTest(result, 'backdrop filter')
      };
      return acc;
    }, {});
  }

  analyzePerformance(results) {
    return Object.entries(results).reduce((acc, [browser, result]) => {
      const performanceTests = result.tests?.filter(test => 
        test.title.toLowerCase().includes('performance') ||
        test.title.toLowerCase().includes('loading')
      ) || [];

      acc[browser] = {
        averageDuration: performanceTests.length > 0 ?
          Math.round(performanceTests.reduce((sum, test) => sum + (test.duration || 0), 0) / performanceTests.length) : 0,
        testsCount: performanceTests.length
      };
      return acc;
    }, {});
  }

  hasPassingTest(result, testName) {
    return result.tests?.some(test => 
      test.title.toLowerCase().includes(testName.toLowerCase()) && 
      test.status === 'passed'
    ) || false;
  }

  generateHTMLReport(report) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tunami - Browser Compatibility Report</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.6; color: #333; background: #f5f5f5; 
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #8b5cf6; color: white; padding: 2rem; border-radius: 8px; margin-bottom: 2rem; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .card { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { font-size: 2rem; font-weight: bold; color: #8b5cf6; }
        .browser-results { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .success { color: #10b981; }
        .warning { color: #f59e0b; }
        .error { color: #ef4444; }
        .progress { background: #e5e5e5; border-radius: 4px; height: 8px; overflow: hidden; }
        .progress-bar { height: 100%; transition: width 0.3s ease; }
        .recommendations { background: white; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; }
        .recommendation { padding: 1rem; margin: 0.5rem 0; border-left: 4px solid; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e5e5; }
        th { background: #f8f9fa; font-weight: 600; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Tunami Browser Compatibility Report</h1>
            <p>Generated on ${new Date(report.timestamp).toLocaleString()}</p>
        </div>

        <div class="summary">
            <div class="card">
                <h3>Overall Compatibility</h3>
                <div class="metric">${report.summary.overallCompatibility}%</div>
                <div class="progress">
                    <div class="progress-bar" style="width: ${report.summary.overallCompatibility}%; background: ${report.summary.overallCompatibility > 80 ? '#10b981' : report.summary.overallCompatibility > 60 ? '#f59e0b' : '#ef4444'};"></div>
                </div>
            </div>
            <div class="card">
                <h3>Browsers Tested</h3>
                <div class="metric">${report.summary.totalBrowsers}</div>
                <p>${report.summary.desktopBrowsers} Desktop, ${report.summary.mobileBrowsers} Mobile</p>
            </div>
            <div class="card">
                <h3>Test Results</h3>
                <div class="metric ${report.summary.successfulTests === report.summary.totalBrowsers ? 'success' : 'warning'}">
                    ${report.summary.successfulTests}/${report.summary.totalBrowsers}
                </div>
                <p>Successful Tests</p>
            </div>
        </div>

        <div class="browser-results">
            ${Object.entries(report.browsers).map(([browser, result]) => `
                <div class="card">
                    <h3>${browser.charAt(0).toUpperCase() + browser.slice(1)}</h3>
                    <p class="${result.success ? 'success' : 'error'}">
                        ${result.success ? '✅ Tests Passed' : '❌ Tests Failed'}
                    </p>
                    ${result.summary ? `
                        <p>Passed: ${result.summary.passed}, Failed: ${result.summary.failed}, Skipped: ${result.summary.skipped}</p>
                    ` : ''}
                    ${result.error ? `<p class="error">Error: ${result.error}</p>` : ''}
                </div>
            `).join('')}
        </div>

        ${report.recommendations.length > 0 ? `
            <div class="recommendations">
                <h2>📋 Recommendations</h2>
                ${report.recommendations.map(rec => `
                    <div class="recommendation" style="border-color: ${rec.type === 'error' ? '#ef4444' : '#f59e0b'}; background: ${rec.type === 'error' ? '#fef2f2' : '#fffbeb'};">
                        <strong>${rec.browser}: ${rec.message}</strong>
                        <p>${rec.action}</p>
                    </div>
                `).join('')}
            </div>
        ` : ''}

        <div class="card">
            <h2>🔊 Audio Compatibility</h2>
            <table>
                <thead>
                    <tr>
                        <th>Browser</th>
                        <th>Audio Tests</th>
                        <th>Passed</th>
                        <th>Failed</th>
                        <th>Score</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.entries(report.compatibility.audioSupport).map(([browser, audio]) => `
                        <tr>
                            <td>${browser}</td>
                            <td>${audio.totalTests}</td>
                            <td class="success">${audio.passed}</td>
                            <td class="${audio.failed > 0 ? 'error' : ''}">${audio.failed}</td>
                            <td class="${audio.score > 80 ? 'success' : audio.score > 60 ? 'warning' : 'error'}">${audio.score}%</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>`;
  }

  generateSummaryReport(report) {
    return `# Browser Compatibility Report - Tunami

**Generated:** ${new Date(report.timestamp).toLocaleString()}

## Summary

- **Overall Compatibility:** ${report.summary.overallCompatibility}%
- **Browsers Tested:** ${report.summary.totalBrowsers} (${report.summary.desktopBrowsers} Desktop, ${report.summary.mobileBrowsers} Mobile)
- **Successful Tests:** ${report.summary.successfulTests}/${report.summary.totalBrowsers}

## Browser Results

${Object.entries(report.browsers).map(([browser, result]) => `
### ${browser.charAt(0).toUpperCase() + browser.slice(1)} ${result.type === 'mobile' ? '📱' : '🖥️'}

- **Status:** ${result.success ? '✅ Passed' : '❌ Failed'}
${result.summary ? `- **Tests:** ${result.summary.passed} passed, ${result.summary.failed} failed, ${result.summary.skipped} skipped` : ''}
${result.error ? `- **Error:** ${result.error}` : ''}
`).join('')}

## Audio Compatibility

${Object.entries(report.compatibility.audioSupport).map(([browser, audio]) => `
- **${browser}:** ${audio.score}% (${audio.passed}/${audio.totalTests} tests passed)
`).join('')}

${report.recommendations.length > 0 ? `
## Recommendations

${report.recommendations.map(rec => `
- **${rec.browser}:** ${rec.message}
  - *Action:* ${rec.action}
`).join('')}
` : ''}

---
*Report generated by Tunami Browser Compatibility Test Runner*
`;
  }
}

// Run the test runner
if (require.main === module) {
  const runner = new BrowserTestRunner();
  runner.run().catch(console.error);
}

module.exports = BrowserTestRunner; 