'use client'

import { useEffect, useState } from 'react'
import { AudioTrack } from '@/types/audio'

interface MobileNotificationsConfig {
  track?: AudioTrack
  isPlaying: boolean
  onPlay: () => void
  onPause: () => void
  onNext?: () => void
  onPrevious?: () => void
}

interface NotificationState {
  permission: NotificationPermission
  isSupported: boolean
}

export function useMobileNotifications({
  track,
  isPlaying,
  onPlay,
  onPause,
  onNext,
  onPrevious
}: MobileNotificationsConfig) {
  const [notificationState, setNotificationState] = useState<NotificationState>({
    permission: 'default',
    isSupported: false
  })

  // Check notification support and permission
  useEffect(() => {
    const isSupported = 'Notification' in window
    
    setNotificationState({
      permission: isSupported ? Notification.permission : 'denied',
      isSupported
    })
  }, [])

  // Request notification permission
  const requestPermission = async (): Promise<boolean> => {
    if (!notificationState.isSupported) {
      return false
    }

    try {
      const permission = await Notification.requestPermission()
      setNotificationState(prev => ({ ...prev, permission }))
      return permission === 'granted'
    } catch (error) {
      console.warn('Failed to request notification permission:', error)
      return false
    }
  }

  // Show notification for track changes
  useEffect(() => {
    if (!track || !notificationState.isSupported || notificationState.permission !== 'granted') {
      return
    }

    // Only show notification when track starts playing
    if (!isPlaying) return

    const notification = new Notification(`♪ ${track.title}`, {
      body: track.artist + (track.aiTool ? ` • Generated with ${track.aiTool}` : ''),
      icon: track.coverArt || '/images/tunami-icon-192.png',
      badge: '/images/tunami-badge-72.png',
      tag: 'tunami-now-playing', // Replace previous notifications
      silent: true, // Don't play notification sound
      requireInteraction: false,
      actions: [
        {
          action: 'play-pause',
          title: isPlaying ? 'Pause' : 'Play',
          icon: isPlaying ? '/images/pause-icon.png' : '/images/play-icon.png'
        },
        {
          action: 'next',
          title: 'Next',
          icon: '/images/next-icon.png'
        }
      ]
    })

    // Handle notification clicks
    notification.onclick = () => {
      // Focus the window when notification is clicked
      window.focus()
      notification.close()
    }

    // Auto-close notification after 4 seconds
    setTimeout(() => {
      notification.close()
    }, 4000)

    return () => {
      notification.close()
    }
  }, [track, isPlaying, notificationState])

  // Handle notification actions (for browsers that support it)
  useEffect(() => {
    if (!notificationState.isSupported) return

    const handleNotificationAction = (event: any) => {
      switch (event.action) {
        case 'play-pause':
          if (isPlaying) {
            onPause()
          } else {
            onPlay()
          }
          break
        case 'next':
          onNext?.()
          break
        case 'previous':
          onPrevious?.()
          break
      }
    }

    // Listen for notification action events
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('notificationclick', handleNotificationAction)
      
      return () => {
        navigator.serviceWorker.removeEventListener('notificationclick', handleNotificationAction)
      }
    }
  }, [isPlaying, onPlay, onPause, onNext, onPrevious, notificationState.isSupported])

  // Show persistent notification for background playback
  const showPersistentNotification = async (track: AudioTrack, isPlaying: boolean) => {
    if (!notificationState.isSupported || notificationState.permission !== 'granted') {
      return
    }

    // Use service worker for persistent notifications if available
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.ready
      
      await registration.showNotification(`♪ Tunami - ${track.title}`, {
        body: track.artist,
        icon: track.coverArt || '/images/tunami-icon-192.png',
        badge: '/images/tunami-badge-72.png',
        tag: 'tunami-persistent',
        persistent: true,
        requireInteraction: false,
        actions: [
          {
            action: 'play-pause',
            title: isPlaying ? 'Pause' : 'Play',
            icon: isPlaying ? '/images/pause-icon.png' : '/images/play-icon.png'
          },
          {
            action: 'previous',
            title: 'Previous',
            icon: '/images/previous-icon.png'
          },
          {
            action: 'next',
            title: 'Next',
            icon: '/images/next-icon.png'
          }
        ]
      })
    }
  }

  // Clear persistent notification
  const clearPersistentNotification = async () => {
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.ready
      const notifications = await registration.getNotifications({ tag: 'tunami-persistent' })
      
      notifications.forEach(notification => notification.close())
    }
  }

  return {
    notificationState,
    requestPermission,
    showPersistentNotification,
    clearPersistentNotification
  }
} 