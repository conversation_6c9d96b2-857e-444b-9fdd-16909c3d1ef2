'use client'

import React, { createContext, useContext, useReducer, useCallback } from 'react'
import { EnhancedPlaylistService, PlaylistPermissions, PlaylistShareData, BulkTrackOperation } from '@/lib/enhanced-playlist-service'
import { 
  Playlist, 
  PlaylistWithTracks, 
  CreatePlaylistData, 
  UpdatePlaylistData, 
  AddTrackToPlaylistData,
  ReorderTrackData,
  PlaylistFilters,
  PlaylistStats
} from '@/types/playlist'

interface EnhancedPlaylistState {
  playlists: Playlist[]
  currentPlaylist: PlaylistWithTracks | null
  currentPermissions: PlaylistPermissions | null
  loading: boolean
  error: string | null
  bulkOperationProgress: {
    isRunning: boolean
    completed: number
    total: number
    operation: string
  } | null
}

type EnhancedPlaylistAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PLAYLISTS'; payload: Playlist[] }
  | { type: 'SET_CURRENT_PLAYLIST'; payload: PlaylistWithTracks | null }
  | { type: 'SET_CURRENT_PERMISSIONS'; payload: PlaylistPermissions | null }
  | { type: 'ADD_PLAYLIST'; payload: Playlist }
  | { type: 'UPDATE_PLAYLIST'; payload: Playlist }
  | { type: 'DELETE_PLAYLIST'; payload: string }
  | { type: 'SET_BULK_PROGRESS'; payload: EnhancedPlaylistState['bulkOperationProgress'] }
  | { type: 'CLEAR_ERROR' }

const initialState: EnhancedPlaylistState = {
  playlists: [],
  currentPlaylist: null,
  currentPermissions: null,
  loading: false,
  error: null,
  bulkOperationProgress: null
}

function enhancedPlaylistReducer(state: EnhancedPlaylistState, action: EnhancedPlaylistAction): EnhancedPlaylistState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false }
    
    case 'SET_PLAYLISTS':
      return { ...state, playlists: action.payload, loading: false }
    
    case 'SET_CURRENT_PLAYLIST':
      return { ...state, currentPlaylist: action.payload, loading: false }
    
    case 'SET_CURRENT_PERMISSIONS':
      return { ...state, currentPermissions: action.payload }
    
    case 'ADD_PLAYLIST':
      return { 
        ...state, 
        playlists: [action.payload, ...state.playlists],
        loading: false 
      }
    
    case 'UPDATE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.map(playlist =>
          playlist.id === action.payload.id ? action.payload : playlist
        ),
        currentPlaylist: state.currentPlaylist?.id === action.payload.id
          ? { ...state.currentPlaylist, ...action.payload }
          : state.currentPlaylist,
        loading: false
      }
    
    case 'DELETE_PLAYLIST':
      return {
        ...state,
        playlists: state.playlists.filter(playlist => playlist.id !== action.payload),
        currentPlaylist: state.currentPlaylist?.id === action.payload ? null : state.currentPlaylist,
        currentPermissions: state.currentPlaylist?.id === action.payload ? null : state.currentPermissions,
        loading: false
      }
    
    case 'SET_BULK_PROGRESS':
      return { ...state, bulkOperationProgress: action.payload }
    
    case 'CLEAR_ERROR':
      return { ...state, error: null }
    
    default:
      return state
  }
}

export interface EnhancedPlaylistContextType {
  // State
  playlists: Playlist[]
  currentPlaylist: PlaylistWithTracks | null
  currentPermissions: PlaylistPermissions | null
  loading: boolean
  error: string | null
  bulkOperationProgress: EnhancedPlaylistState['bulkOperationProgress']
  
  // Basic CRUD operations
  createPlaylist: (data: CreatePlaylistData) => Promise<Playlist>
  updatePlaylist: (id: string, data: UpdatePlaylistData) => Promise<Playlist>
  deletePlaylist: (id: string) => Promise<void>
  getPlaylist: (id: string) => Promise<PlaylistWithTracks>
  getUserPlaylists: (userId?: string) => Promise<Playlist[]>
  getPublicPlaylists: (filters?: PlaylistFilters) => Promise<Playlist[]>
  
  // Track operations
  addTrackToPlaylist: (data: AddTrackToPlaylistData) => Promise<void>
  addTracksToPlaylist: (playlistId: string, trackIds: string[], startPosition?: number) => Promise<BulkTrackOperation[]>
  removeTrackFromPlaylist: (playlistId: string, trackId: string) => Promise<void>
  removeTracksFromPlaylist: (playlistId: string, trackIds: string[]) => Promise<number>
  reorderPlaylistTracks: (playlistId: string, tracks: ReorderTrackData[]) => Promise<void>
  
  // Advanced operations
  duplicatePlaylist: (sourceId: string, newName: string, newDescription?: string, isPublic?: boolean) => Promise<string>
  getPlaylistPermissions: (playlistId: string) => Promise<PlaylistPermissions>
  generateShareUrl: (playlistId: string) => Promise<PlaylistShareData>
  getPlaylistsWithTrack: (trackId: string) => Promise<Playlist[]>
  isTrackInPlaylist: (playlistId: string, trackId: string) => Promise<boolean>
  getPlaylistStats: (playlistId: string) => Promise<PlaylistStats>
  
  // Batch operations
  batchUpdatePlaylistTracks: (
    playlistId: string,
    operations: Array<{
      type: 'add' | 'remove' | 'reorder'
      trackId?: string
      trackIds?: string[]
      position?: number
      newPosition?: number
    }>
  ) => Promise<void>
  
  // Utility functions
  validatePlaylistData: (data: CreatePlaylistData | UpdatePlaylistData) => string[]
  refreshPlaylists: () => Promise<void>
  refreshCurrentPlaylist: () => Promise<void>
  clearError: () => void
}

const EnhancedPlaylistContext = createContext<EnhancedPlaylistContextType | undefined>(undefined)

export function EnhancedPlaylistProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(enhancedPlaylistReducer, initialState)

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading })
  }, [])

  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error })
  }, [])

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' })
  }, [])

  const setBulkProgress = useCallback((progress: EnhancedPlaylistState['bulkOperationProgress']) => {
    dispatch({ type: 'SET_BULK_PROGRESS', payload: progress })
  }, [])

  // Create playlist with validation
  const createPlaylist = useCallback(async (data: CreatePlaylistData): Promise<Playlist> => {
    try {
      setLoading(true)
      clearError()
      
      // Validate data first
      const validationErrors = EnhancedPlaylistService.validatePlaylistData(data)
      if (validationErrors.length > 0) {
        throw new Error(validationErrors.join(', '))
      }
      
      const playlist = await EnhancedPlaylistService.createPlaylist(data)
      dispatch({ type: 'ADD_PLAYLIST', payload: playlist })
      
      return playlist
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Update playlist with validation
  const updatePlaylist = useCallback(async (id: string, data: UpdatePlaylistData): Promise<Playlist> => {
    try {
      setLoading(true)
      clearError()
      
      // Validate data first
      const validationErrors = EnhancedPlaylistService.validatePlaylistData(data)
      if (validationErrors.length > 0) {
        throw new Error(validationErrors.join(', '))
      }
      
      const playlist = await EnhancedPlaylistService.updatePlaylist(id, data)
      dispatch({ type: 'UPDATE_PLAYLIST', payload: playlist })
      
      return playlist
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Delete playlist
  const deletePlaylist = useCallback(async (id: string): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      
      await EnhancedPlaylistService.deletePlaylist(id)
      dispatch({ type: 'DELETE_PLAYLIST', payload: id })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Get playlist with permissions
  const getPlaylist = useCallback(async (id: string): Promise<PlaylistWithTracks> => {
    try {
      setLoading(true)
      clearError()
      
      const [playlist, permissions] = await Promise.all([
        EnhancedPlaylistService.getPlaylist(id),
        EnhancedPlaylistService.getPlaylistPermissions(id)
      ])
      
      dispatch({ type: 'SET_CURRENT_PLAYLIST', payload: playlist })
      dispatch({ type: 'SET_CURRENT_PERMISSIONS', payload: permissions })
      
      return playlist
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Get user playlists
  const getUserPlaylists = useCallback(async (userId?: string): Promise<Playlist[]> => {
    try {
      setLoading(true)
      clearError()
      
      const playlists = await EnhancedPlaylistService.getUserPlaylists(userId)
      dispatch({ type: 'SET_PLAYLISTS', payload: playlists })
      
      return playlists
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch playlists'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Get public playlists
  const getPublicPlaylists = useCallback(async (filters?: PlaylistFilters): Promise<Playlist[]> => {
    try {
      setLoading(true)
      clearError()
      
      const playlists = await EnhancedPlaylistService.getPublicPlaylists(filters)
      dispatch({ type: 'SET_PLAYLISTS', payload: playlists })
      
      return playlists
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch public playlists'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Add single track to playlist
  const addTrackToPlaylist = useCallback(async (data: AddTrackToPlaylistData): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      
      await EnhancedPlaylistService.addTrackToPlaylist(data)
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === data.playlist_id) {
        await getPlaylist(data.playlist_id)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add track to playlist'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Add multiple tracks to playlist
  const addTracksToPlaylist = useCallback(async (
    playlistId: string, 
    trackIds: string[], 
    startPosition?: number
  ): Promise<BulkTrackOperation[]> => {
    try {
      setLoading(true)
      clearError()
      setBulkProgress({
        isRunning: true,
        completed: 0,
        total: trackIds.length,
        operation: 'Adding tracks'
      })
      
      const result = await EnhancedPlaylistService.addTracksToPlaylist(playlistId, trackIds, startPosition)
      
      setBulkProgress({
        isRunning: false,
        completed: trackIds.length,
        total: trackIds.length,
        operation: 'Adding tracks'
      })
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === playlistId) {
        await getPlaylist(playlistId)
      }
      
      // Clear progress after a delay
      setTimeout(() => setBulkProgress(null), 2000)
      
      return result
    } catch (error) {
      setBulkProgress(null)
      const errorMessage = error instanceof Error ? error.message : 'Failed to add tracks to playlist'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Remove single track from playlist
  const removeTrackFromPlaylist = useCallback(async (playlistId: string, trackId: string): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      
      await EnhancedPlaylistService.removeTrackFromPlaylist(playlistId, trackId)
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === playlistId) {
        await getPlaylist(playlistId)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove track from playlist'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Remove multiple tracks from playlist
  const removeTracksFromPlaylist = useCallback(async (playlistId: string, trackIds: string[]): Promise<number> => {
    try {
      setLoading(true)
      clearError()
      setBulkProgress({
        isRunning: true,
        completed: 0,
        total: trackIds.length,
        operation: 'Removing tracks'
      })
      
      const removedCount = await EnhancedPlaylistService.removeTracksFromPlaylist(playlistId, trackIds)
      
      setBulkProgress({
        isRunning: false,
        completed: removedCount,
        total: trackIds.length,
        operation: 'Removing tracks'
      })
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === playlistId) {
        await getPlaylist(playlistId)
      }
      
      // Clear progress after a delay
      setTimeout(() => setBulkProgress(null), 2000)
      
      return removedCount
    } catch (error) {
      setBulkProgress(null)
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove tracks from playlist'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Reorder playlist tracks
  const reorderPlaylistTracks = useCallback(async (playlistId: string, tracks: ReorderTrackData[]): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      
      await EnhancedPlaylistService.reorderPlaylistTracks(playlistId, tracks)
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === playlistId) {
        await getPlaylist(playlistId)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reorder playlist tracks'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Duplicate playlist
  const duplicatePlaylist = useCallback(async (
    sourceId: string, 
    newName: string, 
    newDescription?: string, 
    isPublic: boolean = false
  ): Promise<string> => {
    try {
      setLoading(true)
      clearError()
      
      const newPlaylistId = await EnhancedPlaylistService.duplicatePlaylist(sourceId, newName, newDescription, isPublic)
      
      // Refresh playlists to include the new one
      await getUserPlaylists()
      
      return newPlaylistId
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to duplicate playlist'
      setError(errorMessage)
      throw error
    }
  }, [getUserPlaylists])

  // Get playlist permissions
  const getPlaylistPermissions = useCallback(async (playlistId: string): Promise<PlaylistPermissions> => {
    try {
      const permissions = await EnhancedPlaylistService.getPlaylistPermissions(playlistId)
      
      if (state.currentPlaylist?.id === playlistId) {
        dispatch({ type: 'SET_CURRENT_PERMISSIONS', payload: permissions })
      }
      
      return permissions
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get playlist permissions'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id])

  // Generate share URL
  const generateShareUrl = useCallback(async (playlistId: string): Promise<PlaylistShareData> => {
    try {
      return await EnhancedPlaylistService.generateShareUrl(playlistId)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate share URL'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Get playlists containing a track
  const getPlaylistsWithTrack = useCallback(async (trackId: string): Promise<Playlist[]> => {
    try {
      return await EnhancedPlaylistService.getPlaylistsWithTrack(trackId)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get playlists with track'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Check if track is in playlist
  const isTrackInPlaylist = useCallback(async (playlistId: string, trackId: string): Promise<boolean> => {
    try {
      return await EnhancedPlaylistService.isTrackInPlaylist(playlistId, trackId)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to check track in playlist'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Get playlist statistics
  const getPlaylistStats = useCallback(async (playlistId: string): Promise<PlaylistStats> => {
    try {
      return await EnhancedPlaylistService.getPlaylistStats(playlistId)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get playlist stats'
      setError(errorMessage)
      throw error
    }
  }, [])

  // Batch update playlist tracks
  const batchUpdatePlaylistTracks = useCallback(async (
    playlistId: string,
    operations: Array<{
      type: 'add' | 'remove' | 'reorder'
      trackId?: string
      trackIds?: string[]
      position?: number
      newPosition?: number
    }>
  ): Promise<void> => {
    try {
      setLoading(true)
      clearError()
      setBulkProgress({
        isRunning: true,
        completed: 0,
        total: operations.length,
        operation: 'Batch updating tracks'
      })
      
      await EnhancedPlaylistService.batchUpdatePlaylistTracks(playlistId, operations)
      
      setBulkProgress({
        isRunning: false,
        completed: operations.length,
        total: operations.length,
        operation: 'Batch updating tracks'
      })
      
      // Refresh current playlist if it's the one being modified
      if (state.currentPlaylist?.id === playlistId) {
        await getPlaylist(playlistId)
      }
      
      // Clear progress after a delay
      setTimeout(() => setBulkProgress(null), 2000)
    } catch (error) {
      setBulkProgress(null)
      const errorMessage = error instanceof Error ? error.message : 'Failed to batch update tracks'
      setError(errorMessage)
      throw error
    }
  }, [state.currentPlaylist?.id, getPlaylist])

  // Validate playlist data
  const validatePlaylistData = useCallback((data: CreatePlaylistData | UpdatePlaylistData): string[] => {
    return EnhancedPlaylistService.validatePlaylistData(data)
  }, [])

  // Refresh playlists
  const refreshPlaylists = useCallback(async (): Promise<void> => {
    try {
      await getUserPlaylists()
    } catch (error) {
      // Error is already handled in getUserPlaylists
    }
  }, [getUserPlaylists])

  // Refresh current playlist
  const refreshCurrentPlaylist = useCallback(async (): Promise<void> => {
    if (state.currentPlaylist) {
      try {
        await getPlaylist(state.currentPlaylist.id)
      } catch (error) {
        // Error is already handled in getPlaylist
      }
    }
  }, [state.currentPlaylist, getPlaylist])

  const contextValue: EnhancedPlaylistContextType = {
    // State
    playlists: state.playlists,
    currentPlaylist: state.currentPlaylist,
    currentPermissions: state.currentPermissions,
    loading: state.loading,
    error: state.error,
    bulkOperationProgress: state.bulkOperationProgress,
    
    // Basic CRUD operations
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    getPlaylist,
    getUserPlaylists,
    getPublicPlaylists,
    
    // Track operations
    addTrackToPlaylist,
    addTracksToPlaylist,
    removeTrackFromPlaylist,
    removeTracksFromPlaylist,
    reorderPlaylistTracks,
    
    // Advanced operations
    duplicatePlaylist,
    getPlaylistPermissions,
    generateShareUrl,
    getPlaylistsWithTrack,
    isTrackInPlaylist,
    getPlaylistStats,
    
    // Batch operations
    batchUpdatePlaylistTracks,
    
    // Utility functions
    validatePlaylistData,
    refreshPlaylists,
    refreshCurrentPlaylist,
    clearError
  }

  return (
    <EnhancedPlaylistContext.Provider value={contextValue}>
      {children}
    </EnhancedPlaylistContext.Provider>
  )
}

export function useEnhancedPlaylist() {
  const context = useContext(EnhancedPlaylistContext)
  if (context === undefined) {
    throw new Error('useEnhancedPlaylist must be used within an EnhancedPlaylistProvider')
  }
  return context
} 