'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Music, Heart, Share2, Clock, Eye, Plus } from 'lucide-react'
import { AudioTrack } from '@/types/audio'
import PlayButton from '@/components/audio/PlayButton'
import { useAudio } from '@/contexts/AudioContext'
import AddToPlaylistModal from '@/components/playlists/AddToPlaylistModal'

export interface TrackCardProps {
  track: AudioTrack
  showArtwork?: boolean
  showStats?: boolean
  showActions?: boolean
  size?: 'sm' | 'md' | 'lg'
  layout?: 'horizontal' | 'vertical'
  className?: string
  onClick?: () => void
}

// Mock track stats (in real app, these would come from database)
const getTrackStats = (trackId: string) => ({
  plays: Math.floor(Math.random() * 10000) + 100,
  likes: Math.floor(Math.random() * 1000) + 10,
  duration: Math.floor(Math.random() * 180) + 60, // 1-4 minutes
  createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Last 30 days
})

export default function TrackCard({
  track,
  showArtwork = true,
  showStats = true,
  showActions = true,
  size = 'md',
  layout = 'vertical',
  className = '',
  onClick
}: TrackCardProps) {
  const [isLiked, setIsLiked] = useState(false)
  const [showAddToPlaylist, setShowAddToPlaylist] = useState(false)
  const { currentTrack, isPlaying } = useAudio()
  
  const stats = getTrackStats(track.id)
  const isCurrentTrack = currentTrack?.id === track.id
  const isTrackPlaying = isCurrentTrack && isPlaying

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatPlays = (plays: number) => {
    if (plays >= 1000000) return `${(plays / 1000000).toFixed(1)}M`
    if (plays >= 1000) return `${(plays / 1000).toFixed(1)}K`
    return plays.toString()
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
    return date.toLocaleDateString()
  }

  const handleCardClick = () => {
    onClick?.()
  }

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsLiked(!isLiked)
  }

  const handleShare = async (e: React.MouseEvent) => {
    e.stopPropagation()
    
    const shareData = {
      title: track.title,
      text: `Check out "${track.title}" by ${track.artist}`,
      url: `${window.location.origin}/track/${track.id}`
    }

    if (navigator.share) {
      try {
        await navigator.share(shareData)
      } catch (err) {
        // Fallback to clipboard
        copyToClipboard(shareData.url)
      }
    } else {
      copyToClipboard(shareData.url)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // You could show a toast notification here
      console.log('Link copied to clipboard')
    }).catch(() => {
      console.log('Failed to copy link')
    })
  }

  const handleAddToPlaylist = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowAddToPlaylist(true)
  }

  const sizeClasses = {
    sm: {
      card: 'p-3',
      artwork: 'w-12 h-12',
      title: 'text-sm',
      subtitle: 'text-xs',
      playButton: 'sm' as const
    },
    md: {
      card: 'p-4',
      artwork: 'w-16 h-16',
      title: 'text-base',
      subtitle: 'text-sm',
      playButton: 'md' as const
    },
    lg: {
      card: 'p-6',
      artwork: 'w-24 h-24',
      title: 'text-lg',
      subtitle: 'text-base',
      playButton: 'lg' as const
    }
  }

  const currentSize = sizeClasses[size]

  if (layout === 'horizontal') {
    return (
      <div 
        className={`
          group bg-gray-800 hover:bg-gray-750 rounded-lg border border-gray-700 hover:border-gray-600 transition-all duration-200 cursor-pointer
          ${isCurrentTrack ? 'ring-2 ring-purple-500 bg-purple-900/20' : ''}
          ${currentSize.card} ${className}
        `}
        onClick={handleCardClick}
      >
        <div className="flex items-center space-x-4">
          {/* Artwork */}
          {showArtwork && (
            <div className={`${currentSize.artwork} bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0 relative`}>
              <Music className={`${size === 'sm' ? 'w-5 h-5' : size === 'lg' ? 'w-8 h-8' : 'w-6 h-6'} text-white`} />
              {isTrackPlaying && (
                <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                </div>
              )}
            </div>
          )}

          {/* Track Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="min-w-0 flex-1">
                <h3 className={`${currentSize.title} font-semibold text-white truncate group-hover:text-purple-300 transition-colors`}>
                  {track.title}
                </h3>
                <p className={`${currentSize.subtitle} text-gray-400 truncate`}>
                  {track.artist}
                </p>
                {track.aiTool && (
                  <p className={`${size === 'sm' ? 'text-xs' : 'text-sm'} text-purple-400`}>
                    {track.aiTool}
                  </p>
                )}
              </div>

              {/* Stats */}
              {showStats && (
                <div className={`${size === 'sm' ? 'text-xs' : 'text-sm'} text-gray-500 text-right flex-shrink-0 ml-2`}>
                  <div className="flex items-center gap-1 mb-1">
                    <Eye className="w-3 h-3" />
                    {formatPlays(stats.plays)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {formatDuration(stats.duration)}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <PlayButton 
              track={track}
              size="lg"
              variant="secondary"
              showQueue={false}
            />

            {showActions && (
              <div className="flex items-center space-x-1">
                <button
                  onClick={handleAddToPlaylist}
                  className="p-2 rounded-full text-gray-400 hover:text-white transition-colors"
                  title="Add to playlist"
                >
                  <Plus className="w-4 h-4" />
                </button>

                <button
                  onClick={handleLike}
                  className={`p-2 rounded-full transition-colors ${
                    isLiked 
                      ? 'text-red-400 hover:text-red-300' 
                      : 'text-gray-400 hover:text-white'
                  }`}
                  title={isLiked ? 'Unlike' : 'Like'}
                >
                  <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
                </button>

                <button
                  onClick={handleShare}
                  className="p-2 rounded-full text-gray-400 hover:text-white transition-colors"
                  title="Share track"
                >
                  <Share2 className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Vertical Layout
  return (
    <div 
      className={`
        group bg-gray-800 hover:bg-gray-750 rounded-lg border border-gray-700 hover:border-gray-600 transition-all duration-200 cursor-pointer
        ${isCurrentTrack ? 'ring-2 ring-purple-500 bg-purple-900/20' : ''}
        ${currentSize.card} ${className}
      `}
      onClick={handleCardClick}
    >
      {/* Artwork */}
      {showArtwork && (
        <div className="relative mb-4">
          <div className={`w-full aspect-square bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center relative overflow-hidden`}>
            <Music className={`${size === 'sm' ? 'w-8 h-8' : size === 'lg' ? 'w-12 h-12' : 'w-10 h-10'} text-white`} />
            {isTrackPlaying && (
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              </div>
            )}
          </div>

          {/* Overlay Controls */}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
            <PlayButton 
              track={track}
              size="lg"
              variant="primary"
              showQueue={false}
            />
          </div>
        </div>
      )}

      {/* Track Info */}
      <div className="space-y-2">
        <div>
          <h3 className={`${currentSize.title} font-semibold text-white truncate group-hover:text-purple-300 transition-colors`}>
            {track.title}
          </h3>
          <p className={`${currentSize.subtitle} text-gray-400 truncate`}>
            {track.artist}
          </p>
          {track.aiTool && (
            <p className={`${size === 'sm' ? 'text-xs' : 'text-sm'} text-purple-400`}>
              {track.aiTool}
            </p>
          )}
        </div>

        {/* Stats Row */}
        {showStats && (
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {formatPlays(stats.plays)}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatDuration(stats.duration)}
              </div>
            </div>
            <span>{formatDate(stats.createdAt)}</span>
          </div>
        )}

        {/* Actions Row */}
        {showActions && (
          <div className="flex items-center justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="flex items-center space-x-1">
              <button
                onClick={handleAddToPlaylist}
                className="p-1.5 rounded-full text-gray-400 hover:text-white transition-colors"
                title="Add to playlist"
              >
                <Plus className="w-4 h-4" />
              </button>

              <button
                onClick={handleLike}
                className={`p-1.5 rounded-full transition-colors ${
                  isLiked 
                    ? 'text-red-400 hover:text-red-300' 
                    : 'text-gray-400 hover:text-white'
                }`}
                title={isLiked ? 'Unlike' : 'Like'}
              >
                <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
              </button>

              <button
                onClick={handleShare}
                className="p-1.5 rounded-full text-gray-400 hover:text-white transition-colors"
                title="Share track"
              >
                <Share2 className="w-4 h-4" />
              </button>
            </div>

            <PlayButton 
              track={track}
              size="md"
              variant="minimal"
              showQueue={false}
            />
          </div>
        )}
      </div>

      {/* Add to Playlist Modal */}
      <AddToPlaylistModal
        isOpen={showAddToPlaylist}
        onClose={() => setShowAddToPlaylist(false)}
        track={track}
      />
    </div>
  )
} 