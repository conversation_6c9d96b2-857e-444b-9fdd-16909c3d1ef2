# Tunami MCP Servers Environment Configuration
# Copy this file to .env and fill in your actual values

# GitHub Integration (for github-server.js)
# Get your token from: https://github.com/settings/tokens
GITHUB_TOKEN=*********************************************************************************************

# Supabase Database Configuration (for postgres-server.js)
# Get these from your Supabase project settings
SUPABASE_DB_HOST=your_supabase_host.supabase.co
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=your_username
SUPABASE_DB_PASSWORD=your_password
SUPABASE_DB_SSL=true

# Optional: Custom paths for filesystem server
# TUNAMI_SRC_PATH=../src
# TUNAMI_PUBLIC_PATH=../public
# TUNAMI_UPLOADS_PATH=../uploads
# TUNAMI_LOGS_PATH=../logs

# Optional: Memory storage location
# TUNAMI_MEMORY_FILE=../tunami-memory.json 