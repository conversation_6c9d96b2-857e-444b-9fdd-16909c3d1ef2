-- Migration 003: Indexes, Triggers, and Functions
-- Date: 2025-01-24
-- Description: Create performance indexes, triggers, and utility functions

-- =============================================
-- 1. PERFORMANCE INDEXES
-- =============================================

-- Profiles indexes
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_profiles_email ON profiles(email);

-- Tracks indexes
CREATE INDEX idx_tracks_uploaded_by ON tracks(uploaded_by);
CREATE INDEX idx_tracks_ai_tool ON tracks(ai_tool);
CREATE INDEX idx_tracks_genre ON tracks(genre);
CREATE INDEX idx_tracks_mood ON tracks(mood);
CREATE INDEX idx_tracks_created_at ON tracks(created_at DESC);
CREATE INDEX idx_tracks_is_public ON tracks(is_public);
CREATE INDEX idx_tracks_is_featured ON tracks(is_featured);
CREATE INDEX idx_tracks_play_count ON tracks(play_count DESC);

-- Playlists indexes
CREATE INDEX idx_playlists_user_id ON playlists(user_id);
CREATE INDEX idx_playlists_is_public ON playlists(is_public);
CREATE INDEX idx_playlists_created_at ON playlists(created_at DESC);

-- Playlist tracks indexes
CREATE INDEX idx_playlist_tracks_playlist_id ON playlist_tracks(playlist_id);
CREATE INDEX idx_playlist_tracks_track_id ON playlist_tracks(track_id);
CREATE INDEX idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);

-- User agreements indexes
CREATE INDEX idx_user_agreements_user_id ON user_agreements(user_id);
CREATE INDEX idx_user_agreements_type_version ON user_agreements(agreement_type, agreement_version);

-- =============================================
-- 2. UTILITY FUNCTIONS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- Function to update playlist stats
CREATE OR REPLACE FUNCTION update_playlist_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE playlists 
        SET 
            track_count = track_count + 1,
            total_duration = total_duration + COALESCE((SELECT duration FROM tracks WHERE id = NEW.track_id), 0)
        WHERE id = NEW.playlist_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE playlists 
        SET 
            track_count = track_count - 1,
            total_duration = total_duration - COALESCE((SELECT duration FROM tracks WHERE id = OLD.track_id), 0)
        WHERE id = OLD.playlist_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE 'plpgsql';

-- Function to handle user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name'
    );
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql' SECURITY DEFINER;

-- =============================================
-- 3. TRIGGERS
-- =============================================

-- Updated_at triggers
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tracks_updated_at
    BEFORE UPDATE ON tracks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_playlists_updated_at
    BEFORE UPDATE ON playlists
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Playlist stats triggers
CREATE TRIGGER update_playlist_stats_on_insert
    AFTER INSERT ON playlist_tracks
    FOR EACH ROW EXECUTE FUNCTION update_playlist_stats();

CREATE TRIGGER update_playlist_stats_on_delete
    AFTER DELETE ON playlist_tracks
    FOR EACH ROW EXECUTE FUNCTION update_playlist_stats();

-- Automatic profile creation trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- =============================================
-- 4. UTILITY VIEWS
-- =============================================

-- View for popular tracks
CREATE VIEW popular_tracks AS
SELECT 
    t.*,
    p.username as uploader_username,
    p.full_name as uploader_name
FROM tracks t
JOIN profiles p ON t.uploaded_by = p.id
WHERE t.is_public = true
ORDER BY t.play_count DESC, t.like_count DESC;

-- View for user's complete playlists
CREATE VIEW user_playlists_with_stats AS
SELECT 
    pl.*,
    p.username,
    p.full_name,
    COUNT(pt.track_id) as actual_track_count
FROM playlists pl
JOIN profiles p ON pl.user_id = p.id
LEFT JOIN playlist_tracks pt ON pl.id = pt.playlist_id
GROUP BY pl.id, p.username, p.full_name; 