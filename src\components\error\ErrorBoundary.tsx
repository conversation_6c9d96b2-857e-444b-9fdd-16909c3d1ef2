'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Alert<PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react'
import { TunamiError, ErrorType, ErrorSeverity, logError } from '@/lib/error-handler'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
  level?: 'page' | 'component' | 'critical'
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
}

export class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0
  private maxRetries = 3

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo })

    // Create TunamiError for consistent error handling
    const tunamiError = new TunamiError(
      error.message,
      ErrorType.UNKNOWN,
      this.props.level === 'critical' ? ErrorSeverity.CRITICAL : ErrorSeverity.HIGH,
      {
        code: 'REACT_ERROR_BOUNDARY',
        context: {
          componentStack: errorInfo.componentStack,
          errorBoundaryLevel: this.props.level || 'component',
          retryCount: this.retryCount,
          errorId: this.state.errorId
        }
      }
    )

    // Log the error
    logError(tunamiError)

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)

    // Report to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo)
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // In production, send to error tracking service
    // Example: Sentry, LogRocket, etc.
    console.error('Error reported to tracking service:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString()
    })
  }

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null
      })
    }
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Render appropriate error UI based on level
      return this.renderErrorUI()
    }

    return this.props.children
  }

  private renderErrorUI() {
    const { level = 'component', showDetails = false } = this.props
    const { error, errorInfo, errorId } = this.state

    if (level === 'critical') {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-900 via-gray-900 to-black flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-gray-800 rounded-lg border border-red-500 p-6 text-center">
            <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h1 className="text-xl font-bold text-white mb-2">Critical Error</h1>
            <p className="text-gray-300 mb-6">
              A critical error occurred that prevented the application from working properly.
            </p>
            
            <div className="space-y-3">
              <button
                onClick={this.handleReload}
                className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Reload Application
              </button>
              
              <button
                onClick={this.handleGoHome}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <Home className="w-4 h-4" />
                Go to Homepage
              </button>
            </div>

            {showDetails && error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-gray-400 text-sm">
                  Technical Details
                </summary>
                <div className="mt-2 p-3 bg-gray-900 rounded text-xs text-gray-300 font-mono">
                  <p><strong>Error ID:</strong> {errorId}</p>
                  <p><strong>Message:</strong> {error.message}</p>
                  {error.stack && (
                    <div className="mt-2">
                      <strong>Stack:</strong>
                      <pre className="whitespace-pre-wrap text-xs">{error.stack}</pre>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      )
    }

    if (level === 'page') {
      return (
        <div className="min-h-[60vh] flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
            <Bug className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
            <h2 className="text-lg font-bold text-white mb-2">Page Error</h2>
            <p className="text-gray-300 mb-6">
              Something went wrong while loading this page.
            </p>
            
            <div className="space-y-3">
              {this.retryCount < this.maxRetries && (
                <button
                  onClick={this.handleRetry}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Try Again ({this.maxRetries - this.retryCount} attempts left)
                </button>
              )}
              
              <button
                onClick={this.handleReload}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Reload Page
              </button>
              
              <button
                onClick={this.handleGoHome}
                className="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <Home className="w-4 h-4" />
                Go Home
              </button>
            </div>

            {showDetails && error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-gray-400 text-sm">
                  Error Details
                </summary>
                <div className="mt-2 p-3 bg-gray-900 rounded text-xs text-gray-300">
                  <p><strong>Error ID:</strong> {errorId}</p>
                  <p><strong>Message:</strong> {error.message}</p>
                </div>
              </details>
            )}
          </div>
        </div>
      )
    }

    // Component level error
    return (
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 text-center">
        <Bug className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
        <p className="text-gray-300 text-sm mb-3">
          This component encountered an error
        </p>
        
        <div className="space-y-2">
          {this.retryCount < this.maxRetries && (
            <button
              onClick={this.handleRetry}
              className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors flex items-center justify-center gap-1 mx-auto"
            >
              <RefreshCw className="w-3 h-3" />
              Retry ({this.maxRetries - this.retryCount} left)
            </button>
          )}
          
          {showDetails && error && (
            <details className="text-left">
              <summary className="cursor-pointer text-gray-400 text-xs">
                Details
              </summary>
              <div className="mt-1 p-2 bg-gray-900 rounded text-xs text-gray-300">
                <p><strong>ID:</strong> {errorId}</p>
                <p><strong>Error:</strong> {error.message}</p>
              </div>
            </details>
          )}
        </div>
      </div>
    )
  }
}

// Convenience wrapper components
export function PageErrorBoundary({ children, ...props }: Omit<Props, 'level'>) {
  return (
    <ErrorBoundary level="page" {...props}>
      {children}
    </ErrorBoundary>
  )
}

export function ComponentErrorBoundary({ children, ...props }: Omit<Props, 'level'>) {
  return (
    <ErrorBoundary level="component" {...props}>
      {children}
    </ErrorBoundary>
  )
}

export function CriticalErrorBoundary({ children, ...props }: Omit<Props, 'level'>) {
  return (
    <ErrorBoundary level="critical" showDetails={true} {...props}>
      {children}
    </ErrorBoundary>
  )
}

export default ErrorBoundary 