/* Mobile-First CSS Styles for Tunami */

/* Touch-friendly button sizes (minimum 44px) */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Touch manipulation for better touch response */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Mobile-optimized scrolling */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Prevent zoom on input focus (iOS) */
.no-zoom {
  font-size: 16px !important;
  transform-origin: left top;
  transform: scale(1);
}

/* Mobile-specific animations */
@media (prefers-reduced-motion: no-preference) {
  .mobile-fade-in {
    animation: mobileSlideUp 0.3s ease-out;
  }
  
  .mobile-slide-up {
    animation: mobileSlideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  .mobile-bounce {
    animation: mobileBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
}

@keyframes mobileSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes mobileBounce {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Mobile breakpoints */
@media (max-width: 480px) {
  /* Extra small devices */
  .mobile-xs-hidden {
    display: none !important;
  }
  
  .mobile-xs-full {
    width: 100% !important;
  }
  
  .mobile-xs-padding {
    padding: 0.75rem !important;
  }
  
  .mobile-xs-text {
    font-size: 0.875rem !important;
  }
  
  /* Compact layout for very small screens */
  .mobile-xs-compact {
    padding: 0.5rem !important;
    margin: 0.25rem !important;
  }
  
  /* Larger touch targets for small screens */
  .mobile-xs-touch {
    min-height: 48px !important;
    min-width: 48px !important;
  }
}

@media (max-width: 768px) {
  /* Small devices and tablets in portrait */
  .mobile-sm-hidden {
    display: none !important;
  }
  
  .mobile-sm-full {
    width: 100% !important;
  }
  
  .mobile-sm-stack {
    flex-direction: column !important;
  }
  
  .mobile-sm-center {
    text-align: center !important;
  }
  
  /* Mobile navigation adjustments */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: rgba(17, 24, 39, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(75, 85, 99, 0.3);
  }
  
  /* Mobile header adjustments */
  .mobile-header {
    position: sticky;
    top: 0;
    z-index: 40;
    background: rgba(17, 24, 39, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
  }
  
  /* Mobile content padding to account for fixed nav */
  .mobile-content {
    padding-bottom: 80px; /* Space for bottom navigation */
    padding-top: 60px; /* Space for top header */
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .landscape-compact {
    padding: 0.5rem !important;
  }
  
  .landscape-hidden {
    display: none !important;
  }
  
  /* Adjust audio player for landscape */
  .landscape-audio-player {
    flex-direction: row !important;
    align-items: center !important;
  }
  
  .landscape-audio-controls {
    flex-direction: row !important;
    gap: 1rem !important;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-border {
    border-width: 0.5px;
  }
  
  .retina-shadow {
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.1);
  }
}

/* Dark mode mobile optimizations */
@media (prefers-color-scheme: dark) {
  .mobile-dark-bg {
    background-color: #0f172a;
  }
  
  .mobile-dark-border {
    border-color: #334155;
  }
  
  .mobile-dark-text {
    color: #e2e8f0;
  }
}

/* Mobile-specific component styles */

/* Mobile Audio Player */
.mobile-audio-player {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.mobile-audio-progress {
  height: 12px;
  border-radius: 6px;
  background: rgba(75, 85, 99, 0.3);
  position: relative;
  overflow: hidden;
}

.mobile-audio-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6 0%, #3b82f6 100%);
  border-radius: 6px;
  transition: width 0.1s ease-out;
}

.mobile-audio-thumb {
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.2s ease;
}

.mobile-audio-thumb:active {
  transform: translateY(-50%) scale(1.2);
}

/* Mobile Button Styles */
.mobile-btn {
  min-height: 44px;
  min-width: 44px;
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.2s ease;
  touch-action: manipulation;
  user-select: none;
}

.mobile-btn:active {
  transform: scale(0.95);
}

.mobile-btn-primary {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.mobile-btn-primary:hover {
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
  transform: translateY(-1px);
}

.mobile-btn-secondary {
  background: rgba(75, 85, 99, 0.2);
  color: #e2e8f0;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.mobile-btn-secondary:hover {
  background: rgba(75, 85, 99, 0.3);
}

/* Mobile Card Styles */
.mobile-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  transition: all 0.3s ease;
}

.mobile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(139, 92, 246, 0.3);
}

.mobile-card:active {
  transform: translateY(0);
}

/* Mobile Input Styles */
.mobile-input {
  min-height: 44px;
  font-size: 16px; /* Prevent zoom on iOS */
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(75, 85, 99, 0.3);
  color: #e2e8f0;
  transition: all 0.2s ease;
}

.mobile-input:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  background: rgba(30, 41, 59, 0.8);
}

/* Mobile Navigation */
.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60px;
  min-width: 60px;
  color: #9ca3af;
  transition: all 0.2s ease;
  text-decoration: none;
  border-radius: 0.5rem;
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
  color: #8b5cf6;
  background: rgba(139, 92, 246, 0.1);
}

.mobile-nav-item span {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Mobile Search */
.mobile-search {
  position: relative;
  width: 100%;
}

.mobile-search-input {
  width: 100%;
  padding-left: 3rem;
  padding-right: 1rem;
}

.mobile-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

/* Mobile Loading States */
.mobile-skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: mobileShimmer 1.5s infinite;
  border-radius: 0.5rem;
}

@keyframes mobileShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Mobile Accessibility */
.mobile-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.mobile-focus-visible:focus-visible {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
}

/* Mobile Safe Areas (for devices with notches) */
@supports (padding: max(0px)) {
  .mobile-safe-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .mobile-safe-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  .mobile-safe-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .mobile-safe-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Mobile Performance Optimizations */
.mobile-gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.mobile-smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Mobile Gesture Indicators */
.mobile-swipeable {
  position: relative;
}

.mobile-swipeable::after {
  content: '';
  position: absolute;
  bottom: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 2rem;
  height: 0.25rem;
  background: rgba(156, 163, 175, 0.5);
  border-radius: 0.125rem;
}

/* Mobile Pull-to-Refresh */
.mobile-pull-refresh {
  position: relative;
  overflow: hidden;
}

.mobile-pull-indicator {
  position: absolute;
  top: -3rem;
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.3s ease;
  color: #8b5cf6;
}

.mobile-pull-indicator.active {
  top: 1rem;
}

/* Mobile Haptic Feedback Classes */
.mobile-haptic-light {
  /* Used with JavaScript for light haptic feedback */
}

.mobile-haptic-medium {
  /* Used with JavaScript for medium haptic feedback */
}

.mobile-haptic-heavy {
  /* Used with JavaScript for heavy haptic feedback */
} 