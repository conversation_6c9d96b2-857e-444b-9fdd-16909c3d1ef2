'use client'

import { useRef, useCallback } from 'react'

interface SwipeGesturesConfig {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  threshold?: number
  enabled?: boolean
}

interface TouchPosition {
  x: number
  y: number
}

export function useSwipeGestures({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50,
  enabled = true
}: SwipeGesturesConfig) {
  const touchStartRef = useRef<TouchPosition | null>(null)
  const touchEndRef = useRef<TouchPosition | null>(null)

  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (!enabled) return

    const touch = event.touches[0]
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY
    }
  }, [enabled])

  const handleTouchMove = useCallback((event: React.TouchEvent) => {
    if (!enabled || !touchStartRef.current) return

    const touch = event.touches[0]
    touchEndRef.current = {
      x: touch.clientX,
      y: touch.clientY
    }
  }, [enabled])

  const handleTouchEnd = useCallback((event: React.TouchEvent) => {
    if (!enabled || !touchStartRef.current || !touchEndRef.current) return

    const deltaX = touchEndRef.current.x - touchStartRef.current.x
    const deltaY = touchEndRef.current.y - touchStartRef.current.y

    const absDeltaX = Math.abs(deltaX)
    const absDeltaY = Math.abs(deltaY)

    // Check if the swipe distance meets threshold
    if (Math.max(absDeltaX, absDeltaY) < threshold) return

    // Determine primary swipe direction
    if (absDeltaX > absDeltaY) {
      // Horizontal swipe
      if (deltaX > 0) {
        onSwipeRight?.()
      } else {
        onSwipeLeft?.()
      }
    } else {
      // Vertical swipe
      if (deltaY > 0) {
        onSwipeDown?.()
      } else {
        onSwipeUp?.()
      }
    }

    // Reset touch positions
    touchStartRef.current = null
    touchEndRef.current = null
  }, [enabled, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown])

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  }
} 