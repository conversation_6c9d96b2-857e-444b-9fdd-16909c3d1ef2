'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Play, Pause, Heart, Share2, Download, Clock, User, Sparkles, Tag, Calendar, Eye, HeadphonesIcon } from 'lucide-react'
import MainLayout from '@/components/layout/MainLayout'
import { supabase } from '@/lib/supabase'
import type { Track } from '@/types/database'
import { useAudio } from '@/contexts/AudioContext'
import { AudioTrack } from '@/types/audio'
import PlayButton from '@/components/audio/PlayButton'
import { convertTrackToAudioTrack, formatDuration, getAIToolDisplayName, formatGenre } from '@/lib/audio-utils'

interface TrackWithProfile extends Track {
  profiles?: {
    username: string | null
    full_name: string | null
    avatar_url: string | null
  }
}

export default function TrackPage() {
  const params = useParams()
  const router = useRouter()
  const trackId = params.id as string

  const [track, setTrack] = useState<TrackWithProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLiked, setIsLiked] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>(null)

  // Audio context integration
  const { currentTrack, isPlaying: globalIsPlaying, isClient } = useAudio()

  // Convert database Track to AudioTrack for global player
  const getAudioTrack = (): AudioTrack | null => {
    return track ? convertTrackToAudioTrack(track) : null
  }

  // Check if this track is currently playing in global player
  const isCurrentlyPlaying = isClient && currentTrack?.id === trackId && globalIsPlaying

  useEffect(() => {
    loadTrack()
    checkAuth()
  }, [trackId])

  const checkAuth = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setCurrentUser(user)
    
    if (user) {
      checkIfLiked(user.id)
    }
  }

  const loadTrack = async () => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase
        .from('tracks')
        .select(`
          *,
          profiles:uploaded_by (
            username,
            full_name,
            avatar_url
          )
        `)
        .eq('id', trackId)
        .single()

      if (error) {
        throw error
      }

      if (!data) {
        setError('Track not found')
        return
      }

      // Check if track is public or user owns it
      if (!data.is_public && data.uploaded_by !== currentUser?.id) {
        setError('This track is private')
        return
      }

      setTrack(data)
      
      // Increment play count
      await incrementPlayCount()

    } catch (err) {
      console.error('Error loading track:', err)
      setError(err instanceof Error ? err.message : 'Failed to load track')
    } finally {
      setLoading(false)
    }
  }

  const checkIfLiked = async (userId: string) => {
    try {
      const { data } = await supabase
        .from('track_likes')
        .select('id')
        .eq('user_id', userId)
        .eq('track_id', trackId)
        .single()

      setIsLiked(!!data)
    } catch (err) {
      // User hasn't liked this track
      setIsLiked(false)
    }
  }

  const incrementPlayCount = async () => {
    try {
      await supabase
        .from('tracks')
        .update({ 
          play_count: (track?.play_count || 0) + 1 
        })
        .eq('id', trackId)
    } catch (err) {
      console.error('Error incrementing play count:', err)
    }
  }

  const toggleLike = async () => {
    if (!currentUser) {
      router.push('/auth/login')
      return
    }

    try {
      if (isLiked) {
        // Remove like
        await supabase
          .from('track_likes')
          .delete()
          .eq('user_id', currentUser.id)
          .eq('track_id', trackId)

        setIsLiked(false)
        setTrack(prev => prev ? { ...prev, like_count: Math.max(0, prev.like_count - 1) } : null)
      } else {
        // Add like
        await supabase
          .from('track_likes')
          .insert({
            user_id: currentUser.id,
            track_id: trackId
          })

        setIsLiked(true)
        setTrack(prev => prev ? { ...prev, like_count: prev.like_count + 1 } : null)
      }
    } catch (err) {
      console.error('Error toggling like:', err)
    }
  }

  const shareTrack = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: track?.title,
          text: `Check out "${track?.title}" by ${track?.artist_name}`,
          url: window.location.href
        })
      } catch (err) {
        // Fallback to clipboard
        copyToClipboard()
      }
    } else {
      copyToClipboard()
    }
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(window.location.href)
    // TODO: Show toast notification
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <HeadphonesIcon className="w-16 h-16 text-purple-400 animate-pulse mx-auto mb-4" />
            <p className="text-gray-300">Loading track...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (error || !track) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">❌</span>
            </div>
            <h2 className="text-xl font-bold text-white mb-2">Track Not Found</h2>
            <p className="text-gray-400 mb-4">{error}</p>
            <button
              onClick={() => router.push('/browse')}
              className="btn-primary"
            >
              Browse Music
            </button>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Track Header */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Track Artwork & Player */}
          <div className="lg:col-span-1">
            <div className="aspect-square bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
              <div className="text-center text-white">
                <HeadphonesIcon className="w-20 h-20 mx-auto mb-4 opacity-80" />
                <p className="text-sm opacity-60">AI Generated</p>
              </div>
            </div>

            {/* Audio Player - Centered Play Button */}
            {track.file_url && (
              <div className="bg-gray-800 rounded-xl p-8">
                {/* Centered Large Play Button */}
                {isClient && getAudioTrack() && (
                  <div className="flex justify-center mb-6">
                    <PlayButton
                      track={getAudioTrack()!}
                      size="lg"
                      variant="primary"
                      showQueue={false}
                      className="w-20 h-20"
                      onPlay={() => setIsPlaying(true)}
                      onPause={() => setIsPlaying(false)}
                    />
                  </div>
                )}

                <div className="flex gap-2">
                  <button
                    onClick={toggleLike}
                    className={`btn-secondary flex-1 flex items-center justify-center gap-2 ${
                      isLiked ? 'bg-red-600 hover:bg-red-700' : ''
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
                    {track.like_count}
                  </button>
                  <button
                    onClick={shareTrack}
                    className="btn-secondary flex-1 flex items-center justify-center gap-2"
                  >
                    <Share2 className="w-4 h-4" />
                    Share
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Track Info */}
          <div className="lg:col-span-2">
            <div className="mb-6">
              <h1 className="text-4xl font-bold text-white mb-2">{track.title}</h1>
              <p className="text-xl text-gray-300 mb-4">by {track.artist_name}</p>
              
              {track.ai_prompt && (
                <div className="bg-gray-800 rounded-lg p-4 mb-4">
                  <h3 className="text-sm font-medium text-gray-400 mb-2">AI Prompt</h3>
                  <p className="text-gray-300">{track.ai_prompt}</p>
                </div>
              )}
            </div>

            {/* Track Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center gap-2 text-purple-400 mb-1">
                  <Eye className="w-4 h-4" />
                  <span className="text-xs font-medium">PLAYS</span>
                </div>
                <p className="text-xl font-bold text-white">{track.play_count.toLocaleString()}</p>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center gap-2 text-red-400 mb-1">
                  <Heart className="w-4 h-4" />
                  <span className="text-xs font-medium">LIKES</span>
                </div>
                <p className="text-xl font-bold text-white">{track.like_count.toLocaleString()}</p>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center gap-2 text-blue-400 mb-1">
                  <Clock className="w-4 h-4" />
                  <span className="text-xs font-medium">DURATION</span>
                </div>
                <p className="text-xl font-bold text-white">{formatDuration(track.duration)}</p>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center gap-2 text-green-400 mb-1">
                  <Calendar className="w-4 h-4" />
                  <span className="text-xs font-medium">UPLOADED</span>
                </div>
                <p className="text-sm font-bold text-white">{formatDate(track.created_at)}</p>
              </div>
            </div>

            {/* Artist Info */}
            {track.profiles && (
              <div className="bg-gray-800 rounded-lg p-4 mb-6">
                <h3 className="text-sm font-medium text-gray-400 mb-3">ARTIST</h3>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                    {track.profiles.avatar_url ? (
                      <img 
                        src={track.profiles.avatar_url} 
                        alt="Artist"
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-6 h-6 text-white" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-white">
                      {track.profiles.full_name || track.profiles.username || 'Anonymous'}
                    </p>
                    <p className="text-sm text-gray-400">@{track.profiles.username || 'anonymous'}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Track Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* AI Generation Details */}
          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-400" />
              AI Generation
            </h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-400">AI Tool</p>
                <p className="text-white font-medium">{getAIToolDisplayName(track.ai_tool)}</p>
              </div>
              {track.genre && (
                <div>
                  <p className="text-sm text-gray-400">Genre</p>
                  <p className="text-white font-medium">{formatGenre(track.genre)}</p>
                </div>
              )}
              {track.mood && (
                <div>
                  <p className="text-sm text-gray-400">Mood</p>
                  <p className="text-white font-medium">{formatGenre(track.mood)}</p>
                </div>
              )}
            </div>
          </div>

          {/* Technical Details */}
          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-lg font-bold text-white mb-4">Technical Details</h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-400">File Size</p>
                <p className="text-white font-medium">
                  {track.file_size ? `${(track.file_size / (1024 * 1024)).toFixed(1)} MB` : 'Unknown'}
                </p>
              </div>
              {track.ai_parameters?.audioMetadata?.bitrate && (
                <div>
                  <p className="text-sm text-gray-400">Bitrate</p>
                  <p className="text-white font-medium">{Math.round(track.ai_parameters.audioMetadata.bitrate)} kbps</p>
                </div>
              )}
              {track.ai_parameters?.audioMetadata?.sampleRate && (
                <div>
                  <p className="text-sm text-gray-400">Sample Rate</p>
                  <p className="text-white font-medium">{(track.ai_parameters.audioMetadata.sampleRate / 1000).toFixed(1)} kHz</p>
                </div>
              )}
              {track.ai_parameters?.audioMetadata?.format && (
                <div>
                  <p className="text-sm text-gray-400">Format</p>
                  <p className="text-white font-medium">{track.ai_parameters.audioMetadata.format.toUpperCase()}</p>
                </div>
              )}
              {track.tempo && (
                <div>
                  <p className="text-sm text-gray-400">Tempo</p>
                  <p className="text-white font-medium">{track.tempo} BPM</p>
                </div>
              )}
              {track.key_signature && (
                <div>
                  <p className="text-sm text-gray-400">Key</p>
                  <p className="text-white font-medium">{track.key_signature}</p>
                </div>
              )}
              {track.ai_parameters?.audioMetadata?.year && (
                <div>
                  <p className="text-sm text-gray-400">Year</p>
                  <p className="text-white font-medium">{track.ai_parameters.audioMetadata.year}</p>
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          {track.ai_parameters?.tags && (
            <div className="bg-gray-800 rounded-xl p-6">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <Tag className="w-5 h-5 text-blue-400" />
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {track.ai_parameters.tags.map((tag: string, index: number) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-blue-600/20 border border-blue-500/30 rounded-full text-xs text-blue-300"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
} 