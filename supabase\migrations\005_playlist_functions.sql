-- Enhanced Playlist Database Functions
-- Migration: 005_playlist_functions.sql

-- Function to efficiently reorder playlist tracks
CREATE OR REPLACE FUNCTION reorder_playlist_tracks(
  p_playlist_id UUID,
  p_track_positions JSONB
) RETURNS VOID AS $$
DECLARE
  track_data JSONB;
BEGIN
  -- Validate playlist ownership
  IF NOT EXISTS (
    SELECT 1 FROM playlists 
    WHERE id = p_playlist_id AND user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Unauthorized: You do not own this playlist';
  END IF;

  -- Update positions for each track
  FOR track_data IN SELECT * FROM jsonb_array_elements(p_track_positions)
  LOOP
    UPDATE playlist_tracks 
    SET position = (track_data->>'position')::INTEGER
    WHERE playlist_id = p_playlist_id 
    AND track_id = (track_data->>'track_id')::UUID;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add multiple tracks to playlist
CREATE OR REPLACE FUNCTION add_tracks_to_playlist(
  p_playlist_id UUID,
  p_track_ids UUID[],
  p_start_position INTEGER DEFAULT NULL
) RETURNS TABLE(track_id UUID, position INTEGER) AS $$
DECLARE
  current_max_position INTEGER;
  track_id UUID;
  current_position INTEGER;
BEGIN
  -- Validate playlist ownership
  IF NOT EXISTS (
    SELECT 1 FROM playlists 
    WHERE id = p_playlist_id AND user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Unauthorized: You do not own this playlist';
  END IF;

  -- Get current max position
  SELECT COALESCE(MAX(position), 0) INTO current_max_position
  FROM playlist_tracks 
  WHERE playlist_id = p_playlist_id;

  -- Set starting position
  current_position := COALESCE(p_start_position, current_max_position + 1);

  -- Insert tracks
  FOREACH track_id IN ARRAY p_track_ids
  LOOP
    -- Skip if track already exists in playlist
    IF NOT EXISTS (
      SELECT 1 FROM playlist_tracks 
      WHERE playlist_id = p_playlist_id AND track_id = track_id
    ) THEN
      INSERT INTO playlist_tracks (playlist_id, track_id, position)
      VALUES (p_playlist_id, track_id, current_position);
      
      RETURN QUERY SELECT track_id, current_position;
      current_position := current_position + 1;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove multiple tracks from playlist
CREATE OR REPLACE FUNCTION remove_tracks_from_playlist(
  p_playlist_id UUID,
  p_track_ids UUID[]
) RETURNS INTEGER AS $$
DECLARE
  removed_count INTEGER;
BEGIN
  -- Validate playlist ownership
  IF NOT EXISTS (
    SELECT 1 FROM playlists 
    WHERE id = p_playlist_id AND user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Unauthorized: You do not own this playlist';
  END IF;

  -- Remove tracks
  DELETE FROM playlist_tracks 
  WHERE playlist_id = p_playlist_id 
  AND track_id = ANY(p_track_ids);
  
  GET DIAGNOSTICS removed_count = ROW_COUNT;

  -- Reorder remaining tracks to fill gaps
  WITH ordered_tracks AS (
    SELECT track_id, ROW_NUMBER() OVER (ORDER BY position) as new_position
    FROM playlist_tracks 
    WHERE playlist_id = p_playlist_id
  )
  UPDATE playlist_tracks 
  SET position = ordered_tracks.new_position
  FROM ordered_tracks
  WHERE playlist_tracks.playlist_id = p_playlist_id 
  AND playlist_tracks.track_id = ordered_tracks.track_id;

  RETURN removed_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to duplicate playlist
CREATE OR REPLACE FUNCTION duplicate_playlist(
  p_source_playlist_id UUID,
  p_new_name TEXT,
  p_new_description TEXT DEFAULT NULL,
  p_is_public BOOLEAN DEFAULT FALSE
) RETURNS UUID AS $$
DECLARE
  new_playlist_id UUID;
  source_user_id UUID;
BEGIN
  -- Get source playlist info and validate access
  SELECT user_id INTO source_user_id
  FROM playlists 
  WHERE id = p_source_playlist_id 
  AND (user_id = auth.uid() OR is_public = TRUE);

  IF source_user_id IS NULL THEN
    RAISE EXCEPTION 'Playlist not found or access denied';
  END IF;

  -- Create new playlist
  INSERT INTO playlists (user_id, name, description, is_public)
  VALUES (auth.uid(), p_new_name, p_new_description, p_is_public)
  RETURNING id INTO new_playlist_id;

  -- Copy tracks
  INSERT INTO playlist_tracks (playlist_id, track_id, position)
  SELECT new_playlist_id, track_id, position
  FROM playlist_tracks 
  WHERE playlist_id = p_source_playlist_id
  ORDER BY position;

  RETURN new_playlist_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get playlist with tracks efficiently
CREATE OR REPLACE FUNCTION get_playlist_with_tracks(p_playlist_id UUID)
RETURNS TABLE(
  playlist_id UUID,
  playlist_name TEXT,
  playlist_description TEXT,
  playlist_is_public BOOLEAN,
  playlist_cover_image_url TEXT,
  playlist_created_at TIMESTAMPTZ,
  playlist_updated_at TIMESTAMPTZ,
  playlist_user_id UUID,
  track_id UUID,
  track_title TEXT,
  track_artist TEXT,
  track_album TEXT,
  track_duration INTEGER,
  track_file_url TEXT,
  track_cover_image_url TEXT,
  track_genre TEXT,
  position INTEGER,
  added_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Validate access
  IF NOT EXISTS (
    SELECT 1 FROM playlists 
    WHERE id = p_playlist_id 
    AND (user_id = auth.uid() OR is_public = TRUE)
  ) THEN
    RAISE EXCEPTION 'Playlist not found or access denied';
  END IF;

  RETURN QUERY
  SELECT 
    p.id as playlist_id,
    p.name as playlist_name,
    p.description as playlist_description,
    p.is_public as playlist_is_public,
    p.cover_image_url as playlist_cover_image_url,
    p.created_at as playlist_created_at,
    p.updated_at as playlist_updated_at,
    p.user_id as playlist_user_id,
    t.id as track_id,
    t.title as track_title,
    t.artist as track_artist,
    t.album as track_album,
    t.duration as track_duration,
    t.file_url as track_file_url,
    t.cover_image_url as track_cover_image_url,
    t.genre as track_genre,
    pt.position,
    pt.added_at
  FROM playlists p
  LEFT JOIN playlist_tracks pt ON p.id = pt.playlist_id
  LEFT JOIN tracks t ON pt.track_id = t.id
  WHERE p.id = p_playlist_id
  ORDER BY pt.position NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user playlists with stats
CREATE OR REPLACE FUNCTION get_user_playlists_with_stats(p_user_id UUID DEFAULT NULL)
RETURNS TABLE(
  id UUID,
  name TEXT,
  description TEXT,
  is_public BOOLEAN,
  cover_image_url TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  track_count BIGINT,
  total_duration BIGINT
) AS $$
DECLARE
  target_user_id UUID;
BEGIN
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;

  RETURN QUERY
  SELECT 
    p.id,
    p.name,
    p.description,
    p.is_public,
    p.cover_image_url,
    p.created_at,
    p.updated_at,
    COUNT(pt.track_id) as track_count,
    COALESCE(SUM(t.duration), 0) as total_duration
  FROM playlists p
  LEFT JOIN playlist_tracks pt ON p.id = pt.playlist_id
  LEFT JOIN tracks t ON pt.track_id = t.id
  WHERE p.user_id = target_user_id
  GROUP BY p.id, p.name, p.description, p.is_public, p.cover_image_url, p.created_at, p.updated_at
  ORDER BY p.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search public playlists
CREATE OR REPLACE FUNCTION search_public_playlists(
  p_search_term TEXT DEFAULT NULL,
  p_limit INTEGER DEFAULT 20,
  p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
  id UUID,
  name TEXT,
  description TEXT,
  cover_image_url TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_id UUID,
  track_count BIGINT,
  total_duration BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.name,
    p.description,
    p.cover_image_url,
    p.created_at,
    p.updated_at,
    p.user_id,
    COUNT(pt.track_id) as track_count,
    COALESCE(SUM(t.duration), 0) as total_duration
  FROM playlists p
  LEFT JOIN playlist_tracks pt ON p.id = pt.playlist_id
  LEFT JOIN tracks t ON pt.track_id = t.id
  WHERE p.is_public = TRUE
  AND (p_search_term IS NULL OR p.name ILIKE '%' || p_search_term || '%' OR p.description ILIKE '%' || p_search_term || '%')
  GROUP BY p.id, p.name, p.description, p.cover_image_url, p.created_at, p.updated_at, p.user_id
  ORDER BY p.updated_at DESC
  LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can modify playlist
CREATE OR REPLACE FUNCTION can_modify_playlist(p_playlist_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM playlists 
    WHERE id = p_playlist_id AND user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get playlists containing a track
CREATE OR REPLACE FUNCTION get_playlists_with_track(p_track_id UUID)
RETURNS TABLE(
  id UUID,
  name TEXT,
  description TEXT,
  is_public BOOLEAN,
  cover_image_url TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.name,
    p.description,
    p.is_public,
    p.cover_image_url
  FROM playlists p
  INNER JOIN playlist_tracks pt ON p.id = pt.playlist_id
  WHERE pt.track_id = p_track_id 
  AND p.user_id = auth.uid()
  ORDER BY p.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION reorder_playlist_tracks TO authenticated;
GRANT EXECUTE ON FUNCTION add_tracks_to_playlist TO authenticated;
GRANT EXECUTE ON FUNCTION remove_tracks_from_playlist TO authenticated;
GRANT EXECUTE ON FUNCTION duplicate_playlist TO authenticated;
GRANT EXECUTE ON FUNCTION get_playlist_with_tracks TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_playlists_with_stats TO authenticated;
GRANT EXECUTE ON FUNCTION search_public_playlists TO authenticated;
GRANT EXECUTE ON FUNCTION can_modify_playlist TO authenticated;
GRANT EXECUTE ON FUNCTION get_playlists_with_track TO authenticated; 