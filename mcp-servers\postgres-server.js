#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// Create MCP server for Tunami PostgreSQL operations
const server = new McpServer({
  name: "tunami-postgres",
  version: "1.0.0"
});

// Database connection configuration
const DB_CONFIG = {
  host: process.env.SUPABASE_DB_HOST || 'localhost',
  port: process.env.SUPABASE_DB_PORT || 5432,
  database: process.env.SUPABASE_DB_NAME || 'postgres',
  user: process.env.SUPABASE_DB_USER || 'postgres',
  password: process.env.SUPABASE_DB_PASSWORD || '',
  ssl: process.env.SUPABASE_DB_SSL === 'true'
};

// Mock database connection for demonstration
// In a real implementation, you would use a PostgreSQL client like 'pg'
class MockPostgresClient {
  async query(sql, params = []) {
    // This is a mock implementation
    // In reality, you would connect to your Supabase PostgreSQL database
    console.log(`Executing query: ${sql}`, params);
    
    // Mock responses for common Tunami queries
    if (sql.includes('information_schema.tables')) {
      return {
        rows: [
          { table_name: 'users', table_type: 'BASE TABLE' },
          { table_name: 'tracks', table_type: 'BASE TABLE' },
          { table_name: 'playlists', table_type: 'BASE TABLE' },
          { table_name: 'user_tracks', table_type: 'BASE TABLE' },
          { table_name: 'upload_history', table_type: 'BASE TABLE' }
        ]
      };
    }
    
    if (sql.includes('information_schema.columns')) {
      return {
        rows: [
          { column_name: 'id', data_type: 'uuid', is_nullable: 'NO' },
          { column_name: 'title', data_type: 'text', is_nullable: 'YES' },
          { column_name: 'artist', data_type: 'text', is_nullable: 'YES' },
          { column_name: 'duration', data_type: 'integer', is_nullable: 'YES' },
          { column_name: 'created_at', data_type: 'timestamp with time zone', is_nullable: 'YES' }
        ]
      };
    }
    
    if (sql.includes('SELECT') && sql.includes('tracks')) {
      return {
        rows: [
          { id: '1', title: 'Demo Track 1', artist: 'Demo Artist', duration: 180 },
          { id: '2', title: 'Demo Track 2', artist: 'Another Artist', duration: 240 }
        ]
      };
    }
    
    return { rows: [] };
  }
  
  async end() {
    console.log('Database connection closed');
  }
}

// List tables tool
server.tool(
  "list_tables",
  {
    schema: z.string().default("public").describe("Database schema name")
  },
  async ({ schema = "public" }) => {
    const client = new MockPostgresClient();
    
    try {
      const query = `
        SELECT table_name, table_type 
        FROM information_schema.tables 
        WHERE table_schema = $1 
        ORDER BY table_name
      `;
      
      const result = await client.query(query, [schema]);
      
      const tableList = result.rows.map(row => 
        `${row.table_name} (${row.table_type})`
      ).join('\n');
      
      return {
        content: [{ 
          type: "text", 
          text: `Tables in schema '${schema}':\n\n${tableList}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error listing tables: ${error.message}` 
        }],
        isError: true
      };
    } finally {
      await client.end();
    }
  }
);

// Describe table tool
server.tool(
  "describe_table",
  {
    table_name: z.string().describe("Table name to describe"),
    schema: z.string().default("public").describe("Database schema name")
  },
  async ({ table_name, schema = "public" }) => {
    const client = new MockPostgresClient();
    
    try {
      const query = `
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length
        FROM information_schema.columns 
        WHERE table_schema = $1 AND table_name = $2
        ORDER BY ordinal_position
      `;
      
      const result = await client.query(query, [schema, table_name]);
      
      if (result.rows.length === 0) {
        return {
          content: [{ 
            type: "text", 
            text: `Table '${table_name}' not found in schema '${schema}'` 
          }]
        };
      }
      
      const columnInfo = result.rows.map(row => {
        let info = `${row.column_name}: ${row.data_type}`;
        if (row.character_maximum_length) {
          info += `(${row.character_maximum_length})`;
        }
        info += ` ${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`;
        if (row.column_default) {
          info += ` DEFAULT ${row.column_default}`;
        }
        return info;
      }).join('\n');
      
      return {
        content: [{ 
          type: "text", 
          text: `Table structure for '${schema}.${table_name}':\n\n${columnInfo}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error describing table: ${error.message}` 
        }],
        isError: true
      };
    } finally {
      await client.end();
    }
  }
);

// Execute query tool (read-only)
server.tool(
  "execute_query",
  {
    query: z.string().describe("SQL query to execute (SELECT only)"),
    limit: z.number().default(100).describe("Maximum number of rows to return")
  },
  async ({ query, limit = 100 }) => {
    // Security check: only allow SELECT queries
    const trimmedQuery = query.trim().toUpperCase();
    if (!trimmedQuery.startsWith('SELECT')) {
      return {
        content: [{ 
          type: "text", 
          text: `Error: Only SELECT queries are allowed for security reasons` 
        }],
        isError: true
      };
    }
    
    const client = new MockPostgresClient();
    
    try {
      // Add LIMIT clause if not present
      let finalQuery = query;
      if (!query.toUpperCase().includes('LIMIT')) {
        finalQuery += ` LIMIT ${limit}`;
      }
      
      const result = await client.query(finalQuery);
      
      if (result.rows.length === 0) {
        return {
          content: [{ 
            type: "text", 
            text: `Query executed successfully. No rows returned.` 
          }]
        };
      }
      
      // Format results as table
      const headers = Object.keys(result.rows[0]);
      const headerRow = headers.join(' | ');
      const separatorRow = headers.map(() => '---').join(' | ');
      const dataRows = result.rows.map(row => 
        headers.map(header => row[header] || '').join(' | ')
      );
      
      const table = [headerRow, separatorRow, ...dataRows].join('\n');
      
      return {
        content: [{ 
          type: "text", 
          text: `Query Results (${result.rows.length} rows):\n\n${table}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error executing query: ${error.message}` 
        }],
        isError: true
      };
    } finally {
      await client.end();
    }
  }
);

// Get table stats tool
server.tool(
  "get_table_stats",
  {
    table_name: z.string().describe("Table name to get statistics for"),
    schema: z.string().default("public").describe("Database schema name")
  },
  async ({ table_name, schema = "public" }) => {
    const client = new MockPostgresClient();
    
    try {
      // Mock statistics for demonstration
      const stats = {
        table_name: table_name,
        schema: schema,
        estimated_rows: Math.floor(Math.random() * 10000),
        table_size: `${Math.floor(Math.random() * 100)}MB`,
        index_size: `${Math.floor(Math.random() * 20)}MB`,
        last_vacuum: new Date().toISOString(),
        last_analyze: new Date().toISOString()
      };
      
      return {
        content: [{ 
          type: "text", 
          text: `Statistics for '${schema}.${table_name}':\n\n${JSON.stringify(stats, null, 2)}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error getting table statistics: ${error.message}` 
        }],
        isError: true
      };
    } finally {
      await client.end();
    }
  }
);

// Search tables tool
server.tool(
  "search_tables",
  {
    pattern: z.string().describe("Pattern to search for in table names"),
    schema: z.string().default("public").describe("Database schema name")
  },
  async ({ pattern, schema = "public" }) => {
    const client = new MockPostgresClient();
    
    try {
      const query = `
        SELECT table_name, table_type 
        FROM information_schema.tables 
        WHERE table_schema = $1 AND table_name ILIKE $2
        ORDER BY table_name
      `;
      
      const result = await client.query(query, [schema, `%${pattern}%`]);
      
      if (result.rows.length === 0) {
        return {
          content: [{ 
            type: "text", 
            text: `No tables found matching pattern '${pattern}' in schema '${schema}'` 
          }]
        };
      }
      
      const tableList = result.rows.map(row => 
        `${row.table_name} (${row.table_type})`
      ).join('\n');
      
      return {
        content: [{ 
          type: "text", 
          text: `Tables matching '${pattern}' in schema '${schema}':\n\n${tableList}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error searching tables: ${error.message}` 
        }],
        isError: true
      };
    } finally {
      await client.end();
    }
  }
);

// Get database info tool
server.tool(
  "get_database_info",
  {},
  async () => {
    const client = new MockPostgresClient();
    
    try {
      // Mock database information
      const dbInfo = {
        database_name: DB_CONFIG.database,
        host: DB_CONFIG.host,
        port: DB_CONFIG.port,
        version: "PostgreSQL 15.0 (Supabase)",
        current_user: DB_CONFIG.user,
        current_schema: "public",
        connection_status: "Connected",
        timezone: "UTC"
      };
      
      return {
        content: [{ 
          type: "text", 
          text: `Database Information:\n\n${JSON.stringify(dbInfo, null, 2)}` 
        }]
      };
    } catch (error) {
      return {
        content: [{ 
          type: "text", 
          text: `Error getting database info: ${error.message}` 
        }],
        isError: true
      };
    } finally {
      await client.end();
    }
  }
);

// Start the server
const transport = new StdioServerTransport();
await server.connect(transport);

console.error("Tunami PostgreSQL MCP Server running on stdio"); 