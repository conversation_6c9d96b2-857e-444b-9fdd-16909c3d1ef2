'use client'

import React, { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { usePlaylist } from '@/contexts/PlaylistContext'
import PlaylistList from '@/components/playlists/PlaylistList'

export default function PlaylistsPage() {
  const { user } = useAuth()
  const { playlists, getUserPlaylists, loading, error } = usePlaylist()

  useEffect(() => {
    if (user) {
      getUserPlaylists()
    }
  }, [user, getUserPlaylists])

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Please log in to view your playlists
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            You need to be logged in to create and manage playlists.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <PlaylistList
          playlists={playlists}
          title="My Playlists"
          showCreateButton={true}
          showOwner={false}
          emptyMessage="You haven't created any playlists yet"
        />
      </div>
    </div>
  )
} 