# Test info

- Name: Audio Performance Tests >> should handle multiple audio sources efficiently
- Location: D:\Vinod\Work\Cursor_Projects\Tunami\tests\audio-compatibility.spec.ts:289:7

# Error details

```
Error: expect(received).toBe<PERSON>reater<PERSON>han(expected)

Expected: > 0
Received:   0
    at D:\Vinod\Work\Cursor_Projects\Tunami\tests\audio-compatibility.spec.ts:322:41
```

# Test source

```ts
  222 |     })
  223 |     
  224 |     if (!hasAudio) {
  225 |       test.skip('No audio elements found for state testing')
  226 |     }
  227 |     
  228 |     // Test would involve playing audio, navigating, and checking state
  229 |     console.log('Audio state persistence test - implementation depends on app structure')
  230 |   })
  231 | })
  232 |
  233 | // Helper function to enable audio interaction
  234 | async function enableAudioIfNeeded(page: Page) {
  235 |   try {
  236 |     // Look for any audio interaction prompts or buttons
  237 |     const audioEnableButton = page.locator('button:has-text("Enable Audio")')
  238 |     if (await audioEnableButton.isVisible({ timeout: 2000 })) {
  239 |       await audioEnableButton.click()
  240 |     }
  241 |     
  242 |     // Simulate a click to enable audio context (for autoplay policies)
  243 |     await page.click('body')
  244 |     
  245 |     // Wait for any audio initialization
  246 |     await page.waitForTimeout(500)
  247 |   } catch (error) {
  248 |     // Ignore errors if no interaction is needed
  249 |     console.log('No audio interaction needed or failed:', error.message)
  250 |   }
  251 | }
  252 |
  253 | // Performance test for audio loading
  254 | test.describe('Audio Performance Tests', () => {
  255 |   test('should load audio files efficiently', async ({ page }) => {
  256 |     // Test audio loading performance
  257 |     const loadingPerformance = await page.evaluate(async () => {
  258 |       const start = performance.now()
  259 |       
  260 |       const audio = new Audio()
  261 |       audio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA'
  262 |       
  263 |       return new Promise((resolve, reject) => {
  264 |         audio.oncanplay = () => {
  265 |           resolve({
  266 |             loadTime: performance.now() - start,
  267 |             duration: audio.duration || 0,
  268 |             readyState: audio.readyState
  269 |           })
  270 |         }
  271 |         
  272 |         audio.onerror = () => {
  273 |           reject(new Error('Audio loading failed'))
  274 |         }
  275 |         
  276 |         // Set timeout for loading
  277 |         setTimeout(() => {
  278 |           reject(new Error('Audio loading timeout'))
  279 |         }, 5000)
  280 |         
  281 |         audio.load()
  282 |       })
  283 |     })
  284 |
  285 |     expect(loadingPerformance.loadTime).toBeLessThan(1000) // Should load within 1 second
  286 |     expect(loadingPerformance.readyState).toBeGreaterThanOrEqual(3) // HAVE_FUTURE_DATA or better
  287 |   })
  288 |
  289 |   test('should handle multiple audio sources efficiently', async ({ page }) => {
  290 |     // Test concurrent audio loading
  291 |     const concurrentTest = await page.evaluate(async () => {
  292 |       const sources = [
  293 |         'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEA',
  294 |         'data:audio/mpeg;base64,//tQxAAAAAAAAAAAAAAAAAAA',
  295 |         'data:audio/ogg;base64,T2dnUwACAAAAAAAAAAA='
  296 |       ]
  297 |       
  298 |       const start = performance.now()
  299 |       
  300 |       const loadPromises = sources.map(src => {
  301 |         const audio = new Audio()
  302 |         audio.src = src
  303 |         
  304 |         return new Promise((resolve, reject) => {
  305 |           audio.oncanplay = () => resolve(true)
  306 |           audio.onerror = () => resolve(false) // Don't reject, just mark as failed
  307 |           setTimeout(() => resolve(false), 2000) // Timeout after 2 seconds
  308 |           audio.load()
  309 |         })
  310 |       })
  311 |       
  312 |       const results = await Promise.all(loadPromises)
  313 |       
  314 |       return {
  315 |         totalTime: performance.now() - start,
  316 |         successCount: results.filter(Boolean).length,
  317 |         totalSources: sources.length
  318 |       }
  319 |     })
  320 |
  321 |     expect(concurrentTest.totalTime).toBeLessThan(3000) // Should complete within 3 seconds
> 322 |     expect(concurrentTest.successCount).toBeGreaterThan(0) // At least one should succeed
      |                                         ^ Error: expect(received).toBeGreaterThan(expected)
  323 |   })
  324 | }) 
```