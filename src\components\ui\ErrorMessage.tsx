import React from 'react'
import { AlertTriangle, RefreshCw, X, Info, AlertCircle, CheckCircle } from 'lucide-react'

export type ErrorSeverity = 'info' | 'warning' | 'error' | 'success'

interface ErrorMessageProps {
  severity?: ErrorSeverity
  title?: string
  message: string
  details?: string
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
  showIcon?: boolean
  retryLabel?: string
  dismissible?: boolean
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  severity = 'error',
  title,
  message,
  details,
  onRetry,
  onDismiss,
  className = '',
  showIcon = true,
  retryLabel = 'Try Again',
  dismissible = true
}) => {
  const getConfig = () => {
    switch (severity) {
      case 'success':
        return {
          bgColor: 'bg-green-500/10',
          borderColor: 'border-green-500/20',
          textColor: 'text-green-400',
          icon: CheckCircle
        }
      case 'warning':
        return {
          bgColor: 'bg-yellow-500/10',
          borderColor: 'border-yellow-500/20',
          textColor: 'text-yellow-400',
          icon: AlertTriangle
        }
      case 'info':
        return {
          bgColor: 'bg-blue-500/10',
          borderColor: 'border-blue-500/20',
          textColor: 'text-blue-400',
          icon: Info
        }
      default:
        return {
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500/20',
          textColor: 'text-red-400',
          icon: AlertCircle
        }
    }
  }

  const config = getConfig()
  const Icon = config.icon

  return (
    <div 
      className={`${config.bgColor} ${config.borderColor} border rounded-lg p-4 ${className}`}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start gap-3">
        {showIcon && (
          <Icon className={`w-5 h-5 ${config.textColor} flex-shrink-0 mt-0.5`} />
        )}
        
        <div className="flex-1 min-w-0">
          {title && (
            <h4 className={`${config.textColor} font-medium mb-1`}>
              {title}
            </h4>
          )}
          
          <p className={`${config.textColor} text-sm`}>
            {message}
          </p>
          
          {details && (
            <details className="mt-2">
              <summary className={`${config.textColor} text-xs cursor-pointer hover:opacity-80`}>
                Show details
              </summary>
              <div className={`mt-1 text-xs ${config.textColor} opacity-80 font-mono bg-black/20 p-2 rounded`}>
                {details}
              </div>
            </details>
          )}
          
          {onRetry && (
            <button
              onClick={onRetry}
              className={`mt-3 inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded transition-colors ${
                severity === 'error' 
                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                  : 'bg-gray-600 hover:bg-gray-700 text-white'
              }`}
            >
              <RefreshCw className="w-3 h-3" />
              {retryLabel}
            </button>
          )}
        </div>
        
        {dismissible && onDismiss && (
          <button
            onClick={onDismiss}
            className={`${config.textColor} hover:opacity-70 transition-opacity flex-shrink-0`}
            aria-label="Dismiss"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  )
}

// Specialized error message components
export const NetworkError: React.FC<Omit<ErrorMessageProps, 'severity' | 'title'>> = (props) => (
  <ErrorMessage
    severity="error"
    title="Connection Error"
    {...props}
    message={props.message || "Unable to connect to our servers. Please check your internet connection and try again."}
  />
)

export const ValidationError: React.FC<Omit<ErrorMessageProps, 'severity' | 'title'>> = (props) => (
  <ErrorMessage
    severity="warning"
    title="Validation Error"
    {...props}
    message={props.message || "Please check your input and try again."}
  />
)

export const SuccessMessage: React.FC<Omit<ErrorMessageProps, 'severity'>> = (props) => (
  <ErrorMessage
    severity="success"
    {...props}
  />
)

export const InfoMessage: React.FC<Omit<ErrorMessageProps, 'severity'>> = (props) => (
  <ErrorMessage
    severity="info"
    {...props}
  />
)

export default ErrorMessage 