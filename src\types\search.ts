import { Track } from './track'
import { Playlist } from './playlist'

// Search suggestion types
export interface SearchSuggestion {
  id: string
  text: string
  type: 'track' | 'artist' | 'playlist' | 'user' | 'query'
  trending?: boolean
  metadata?: {
    artist?: string
    album?: string
    genre?: string
    aiTool?: string
  }
}

// Search history
export interface SearchHistory {
  id: string
  query: string
  timestamp: Date
  results_count?: number
  category?: 'all' | 'tracks' | 'artists' | 'playlists' | 'ai'
}

// Search filters
export interface SearchFilters {
  category: 'all' | 'tracks' | 'artists' | 'playlists' | 'ai'
  genre?: string
  aiTool?: string
  dateRange?: {
    start: Date
    end: Date
  }
  duration?: {
    min: number
    max: number
  }
  quality?: 'low' | 'medium' | 'high'
}

// Search results
export interface SearchResults {
  tracks: SearchTrack[]
  artists: SearchArtist[]
  playlists: SearchPlaylist[]
  users: SearchUser[]
  totalCount: number
  hasMore: boolean
  nextCursor?: string
}

// Artist search result
export interface SearchArtist {
  id: string
  name: string
  avatar?: string
  bio?: string
  trackCount: number
  followerCount: number
  isVerified: boolean
  genres: string[]
}

// Search context for components
export interface SearchContextType {
  query: string
  results: SearchResults | null
  suggestions: SearchSuggestion[]
  searchHistory: SearchHistory[]
  filters: SearchFilters
  loading: boolean
  error: string | null
  
  // Actions
  search: (query: string, filters?: SearchFilters) => Promise<void>
  getSuggestions: (query: string) => Promise<SearchSuggestion[]>
  updateFilters: (filters: Partial<SearchFilters>) => void
  clearFilters: () => void
  saveToHistory: (query: string) => Promise<void>
  clearHistory: () => Promise<void>
  loadMore: () => Promise<void>
}

// Search analytics
export interface SearchAnalytics {
  query: string
  result_count: number
  clicked_result?: {
    type: 'track' | 'playlist' | 'artist'
    id: string
    position: number
  }
  filters_used: SearchFilters
  search_time: number // milliseconds
  user_id?: string
  created_at: string
}

// Popular searches
export interface PopularSearch {
  query: string
  search_count: number
  trend_direction: 'up' | 'down' | 'stable'
  categories: string[]
}

// Search configuration
export interface SearchConfig {
  max_suggestions: number
  max_history_items: number
  debounce_delay: number
  min_query_length: number
  results_per_page: number
  enable_analytics: boolean
  enable_autocomplete: boolean
}

// Search API request/response types
export interface SearchRequest {
  query: string
  filters?: SearchFilters
  page?: number
  per_page?: number
  include_suggestions?: boolean
}

export interface SearchResponse {
  results: SearchResults
  suggestions?: SearchSuggestion[]
  analytics_id?: string
}

// Advanced search operators
export interface SearchOperator {
  field: string
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than' | 'between'
  value: string | number | [number, number]
}

export interface AdvancedSearchQuery {
  operators: SearchOperator[]
  logic: 'AND' | 'OR'
}

// Search result item for unified display
export interface SearchResultItem {
  id: string
  type: 'track' | 'playlist' | 'artist'
  title: string
  subtitle: string
  image_url?: string
  metadata: {
    duration?: number
    track_count?: number
    ai_tool?: string
    genre?: string
    created_at?: string
    is_public?: boolean
  }
  relevance_score: number
}

export interface SearchTrack {
  id: string
  title: string
  artist: string
  album?: string
  duration: number
  genre?: string
  aiTool?: string
  coverArt?: string
  isPublic: boolean
  playCount: number
  likeCount: number
  createdAt: Date
}

export interface SearchPlaylist {
  id: string
  name: string
  description?: string
  coverImage?: string
  trackCount: number
  isPublic: boolean
  createdBy: {
    id: string
    name: string
    avatar?: string
  }
  createdAt: Date
  updatedAt: Date
}

export interface SearchUser {
  id: string
  email: string
  username?: string
  avatar?: string
  bio?: string
  trackCount: number
  playlistCount: number
  followerCount: number
  followingCount: number
  isVerified: boolean
  createdAt: Date
} 