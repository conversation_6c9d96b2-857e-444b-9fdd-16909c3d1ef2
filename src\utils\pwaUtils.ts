// PWA Utilities for Tunami
// Handles service worker registration, install prompts, and PWA features

export interface PWAInstallPrompt {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

export interface PWACapabilities {
  isInstallable: boolean
  isInstalled: boolean
  isStandalone: boolean
  supportsServiceWorker: boolean
  supportsNotifications: boolean
  supportsPushNotifications: boolean
  supportsBackgroundSync: boolean
}

// Register service worker
export async function registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    console.log('Service Worker not supported')
    return null
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/'
    })

    console.log('Service Worker registered successfully:', registration)

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New content is available
            showUpdateAvailableNotification()
          }
        })
      }
    })

    return registration
  } catch (error) {
    console.error('Service Worker registration failed:', error)
    return null
  }
}

// Check PWA capabilities
export function getPWACapabilities(): PWACapabilities {
  if (typeof window === 'undefined') {
    return {
      isInstallable: false,
      isInstalled: false,
      isStandalone: false,
      supportsServiceWorker: false,
      supportsNotifications: false,
      supportsPushNotifications: false,
      supportsBackgroundSync: false
    }
  }

  const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                      (window.navigator as any).standalone === true

  return {
    isInstallable: 'beforeinstallprompt' in window,
    isInstalled: isStandalone,
    isStandalone,
    supportsServiceWorker: 'serviceWorker' in navigator,
    supportsNotifications: 'Notification' in window,
    supportsPushNotifications: 'PushManager' in window,
    supportsBackgroundSync: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype
  }
}

// Handle install prompt
export class PWAInstallManager {
  private deferredPrompt: PWAInstallPrompt | null = null
  private installCallbacks: Array<(canInstall: boolean) => void> = []

  constructor() {
    if (typeof window !== 'undefined') {
      this.setupInstallPromptListener()
    }
  }

  private setupInstallPromptListener() {
    window.addEventListener('beforeinstallprompt', (e) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault()
      
      // Store the event so it can be triggered later
      this.deferredPrompt = e as any
      
      // Notify listeners that install is available
      this.notifyInstallAvailable(true)
      
      console.log('PWA install prompt available')
    })

    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed')
      this.deferredPrompt = null
      this.notifyInstallAvailable(false)
    })
  }

  private notifyInstallAvailable(canInstall: boolean) {
    this.installCallbacks.forEach(callback => callback(canInstall))
  }

  public onInstallAvailable(callback: (canInstall: boolean) => void) {
    this.installCallbacks.push(callback)
    
    // Immediately call with current state
    callback(this.canInstall())
    
    // Return unsubscribe function
    return () => {
      const index = this.installCallbacks.indexOf(callback)
      if (index > -1) {
        this.installCallbacks.splice(index, 1)
      }
    }
  }

  public canInstall(): boolean {
    return this.deferredPrompt !== null
  }

  public async install(): Promise<boolean> {
    if (!this.deferredPrompt) {
      console.log('No install prompt available')
      return false
    }

    try {
      // Show the install prompt
      await this.deferredPrompt.prompt()
      
      // Wait for the user to respond to the prompt
      const { outcome } = await this.deferredPrompt.userChoice
      
      console.log(`User response to install prompt: ${outcome}`)
      
      // Clear the deferredPrompt
      this.deferredPrompt = null
      this.notifyInstallAvailable(false)
      
      return outcome === 'accepted'
    } catch (error) {
      console.error('Error during PWA installation:', error)
      return false
    }
  }
}

// Singleton instance
export const pwaInstallManager = new PWAInstallManager()

// Request notification permission
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!('Notification' in window)) {
    console.log('Notifications not supported')
    return 'denied'
  }

  if (Notification.permission === 'granted') {
    return 'granted'
  }

  if (Notification.permission === 'denied') {
    return 'denied'
  }

  // Request permission
  const permission = await Notification.requestPermission()
  console.log('Notification permission:', permission)
  return permission
}

// Show local notification
export function showNotification(title: string, options?: NotificationOptions): Notification | null {
  if (!('Notification' in window) || Notification.permission !== 'granted') {
    console.log('Cannot show notification: permission not granted')
    return null
  }

  const defaultOptions: NotificationOptions = {
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    ...options
  }

  // Add vibrate pattern if supported
  if ('vibrate' in navigator) {
    (defaultOptions as any).vibrate = [100, 50, 100]
  }

  return new Notification(title, defaultOptions)
}

// Subscribe to push notifications
export async function subscribeToPushNotifications(): Promise<PushSubscription | null> {
  if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
    console.log('Push notifications not supported')
    return null
  }

  try {
    const registration = await navigator.serviceWorker.ready
    
    // Check if already subscribed
    const existingSubscription = await registration.pushManager.getSubscription()
    if (existingSubscription) {
      return existingSubscription
    }

    // Subscribe to push notifications
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
    })

    console.log('Push notification subscription:', subscription)
    
    // Send subscription to server
    await sendSubscriptionToServer(subscription)
    
    return subscription
  } catch (error) {
    console.error('Failed to subscribe to push notifications:', error)
    return null
  }
}

// Send subscription to server
async function sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
  try {
    await fetch('/api/push/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(subscription)
    })
  } catch (error) {
    console.error('Failed to send subscription to server:', error)
  }
}

// Cache audio file using service worker
export function cacheAudioFile(url: string): void {
  if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
    navigator.serviceWorker.controller.postMessage({
      type: 'CACHE_AUDIO',
      url
    })
  }
}

// Show update available notification
function showUpdateAvailableNotification(): void {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification('Tunami Update Available', {
      body: 'A new version of Tunami is available. Refresh to update.',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'app-update',
      requireInteraction: true
    })

    notification.addEventListener('click', () => {
      window.location.reload()
      notification.close()
    })
  }
}

// Check if app is running in standalone mode
export function isStandaloneMode(): boolean {
  if (typeof window === 'undefined') return false
  
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true
}

// Get install prompt guidance based on browser
export function getInstallInstructions(): { browser: string; instructions: string[] } {
  if (typeof window === 'undefined') {
    return { browser: 'unknown', instructions: [] }
  }

  const userAgent = navigator.userAgent.toLowerCase()

  if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
    return {
      browser: 'Chrome',
      instructions: [
        'Tap the menu button (⋮) in the top right',
        'Select "Add to Home screen" or "Install app"',
        'Tap "Add" to install Tunami'
      ]
    }
  }

  if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
    return {
      browser: 'Safari',
      instructions: [
        'Tap the share button (□↗) at the bottom',
        'Scroll down and tap "Add to Home Screen"',
        'Tap "Add" to install Tunami'
      ]
    }
  }

  if (userAgent.includes('firefox')) {
    return {
      browser: 'Firefox',
      instructions: [
        'Tap the menu button (⋮) in the top right',
        'Select "Add to Home screen"',
        'Tap "Add" to install Tunami'
      ]
    }
  }

  if (userAgent.includes('edg')) {
    return {
      browser: 'Edge',
      instructions: [
        'Tap the menu button (⋯) in the bottom bar',
        'Select "Add to phone"',
        'Tap "Add" to install Tunami'
      ]
    }
  }

  return {
    browser: 'Browser',
    instructions: [
      'Look for an "Add to Home Screen" or "Install" option in your browser menu',
      'Follow the prompts to install Tunami as an app'
    ]
  }
}

// Analytics for PWA usage
export function trackPWAEvent(event: string, data?: Record<string, any>): void {
  // Track PWA-specific events
  if (typeof window !== 'undefined' && 'gtag' in window) {
    (window as any).gtag('event', event, {
      event_category: 'PWA',
      ...data
    })
  }
  
  console.log('PWA Event:', event, data)
}

// Initialize PWA features
export async function initializePWA(): Promise<void> {
  if (typeof window === 'undefined') return

  try {
    // Register service worker
    await registerServiceWorker()
    
    // Track PWA capabilities
    const capabilities = getPWACapabilities()
    trackPWAEvent('pwa_capabilities_checked', capabilities)
    
    // Request notification permission if supported
    if (capabilities.supportsNotifications) {
      await requestNotificationPermission()
    }
    
    console.log('PWA initialized successfully')
  } catch (error) {
    console.error('Failed to initialize PWA:', error)
  }
} 