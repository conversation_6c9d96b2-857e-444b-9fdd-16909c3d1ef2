import { useState, useEffect, useCallback } from 'react'

interface UseInfiniteScrollProps {
  fetchMore: () => void
  hasMore: boolean
  loading: boolean
  threshold?: number
}

export function useInfiniteScroll({
  fetchMore,
  hasMore,
  loading,
  threshold = 100
}: UseInfiniteScrollProps) {
  const [isFetching, setIsFetching] = useState(false)

  const handleScroll = useCallback(() => {
    if (loading || !hasMore || isFetching) return

    const scrollTop = document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight
    const clientHeight = document.documentElement.clientHeight

    if (scrollTop + clientHeight >= scrollHeight - threshold) {
      setIsFetching(true)
      fetchMore()
    }
  }, [fetchMore, hasMore, loading, isFetching, threshold])

  useEffect(() => {
    if (!isFetching) return

    const timer = setTimeout(() => {
      setIsFetching(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [isFetching])

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  return { isFetching }
} 